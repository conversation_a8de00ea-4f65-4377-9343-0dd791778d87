# Dawinii Pharmacy System

A modern pharmacy management system built with Tauri, Vue 3, TypeScript, and TailwindCSS. Features bilingual support (Arabic/English), dark/light themes, and comprehensive pharmacy operations management.

## Features

- 🏥 Complete pharmacy management system
- 🌍 Bilingual support (Arabic/English) with RTL/LTR layouts
- 🌙 Dark/light theme support
- 📱 Responsive design with TailwindCSS
- 🔐 Device-based authentication
- 📊 Dashboard with analytics and charts
- 💊 Medication inventory management
- 🛒 Shopping cart and checkout system
- 📋 Order management and tracking
- 💰 Invoice and payment processing

## Development Setup

### Prerequisites
- Node.js 18+
- Yarn package manager
- Rust (for Tauri desktop app)

### Local Development
```bash
# Install dependencies
yarn install

# Run development server
yarn dev

# Build for production
yarn build

# Run Tauri desktop app
yarn tauri dev
```

## Docker Deployment

### Quick Start
```bash
# Build and run with Docker Compose
docker-compose up -d

# Access the application
open http://localhost:8080
```

### Manual Docker Commands
```bash
# Build the Docker image
docker build -t dawinii-pharmacy-system .

# Run the container
docker run -d -p 8080:80 --name dawinii-pharmacy dawinii-pharmacy-system

# View logs
docker logs dawinii-pharmacy

# Stop the container
docker stop dawinii-pharmacy
```

### Docker Configuration
- **Port**: 8080 (mapped to container port 80)
- **Technology**: Nginx serving built Vue.js application
- **Build**: Multi-stage build for optimized production image

## Project Structure

```
├── src/
│   ├── components/     # Vue components
│   ├── views/         # Page components
│   ├── stores/        # Pinia state management
│   ├── locales/       # i18n translations
│   ├── types/         # TypeScript definitions
│   └── utils/         # Utility functions
├── src-tauri/         # Tauri Rust backend
├── public/            # Static assets
├── Dockerfile         # Docker configuration
└── docker-compose.yml # Docker Compose setup
```

## Technology Stack

- **Frontend**: Vue 3, TypeScript, TailwindCSS
- **Desktop**: Tauri (Rust)
- **State Management**: Pinia
- **Internationalization**: Vue I18n
- **Charts**: Chart.js
- **Icons**: Heroicons
- **Deployment**: Docker, Nginx

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) + [Tauri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)
