use std::sync::Mutex;
use tauri::State;

// Simple in-memory session store
struct SessionStore {
    current_session: Option<UserSession>,
}

struct UserSession {
    user_id: String,
    role: String,
    token: String,
}

// Wrap the session store in a mutex for thread safety
struct AppState {
    session_store: Mutex<SessionStore>,
}

// Command to verify a token from QR code
#[tauri::command]
fn verify_token(token: &str, state: State<AppState>) -> Result<bool, String> {
    // In a real app, you would validate the token against a backend service
    // For this demo, we'll accept any token and extract role from it

    // Simulate token verification
    if token.is_empty() {
        return Err("Invalid token".into());
    }

    // Extract user role from token (in a real app, this would be decoded from JWT)
    // For demo, we'll assume the token contains role info
    let role = if token.len() % 2 == 0 {
        "PharmacyOwner"
    } else {
        "PharmacyUser"
    };
    let user_id = "123456"; // In a real app, this would be extracted from the token

    // Store the session
    let mut session_store = state.session_store.lock().unwrap();
    session_store.current_session = Some(UserSession {
        user_id: user_id.to_string(),
        role: role.to_string(),
        token: token.to_string(),
    });

    Ok(true)
}

// Command to check if user is logged in
#[tauri::command]
fn get_session(state: State<AppState>) -> Option<(String, String)> {
    let session_store = state.session_store.lock().unwrap();

    session_store
        .current_session
        .as_ref()
        .map(|session| (session.user_id.clone(), session.role.clone()))
}

// Command to log out
#[tauri::command]
fn logout(state: State<AppState>) -> bool {
    let mut session_store = state.session_store.lock().unwrap();
    session_store.current_session = None;
    true
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let app_state = AppState {
        session_store: Mutex::new(SessionStore {
            current_session: None,
        }),
    };

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![verify_token, get_session, logout])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
