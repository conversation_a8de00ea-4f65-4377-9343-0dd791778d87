{"name": "dawinii-pharmacy-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "chart.js": "^4.4.9", "debounce": "^2.2.0", "jsqr": "^1.4.0", "leaflet": "^1.9.4", "pinia": "^3.0.2", "qrcode": "^1.5.4", "tailwindcss": "^3.4.17", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-i18n": "9", "vue-leaflet": "^0.1.0", "vue-router": "4"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}