import { computed } from 'vue';
import { currentTheme, currentThemeMode, toggleTheme, ThemeColors } from './themeConfig';

/**
 * Composable hook for accessing theme values and functions
 */
export function useTheme() {
  // Computed property to check if dark mode is active
  const isDarkMode = computed(() => currentThemeMode.value === 'dark');
  
  // Function to get a theme value by category and key
  const getThemeValue = <K1 extends keyof ThemeColors, K2 extends keyof ThemeColors[K1]>(
    category: K1,
    key: K2
  ): string => {
    return currentTheme.value[category][key];
  };
  
  // Common theme values as computed properties
  const backgroundColor = computed(() => currentTheme.value.background.primary);
  const cardBackgroundColor = computed(() => currentTheme.value.background.card);
  const textColor = computed(() => currentTheme.value.text.primary);
  const secondaryTextColor = computed(() => currentTheme.value.text.secondary);
  const accentColor = computed(() => currentTheme.value.ui.primary);
  const borderColor = computed(() => currentTheme.value.border.primary);
  const shadowStyle = computed(() => currentTheme.value.shadow.medium);
  
  return {
    // Theme state
    isDarkMode,
    currentTheme,
    
    // Theme actions
    toggleTheme,
    
    // Theme value getters
    getThemeValue,
    
    // Common theme values
    backgroundColor,
    cardBackgroundColor,
    textColor,
    secondaryTextColor,
    accentColor,
    borderColor,
    shadowStyle,
  };
}
