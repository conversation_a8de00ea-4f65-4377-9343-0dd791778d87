import { computed, ref } from 'vue';

// Define the theme interface
export interface ThemeColors {
  // Background colors
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    card: string;
    input: string;
  };
  // Text colors
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    accent: string;
    onAccent: string;
    error: string;
  };
  // Border colors
  border: {
    primary: string;
    secondary: string;
    accent: string;
    error: string;
  };
  // UI element colors
  ui: {
    primary: string;
    primaryHover: string;
    secondary: string;
    secondaryHover: string;
    accent: string;
    accentHover: string;
    error: string;
    errorHover: string;
    success: string;
    successHover: string;
    warning: string;
    warningHover: string;
    info: string;
    infoHover: string;
  };
  // Notification colors
  notification: {
    background: string;
    backgroundUnread: string;
    backgroundHover: string;
    backgroundUnreadHover: string;
    text: string;
    title: string;
    time: string;
  };
  // Sidebar colors
  sidebar: {
    background: string;
    itemText: string;
    itemTextHover: string;
    itemBackground: string;
    itemBackgroundHover: string;
    itemBackgroundActive: string;
    itemTextActive: string;
    border: string;
  };
  // Navbar colors
  navbar: {
    background: string;
    text: string;
    border: string;
    title: string;
  };
  // Dropdown colors
  dropdown: {
    background: string;
    border: string;
    itemText: string;
    itemTextHover: string;
    itemBackground: string;
    itemBackgroundHover: string;
    itemBackgroundActive: string;
    itemTextActive: string;
    headerBorder: string;
    footerBorder: string;
  };
  // Shadow colors
  shadow: {
    light: string;
    medium: string;
    dark: string;
  };
}

// Define the theme configuration using CSS variables
export const themeConfig = {
  light: {
    // Background colors
    background: {
      primary: 'var(--background-primary)',
      secondary: 'var(--background-secondary)',
      tertiary: 'var(--background-tertiary)',
      card: 'var(--background-card)',
      input: 'var(--background-input)',
    },
    // Text colors
    text: {
      primary: 'var(--text-primary)',
      secondary: 'var(--text-secondary)',
      tertiary: 'var(--text-tertiary)',
      accent: 'var(--text-accent)',
      onAccent: 'var(--text-on-accent)',
      error: 'var(--text-error)',
    },
    // Border colors
    border: {
      primary: 'var(--border-primary)',
      secondary: 'var(--border-secondary)',
      accent: 'var(--border-accent)',
      error: 'var(--border-error)',
    },
    // UI element colors
    ui: {
      primary: 'var(--ui-primary)',
      primaryHover: 'var(--ui-primary-hover)',
      secondary: 'var(--ui-secondary)',
      secondaryHover: 'var(--ui-secondary-hover)',
      accent: 'var(--ui-accent)',
      accentHover: 'var(--ui-accent-hover)',
      error: 'var(--ui-error)',
      errorHover: 'var(--ui-error-hover)',
      success: 'var(--ui-success)',
      successHover: 'var(--ui-success-hover)',
      warning: 'var(--ui-warning)',
      warningHover: 'var(--ui-warning-hover)',
      info: 'var(--ui-info)',
      infoHover: 'var(--ui-info-hover)',
    },
    // Notification colors
    notification: {
      background: 'var(--notification-background)',
      backgroundUnread: 'var(--notification-background-unread)',
      backgroundHover: 'var(--notification-background-hover)',
      backgroundUnreadHover: 'var(--notification-background-unread-hover)',
      text: 'var(--notification-text)',
      title: 'var(--notification-title)',
      time: 'var(--notification-time)',
    },
    // Sidebar colors
    sidebar: {
      background: 'var(--sidebar-background)',
      itemText: 'var(--sidebar-item-text)',
      itemTextHover: 'var(--sidebar-item-text-hover)',
      itemBackground: 'var(--sidebar-item-background)',
      itemBackgroundHover: 'var(--sidebar-item-background-hover)',
      itemBackgroundActive: 'var(--sidebar-item-background-active)',
      itemTextActive: 'var(--sidebar-item-text-active)',
      border: 'var(--sidebar-border)',
    },
    // Navbar colors
    navbar: {
      background: 'var(--navbar-background)',
      text: 'var(--navbar-text)',
      border: 'var(--navbar-border)',
      title: 'var(--navbar-title)',
    },
    // Dropdown colors
    dropdown: {
      background: 'var(--dropdown-background)',
      border: 'var(--dropdown-border)',
      itemText: 'var(--dropdown-item-text)',
      itemTextHover: 'var(--dropdown-item-text-hover)',
      itemBackground: 'var(--dropdown-item-background)',
      itemBackgroundHover: 'var(--dropdown-item-background-hover)',
      itemBackgroundActive: 'var(--dropdown-item-background-active)',
      itemTextActive: 'var(--dropdown-item-text-active)',
      headerBorder: 'var(--dropdown-header-border)',
      footerBorder: 'var(--dropdown-footer-border)',
    },
    // Shadow colors
    shadow: {
      light: 'var(--shadow-light)',
      medium: 'var(--shadow-medium)',
      dark: 'var(--shadow-dark)',
    },
  },
  dark: {
    // We use the same CSS variables for both themes, as they are automatically updated by the .dark class
    // This keeps the structure consistent for both themes
    background: {
      primary: 'var(--background-primary)',
      secondary: 'var(--background-secondary)',
      tertiary: 'var(--background-tertiary)',
      card: 'var(--background-card)',
      input: 'var(--background-input)',
    },
    text: {
      primary: 'var(--text-primary)',
      secondary: 'var(--text-secondary)',
      tertiary: 'var(--text-tertiary)',
      accent: 'var(--text-accent)',
      onAccent: 'var(--text-on-accent)',
      error: 'var(--text-error)',
    },
    border: {
      primary: 'var(--border-primary)',
      secondary: 'var(--border-secondary)',
      accent: 'var(--border-accent)',
      error: 'var(--border-error)',
    },
    ui: {
      primary: 'var(--ui-primary)',
      primaryHover: 'var(--ui-primary-hover)',
      secondary: 'var(--ui-secondary)',
      secondaryHover: 'var(--ui-secondary-hover)',
      accent: 'var(--ui-accent)',
      accentHover: 'var(--ui-accent-hover)',
      error: 'var(--ui-error)',
      errorHover: 'var(--ui-error-hover)',
      success: 'var(--ui-success)',
      successHover: 'var(--ui-success-hover)',
      warning: 'var(--ui-warning)',
      warningHover: 'var(--ui-warning-hover)',
      info: 'var(--ui-info)',
      infoHover: 'var(--ui-info-hover)',
    },
    notification: {
      background: 'var(--notification-background)',
      backgroundUnread: 'var(--notification-background-unread)',
      backgroundHover: 'var(--notification-background-hover)',
      backgroundUnreadHover: 'var(--notification-background-unread-hover)',
      text: 'var(--notification-text)',
      title: 'var(--notification-title)',
      time: 'var(--notification-time)',
    },
    sidebar: {
      background: 'var(--sidebar-background)',
      itemText: 'var(--sidebar-item-text)',
      itemTextHover: 'var(--sidebar-item-text-hover)',
      itemBackground: 'var(--sidebar-item-background)',
      itemBackgroundHover: 'var(--sidebar-item-background-hover)',
      itemBackgroundActive: 'var(--sidebar-item-background-active)',
      itemTextActive: 'var(--sidebar-item-text-active)',
      border: 'var(--sidebar-border)',
    },
    navbar: {
      background: 'var(--navbar-background)',
      text: 'var(--navbar-text)',
      border: 'var(--navbar-border)',
      title: 'var(--navbar-title)',
    },
    dropdown: {
      background: 'var(--dropdown-background)',
      border: 'var(--dropdown-border)',
      itemText: 'var(--dropdown-item-text)',
      itemTextHover: 'var(--dropdown-item-text-hover)',
      itemBackground: 'var(--dropdown-item-background)',
      itemBackgroundHover: 'var(--dropdown-item-background-hover)',
      itemBackgroundActive: 'var(--dropdown-item-background-active)',
      itemTextActive: 'var(--dropdown-item-text-active)',
      headerBorder: 'var(--dropdown-header-border)',
      footerBorder: 'var(--dropdown-footer-border)',
    },
    shadow: {
      light: 'var(--shadow-light)',
      medium: 'var(--shadow-medium)',
      dark: 'var(--shadow-dark)',
    },
  },
};

// Create a reactive current theme mode
export const currentThemeMode = ref<'light' | 'dark'>(
  localStorage.getItem('dawinii-theme') === 'dark' ? 'dark' : 'light'
);

// Create a computed property for the current theme
export const currentTheme = computed<ThemeColors>(() => {
  return themeConfig[currentThemeMode.value];
});

// Utility function to get a theme value
export function useThemeValue<
  K1 extends keyof ThemeColors,
  K2 extends keyof ThemeColors[K1]
>(category: K1, key: K2): string {
  return currentTheme.value[category][key];
}

// Utility function to toggle theme
export function toggleTheme(): void {
  currentThemeMode.value = currentThemeMode.value === 'light' ? 'dark' : 'light';
  localStorage.setItem('dawinii-theme', currentThemeMode.value);
  console.log("currentThemeMode.value: ", currentThemeMode.value)

  // Apply theme to document
  if (currentThemeMode.value === 'dark') {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
}

// Utility function to initialize theme
export function initTheme(): void {
  // Apply theme to document
  if (currentThemeMode.value === 'dark') {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
}
