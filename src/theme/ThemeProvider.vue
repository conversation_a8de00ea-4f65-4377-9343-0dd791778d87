<script setup lang="ts">
import { provide, computed } from 'vue';
import { currentTheme, currentThemeMode, toggleTheme } from './themeConfig';

// Provide theme values to all child components
provide('theme', currentTheme);
provide('themeMode', currentThemeMode);
provide('toggleTheme', toggleTheme);

// Computed properties for commonly used theme values
const backgroundColor = computed(() => currentTheme.value.background.primary);
const textColor = computed(() => currentTheme.value.text.primary);
const accentColor = computed(() => currentTheme.value.ui.primary);

// Provide these computed properties
provide('backgroundColor', backgroundColor);
provide('textColor', textColor);
provide('accentColor', accentColor);
</script>

<template>
  <!-- This is a functional component that doesn't render anything -->
  <slot></slot>
</template>
