// Country and Currency data with ISO codes
export interface Country {
  code: string; // ISO 3166-1 alpha-2
  name: {
    en: string;
    ar: string;
  };
  flag: string; // Unicode flag emoji
  currency: string; // ISO 4217 currency code
  phoneCode: string;
}

export interface Currency {
  code: string; // ISO 4217
  name: {
    en: string;
    ar: string;
  };
  symbol: string;
  symbolPosition: 'before' | 'after'; // Symbol placement
}

export const currencies: Record<string, Currency> = {
  EGP: {
    code: 'EGP',
    name: { en: 'Egyptian Pound', ar: 'الجنيه المصري' },
    symbol: 'ج.م',
    symbolPosition: 'after'
  },
  SAR: {
    code: 'SAR',
    name: { en: 'Saudi Riyal', ar: 'الريال السعودي' },
    symbol: 'ر.س',
    symbolPosition: 'after'
  },
  AED: {
    code: 'AED',
    name: { en: 'UAE Dirham', ar: 'الدرهم الإماراتي' },
    symbol: 'د.إ',
    symbolPosition: 'after'
  },
  USD: {
    code: 'USD',
    name: { en: 'US Dollar', ar: 'الدولار الأمريكي' },
    symbol: '$',
    symbolPosition: 'before'
  },
  EUR: {
    code: 'EUR',
    name: { en: 'Euro', ar: 'اليورو' },
    symbol: '€',
    symbolPosition: 'before'
  },
  GBP: {
    code: 'GBP',
    name: { en: 'British Pound', ar: 'الجنيه الإسترليني' },
    symbol: '£',
    symbolPosition: 'before'
  },
  KWD: {
    code: 'KWD',
    name: { en: 'Kuwaiti Dinar', ar: 'الدينار الكويتي' },
    symbol: 'د.ك',
    symbolPosition: 'after'
  },
  QAR: {
    code: 'QAR',
    name: { en: 'Qatari Riyal', ar: 'الريال القطري' },
    symbol: 'ر.ق',
    symbolPosition: 'after'
  },
  BHD: {
    code: 'BHD',
    name: { en: 'Bahraini Dinar', ar: 'الدينار البحريني' },
    symbol: 'د.ب',
    symbolPosition: 'after'
  },
  OMR: {
    code: 'OMR',
    name: { en: 'Omani Rial', ar: 'الريال العماني' },
    symbol: 'ر.ع',
    symbolPosition: 'after'
  },
  JOD: {
    code: 'JOD',
    name: { en: 'Jordanian Dinar', ar: 'الدينار الأردني' },
    symbol: 'د.أ',
    symbolPosition: 'after'
  },
  LBP: {
    code: 'LBP',
    name: { en: 'Lebanese Pound', ar: 'الليرة اللبنانية' },
    symbol: 'ل.ل',
    symbolPosition: 'after'
  }
};

export const countries: Country[] = [
  {
    code: 'EG',
    name: { en: 'Egypt', ar: 'مصر' },
    flag: '🇪🇬',
    currency: 'EGP',
    phoneCode: '+20'
  },
  {
    code: 'SA',
    name: { en: 'Saudi Arabia', ar: 'السعودية' },
    flag: '🇸🇦',
    currency: 'SAR',
    phoneCode: '+966'
  },
  {
    code: 'AE',
    name: { en: 'United Arab Emirates', ar: 'الإمارات العربية المتحدة' },
    flag: '🇦🇪',
    currency: 'AED',
    phoneCode: '+971'
  },
  {
    code: 'US',
    name: { en: 'United States', ar: 'الولايات المتحدة' },
    flag: '🇺🇸',
    currency: 'USD',
    phoneCode: '+1'
  },
  {
    code: 'GB',
    name: { en: 'United Kingdom', ar: 'المملكة المتحدة' },
    flag: '🇬🇧',
    currency: 'GBP',
    phoneCode: '+44'
  },
  {
    code: 'DE',
    name: { en: 'Germany', ar: 'ألمانيا' },
    flag: '🇩🇪',
    currency: 'EUR',
    phoneCode: '+49'
  },
  {
    code: 'FR',
    name: { en: 'France', ar: 'فرنسا' },
    flag: '🇫🇷',
    currency: 'EUR',
    phoneCode: '+33'
  },
  {
    code: 'KW',
    name: { en: 'Kuwait', ar: 'الكويت' },
    flag: '🇰🇼',
    currency: 'KWD',
    phoneCode: '+965'
  },
  {
    code: 'QA',
    name: { en: 'Qatar', ar: 'قطر' },
    flag: '🇶🇦',
    currency: 'QAR',
    phoneCode: '+974'
  },
  {
    code: 'BH',
    name: { en: 'Bahrain', ar: 'البحرين' },
    flag: '🇧🇭',
    currency: 'BHD',
    phoneCode: '+973'
  },
  {
    code: 'OM',
    name: { en: 'Oman', ar: 'عمان' },
    flag: '🇴🇲',
    currency: 'OMR',
    phoneCode: '+968'
  },
  {
    code: 'JO',
    name: { en: 'Jordan', ar: 'الأردن' },
    flag: '🇯🇴',
    currency: 'JOD',
    phoneCode: '+962'
  },
  {
    code: 'LB',
    name: { en: 'Lebanon', ar: 'لبنان' },
    flag: '🇱🇧',
    currency: 'LBP',
    phoneCode: '+961'
  }
];

// Helper functions
export const getCountryByCode = (code: string): Country | undefined => {
  return countries.find(country => country.code === code);
};

export const getCurrencyByCode = (code: string): Currency | undefined => {
  return currencies[code];
};

export const getCountriesByCurrency = (currencyCode: string): Country[] => {
  return countries.filter(country => country.currency === currencyCode);
};

// Default values
export const DEFAULT_COUNTRY = 'EG'; // Egypt
export const DEFAULT_CURRENCY = 'EGP'; // Egyptian Pound
