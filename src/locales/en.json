{"app": {"title": "DAWINII - Smart Pharmacy System", "welcome": "Welcome to DAWINII"}, "login": {"title": "Connect Your Device", "subtitle": "Use the mobile app to connect this device to your account.", "scanQr": "Scan QR to Connect", "roomCode": "Room Code", "roomCodeInstructions": "Enter this code in the mobile app if QR scanning fails:", "qrInstructions": "Scan the QR code using the DAWINII mobile app to log in.", "authenticating": "Authenticating...", "authFailed": "Authentication failed. Please try again.", "loginSuccess": "Login successful. Redirecting…", "tips": {"title": "How to Connect", "step1": "Open the DAWINII mobile app", "step2": "Go to Settings → Connected Devices", "step3": "Tap 'Connect a New Device'", "step4": "Scan the QR code or enter the Room Code", "deviceLimit": "Only devices registered via the mobile app can log in", "subscriptionNote": "Device limits depend on your subscription plan"}, "waitingForConnection": "Waiting for mobile app connection...", "refreshCode": "Refresh Code", "codeExpires": "Code expires in {minutes} minutes"}, "qrScanner": {"scanToConnect": "Scan QR to Connect", "cameraAccessError": "Could not access camera. Please ensure camera permissions are granted.", "initializationError": "Failed to initialize camera. Please try again."}, "home": {"dashboard": "Dashboard", "importExport": "Import/Export", "viewStock": "View Stock", "cart": "<PERSON><PERSON>", "pharmacyBalance": "Pharmacy Balance", "notifications": "Notifications", "settings": "Settings", "welcomeMessage": "This is the main content area. Select an option from the sidebar to get started.", "generateQr": "Generate QR Code", "hideQr": "Hide QR Code", "yourQrCode": "Your Login QR Code", "qrDescription": "Use this QR code to log in from another device:"}, "user": {"owner": "Owner", "staff": "Staff", "settings": "Settings", "role": "Role", "logout": "Logout", "profile": "Profile", "userAccount": "User Account", "userId": "User ID", "email": "Email", "phone": "Phone", "deviceId": "Device ID", "roleAdmin": "<PERSON><PERSON> (Pharmacy Owner)", "roleUser": "User (Pharmacy Staff)", "viewAs": "View as", "adminControls": "Admin Controls", "viewAnalytics": "View Pharmacy Analytics", "manageStaff": "Manage Staff Users", "inventoryAudit": "Inventory Audit Access", "profitDashboard": "Profit Dashboard", "accountPreferences": "Account Preferences", "updateInfo": "Update Personal Info", "updateInfoDesc": "Change your name, email, or phone number", "update": "Update", "changePassword": "Change Password", "changePasswordDesc": "Set a new secure password for your account", "change": "Change", "notifications": "Notifications", "notificationsDesc": "Enable or disable system notifications", "pharmacyInfo": "Pharmacy Information", "firstName": "First Name", "lastName": "Last Name", "gender": "Gender", "preferredLanguage": "Preferred Language", "pharmacyName": "Pharmacy Name", "licenseNumber": "License Number", "taxNumber": "Tax Number", "location": "Location", "address": "Address", "city": "City", "state": "State/Governorate", "country": "Country", "zipCode": "ZIP/Postal Code", "description": "Description", "viewAgreement": "View Agreement Document", "downloadAgreement": "Download Agreement", "personalInfo": "Personal Information"}, "language": {"english": "English", "arabic": "Arabic", "switchLanguage": "Switch Language"}, "settings": {"title": "Settings", "appearance": "Appearance", "themeMode": "Theme Mode", "lightMode": "Light Mode", "darkMode": "Dark Mode", "lightModeEnabled": "Light mode enabled", "darkModeEnabled": "Dark mode enabled", "notifications": "Notifications", "enableNotifications": "Enable Notifications", "notificationsEnabled": "Notifications enabled", "notificationsDisabled": "Notifications disabled", "notificationsEnabledStatus": "You will receive notifications", "notificationsDisabledStatus": "Notifications are disabled", "language": "Language", "changeLanguage": "Change Language", "currentLanguage": "Current language", "connectedDevices": "Connected Devices", "currentDevice": "Current Device", "lastActive": "Last active", "removeDevice": "Remove", "addNewDevice": "Add New Device", "hideQrCode": "Hide QR Code", "scanQrToConnect": "Scan this QR code to connect a new device", "systemPreferences": "System Preferences", "onlineMode": "Online Mode", "onlineModeEnabled": "Online features are active. You can receive orders and participate in Dawinii services.", "onlineModeDisabled": "Working in local mode. Online features are disabled.", "onlineModeDescription": "Enable this to receive online orders and participate in Dawinii services. Disable to work locally without any online features.", "autoAcceptOrders": "Auto Accept Orders", "autoAcceptOrdersEnabled": "New orders will be automatically accepted without manual confirmation.", "autoAcceptOrdersDisabled": "New orders will require manual acceptance through popup notifications.", "autoAcceptOrdersDescription": "When enabled, incoming orders will be automatically accepted and processed without showing the order popup. When disabled, you'll see a popup notification for each new order requiring manual acceptance.", "onlineModeRequired": "Online Mode must be enabled to use Auto Accept Orders.", "additional": "Additional Settings", "additionalDescription": "More settings will be available in future updates.", "regionalSettings": "Regional Settings", "country": "Country", "currency": "<PERSON><PERSON><PERSON><PERSON>", "selectCountry": "Select your country", "selectCurrency": "Select your currency", "countryChanged": "Country updated to {country}", "currencyChanged": "Currency updated to {currency}", "regionalSettingsDescription": "Choose your country. The currency will be automatically set based on your selected country. These settings will be used for pricing, invoices, and regional features throughout the system."}, "notifications": {"title": "Notifications", "noNotifications": "No new notifications", "markAllAsRead": "Mark all as read", "viewAll": "View all", "allNotifications": "All Notifications", "unreadNotifications": "Unread Notifications", "readNotifications": "Read Notifications", "filterByType": "Filter by Type", "searchNotifications": "Search notifications...", "markAsRead": "<PERSON> as read", "markAsUnread": "<PERSON> as unread", "notificationTypes": {"all": "All Types", "order": "Order", "medication": "Medication", "stock": "Stock", "system": "System"}, "timeAgo": {"justNow": "Just now", "minutesAgo": "{count} minute(s) ago", "hoursAgo": "{count} hour(s) ago", "daysAgo": "{count} day(s) ago", "weeksAgo": "{count} week(s) ago", "monthsAgo": "{count} month(s) ago"}, "loadMore": "Load more", "noMoreNotifications": "No more notifications", "goToRelatedPage": "Go to related page", "mockNotifications": {"newMedicationTitle": "New medication added", "newMedicationMessage": "Paracetamol 500mg has been added to inventory", "lowStockTitle": "Low stock alert", "lowStockMessage": "Amoxicillin 250mg is running low", "systemUpdateTitle": "System update", "systemUpdateMessage": "DAWINII system will be updated tonight at 2 AM", "timeAgo": {"minutes": "{count} minutes ago", "hour": "1 hour ago", "hours": "{count} hours ago"}}, "notificationDetails": "Notification Details", "priority": {"low": "Low", "medium": "Medium", "high": "High"}}, "direction": "ltr", "footer": {"date": "Date", "time": "Time", "copyright": "© 2024 DAWINII Pharmacy System. All rights reserved.", "networkStatus": "Network", "online": "Online", "offline": "Offline", "onlineMode": "Online Mode", "enabled": "Enabled", "disabled": "Disabled", "systemStatus": "System", "version": "v1.0.0", "statusDescriptions": {"localMode": "Working in local mode - Online features disabled", "noInternet": "No internet connection - Online features unavailable", "onlineActive": "Online mode active - All features available"}, "popups": {"version": {"title": "Release Notes", "description": "Latest updates and improvements", "readMore": "Read More", "newFeatures": "New Features", "bugFixes": "Bug Fixes", "features": ["Enhanced inventory management", "Improved search functionality", "New dashboard analytics"], "fixes": ["Fixed mobile menu navigation", "Resolved footer positioning issues", "Improved RTL layout support"]}, "networkStatus": {"title": "Network Connection", "details": "Details", "online": {"status": "Connected", "description": "Your device is connected to the internet", "details": ["Real-time data synchronization", "Cloud backup enabled", "Online features available"]}, "offline": {"status": "Disconnected", "description": "No internet connection detected", "details": ["Working in offline mode", "Data will sync when reconnected", "Limited features available"]}}, "onlineMode": {"title": "Online Mode", "details": "Details", "enabled": {"status": "Enabled", "description": "Online features and cloud services are active", "details": ["AI-powered features available", "Cloud synchronization active", "Real-time updates enabled"]}, "disabled": {"status": "Disabled", "description": "Working in local mode only", "details": ["Local data storage only", "AI features unavailable", "Manual sync required"]}}}}, "dashboard": {"inventorySummary": "Inventory Summary", "totalMedications": "Total Medications", "outOfStock": "Out of Stock", "lowStock": "Low Stock", "expiringSoon": "Expiring Soon", "recentlyAdded": "Recently Added", "sponsored": "Sponsored", "learnMore": "Learn More", "previousAd": "Previous Advertisement", "nextAd": "Next Advertisement", "goToSlide": "Go to slide", "tooltips": {"viewAllMedications": "Click to view all medications in stock", "viewOutOfStock": "Click to view all out of stock medications", "viewExpiringSoon": "Click to view medications expiring soon", "viewRecentlyAdded": "Click to view medications added in the last 7 days", "viewLowStock": "Click to view medications with low stock levels", "viewAllOrders": "Click to view all orders", "viewRevenue": "Click to view revenue summary", "viewStoreOrders": "Click to view store orders and manage requests from partner stores", "viewTodayInvoices": "Click to view today's invoices", "viewPendingReviews": "Click to view customer reviews awaiting response"}, "ordersInvoices": "Orders & Invoices", "invoicesToday": "Today's Invoices", "todaysSales": "Today's Sales", "invoices": "Invoices", "revenue": "Revenue", "topSellingMedications": "Top Selling Medications", "units": "units", "deliveryOrders": "Delivery Orders", "new": "New", "inTransit": "In Transit", "completed": "Completed", "storeOrders": {"title": "Store Orders", "pending": "Pending", "inTransit": "In Transit", "completed": "Completed", "rejected": "Rejected", "totalOrders": "Total Orders", "completionRate": "Success Rate", "viewPendingOrders": "View Pending Orders"}, "appOrders": "App Orders This Month", "totalUsers": "Total Users", "activeUsers": "Active Users", "totalOrders": "Total Orders", "averageOrderValue": "Avg. Order Value", "quickActions": "Quick Actions", "addNewMedication": "Add New Medication", "newSaleInvoice": "New Sale / Invoice", "generateReports": "Generate Reports", "generateReportsTooltip": "Generate detailed reports and analytics", "scanQrToConnect": "Scan QR to Connect Device", "monthlyRevenue": "Monthly Revenue", "months": {"jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "may": "May", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec"}}, "importExport": {"title": "Import / Export", "importTitle": "Import from CSV / JSON using AI", "importDescription": "Upload a CSV or JSON file containing medication or inventory data. Our AI will help validate and categorize the data.", "importTitleOffline": "Import from CSV / JSON", "importDescriptionOffline": "Upload a CSV or JSON file containing medication or inventory data. Enable Online Mode to access AI-powered validation and categorization.", "uploadButton": "Choose <PERSON>", "acceptedFileTypes": "Accepted file types: .csv, .json", "dragDropText": "Drag and drop your file here, or click to browse", "dropFile": "Drop your file here", "supportedFormats": "Supports CSV and JSON files (max 10MB)", "fileTooLarge": "File size must be less than 10MB", "fileUploaded": "File uploaded successfully!", "dataPreview": "Data Preview", "moreRows": "+{count} more rows", "runAiAssist": "Run AI Assist", "aiProcessing": "AI is analyzing your data...", "aiProcessed": "AI analysis complete! Data has been validated and categorized.", "unsupportedFileType": "Unsupported file type. Please upload a CSV or JSON file.", "errorReadingFile": "Error reading file. Please try again.", "invalidCsvFormat": "Invalid CSV format. Please check your file and try again.", "exportTitle": "Export to CSV / JSON", "exportDescription": "Export your current pharmacy stock or inventory data in your preferred format.", "exportCsv": "Export as CSV", "exportJson": "Export as JSON", "exporting": "Exporting as {format}...", "exportSuccess": "Export as {format} completed successfully!", "exportDataPreview": "Data to be exported", "name": "Name", "category": "Category", "stock": "Stock", "status": "Status", "aiFeatureOffline": "AI-Powered Import Assistant", "aiFeatureOfflineMessage": "Connect to the internet and enable Online Mode to unlock our powerful AI assistant that can automatically categorize, validate, and optimize your imported data.", "enableOnlineModeHint": "Go to Settings → Enable Online Mode to access this feature"}, "wallet": {"title": "Pharmacy Balance", "balance": "Balance", "points": "Points", "pointsLabel": "points", "monthlyLimit": "Available Limit This Month", "walletLabel": "Wallet", "id": "ID", "expiringPointsMonths": "{amount} points expire in {months} months", "expiringPointsDays": "{amount} points will expire in {days} days", "completeTasksToEarnPoints": "Complete tasks to earn reward points", "transactionHistory": "Transaction History", "transactionId": "Transaction ID", "type": "Type", "amount": "Amount", "pointsValue": "Points", "date": "Date/Time", "status": "Status", "earnMorePoints": "<PERSON><PERSON><PERSON>", "extraMissions": "Extra Missions", "pending": "Pending", "viewAllMedications": "Click to view all needed medications", "transactionTypes": {"purchase": "Purchase", "topup": "Top-Up", "pointearned": "Point Earned"}, "statuses": {"completed": "Completed", "pending": "Pending", "failed": "Failed"}}, "stock": {"title": "Medication Stock", "search": "Search medications...", "filter": "Filter", "status": "Status", "expiryStatus": "Expiry Status", "allItems": "All Items", "inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "recentlyAdded": "Recently Added", "valid": "<PERSON><PERSON>", "expiringSoon": "Expiring Soon", "expired": "Expired", "itemsPerPage": "Items per page", "of": "of", "noResults": "No medications found matching your criteria", "loading": "Loading inventory data...", "error": "Error loading inventory data", "retry": "Retry", "columns": {"name": "Medication Name", "category": "Category", "quantity": "Quantity in Stock", "expiryDate": "Expiry Date", "pricePerUnit": "Price per Unit", "status": "Status"}, "sortAsc": "Sort ascending", "sortDesc": "Sort descending"}, "medication": {"addNewMedication": "Add New Medication", "addNewMedicationTooltip": "Add one or more medications to inventory", "medicationDetails": "Medication Details", "name": "Medication Name", "namePlaceholder": "Enter medication name", "barcode": "Barcode", "barcodePlaceholder": "Enter barcode (optional)", "category": "Category", "categoryPlaceholder": "Select category", "customCategoryPlaceholder": "Enter custom category", "type": "Type", "quantity": "Quantity in Stock", "expiryDate": "Expiry Date", "price": "Price", "purchasePrice": "Purchase Price", "discount": "Discount", "discountPercentage": "Discount Percentage (%)", "validUntil": "<PERSON>id <PERSON>", "supplier": "Supplier", "storageLocation": "Storage Location", "addAnother": "Add Another Medication", "removeMedication": "Remove Medication", "addSuccess": "Medication(s) added successfully!", "delete": "Delete Medication", "confirmDelete": "Confirm Deletion", "deleteWarning": "Are you sure you want to delete this medication? This action cannot be undone.", "outOfStock": "This medication is out of stock", "inStock": "In Stock", "lowStockWarning": "Low stock: Only {quantity} units remaining", "expiringSoonWarning": "Expires soon: {date}", "expiredWarning": "Expired on: {date}", "basicInfo": "Basic Information", "stockInfo": "Stock & Expiry", "pricingInfo": "Pricing Information", "supplyInfo": "Supply Information", "discountRate": "Discount Rate", "units": "units", "maxAvailable": "Max", "types": {"pill": "<PERSON>ll", "syrup": "<PERSON><PERSON><PERSON>", "injection": "Injection", "cream": "Cream", "drops": "Drops", "inhaler": "<PERSON><PERSON><PERSON>", "medication": "Medication", "other": "Other"}, "categories": {"Pain Relief": "Pain Relief", "Antibiotics": "Antibiotics", "Allergy": "Allergy", "Digestive": "Digestive", "Vitamins": "Vitamins", "Cardiovascular": "Cardiovascular", "Respiratory": "Respiratory", "Diabetes": "Diabetes", "Mental Health": "Mental Health", "Skin Care": "Skin Care", "Eye Care": "Eye Care", "First Aid": "First Aid", "Other": "Other"}}, "validation": {"required": "This field is required", "positiveNumber": "Please enter a positive number", "nonNegativeNumber": "Please enter a non-negative number", "percentageRange": "Percentage must be between 1 and 100", "futureDateRequired": "Please enter a future date", "invalidDate": "Please enter a valid date"}, "cart": {"title": "Shopping Cart", "items": "Cart Items", "emptyCart": "Your cart is empty", "emptyCartMessage": "Add items from the stock to start your order", "browseItems": "Browse Items", "addToCart": "Add to Cart", "updateCart": "Update Cart", "removeFromCart": "Remove", "clearCart": "Clear Cart", "continueShopping": "Continue Shopping", "checkout": "Proceed to Checkout", "summary": "Order Summary", "subtotal": "Subtotal", "tax": "Tax", "total": "Total", "quantity": "Quantity", "actions": "Cart Actions", "addedToCart": "Item added to cart successfully", "addedToCurrentPack": "Added to current pack", "addedToNewPack": "Added to new pack", "addedToSpecificPack": "Added to {packName}", "updatedInCart": "Cart updated successfully", "removedFromCart": "Item removed from cart", "cartCleared": "<PERSON><PERSON> cleared successfully", "orderPlaced": "Order placed successfully", "maxPacksReachedError": "Maximum order packs reached (12). Please serve or remove existing packs first.", "maxPacksReachedAlert": "Cannot add to new pack: Maximum 12 order packs reached. Please complete or cancel existing orders first.", "orderPacks": {"title": "Order Packs", "autoAcceptedOrder": "Auto-Accepted Order", "localOrder": "Local Order #{number}", "manualOrder": "Manual Order", "unknownOrder": "Unknown Order", "createLocalOrder": "New Local Order", "createFirstOrder": "Create First Order", "cancelPack": "Cancel Pack", "noActivePacksMessage": "Create a new local order or wait for incoming orders from the app", "addToCurrentPack": "Add to Current Pack", "addToNewPack": "Add to New Pack", "selectPack": "Select Pack", "selectPackToAdd": "Select which pack to add this item to:", "createNewPack": "Create New Pack", "packSelection": "Pack Selection", "activePackCount": "{count} active order pack | {count} active order packs", "itemCount": "{count} item | {count} items", "pointsToEarn": "{points} points", "expiresIn": "Expires in", "expiringSoon": "Expiring soon:", "expired": "Expired", "expiredStatus": "This reservation has expired", "expiringSoonStatus": "This reservation expires soon", "serviceType": {"reserved": "Reservation", "delivery": "Delivery"}, "localOrderCreated": "New local order created successfully", "packCancelled": "Order pack cancelled", "maxPacksReached": "Maximum order packs limit reached (10 packs)", "maxPacksReachedTooltip": "You can have a maximum of 10 active order packs at once", "maxReached": "<PERSON> reached", "remaining": "remaining"}, "packWarning": {"title": "Serve Orders Quickly!", "message": "You have more than 5 order packs. Please serve them quickly to get customer ratings and earn wallet points. You can only have 12 online packs maximum, so finish current orders to receive more."}}, "checkout": {"title": "Checkout", "emptyCart": "Your cart is empty", "emptyCartMessage": "Add items to your cart before proceeding to checkout", "browseItems": "Browse Items", "customerInfo": "Customer Info", "paymentDelivery": "Payment & Delivery", "confirmation": "Confirmation", "customerInformation": "Customer Information", "firstName": "First Name", "lastName": "Last Name", "firstNamePlaceholder": "Enter first name", "lastNamePlaceholder": "Enter last name", "email": "Email", "emailPlaceholder": "Enter email address", "phone": "Phone Number", "phonePlaceholder": "Enter phone number", "deliveryOptions": "Delivery Options", "pickup": "Pickup", "pickupDescription": "Collect from pharmacy", "delivery": "Home Delivery", "deliveryDescription": "Delivered to your address", "address": "Address", "addressPlaceholder": "Enter delivery address", "city": "City", "cityPlaceholder": "Enter city", "postalCode": "Postal Code", "postalCodePlaceholder": "Enter postal code", "paymentMethod": "Payment Method", "creditCard": "Credit Card", "orderConfirmed": "Order Confirmed!", "orderConfirmedMessage": "Thank you for your order. We will process it shortly and notify you when it is ready.", "viewOrders": "View My Orders", "printInvoice": "Print Invoice", "printError": "Failed to print invoice. Please try again.", "paymentMethods": {"card": "Credit Card", "cash": "Cash", "bank": "Bank Transfer"}, "invoice": {"pharmacyName": "DAWINII PHARMACY", "title": "Invoice", "invoiceNumber": "Invoice #", "orderDetails": "Order Details", "orderDate": "Order Date", "orderTime": "Order Time", "paymentMethod": "Payment Method", "deliveryOption": "Delivery Option", "pharmacyPickup": "Pharmacy Pickup", "homeDelivery": "Home Delivery", "customerInformation": "Customer Information", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "notes": "Notes", "item": "<PERSON><PERSON>", "quantity": "Quantity", "unitPrice": "Unit Price", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "totalAmount": "Total Amount", "thankYou": "Thank you for your business!", "systemName": "Dawinii Pharmacy System", "computerGenerated": "This is a computer-generated invoice. No signature required."}, "continueShopping": "Continue Shopping", "pleaseCompleteRequiredFields": "Please complete all required fields", "orderPlacedSuccessfully": "Order placed successfully!", "orderFailed": "Failed to place order. Please try again.", "processing": "Processing...", "placeOrder": "Place Order", "next": "Next", "previous": "Previous", "backToCart": "Back to Cart", "autoAcceptedOrder": "Auto-Accepted Order", "orderFromApp": "Order received from mobile app", "localOrder": "Local Order", "localOrderNumber": "Local Order #{number}", "pointsToEarn": "{points} points to earn", "expiresInHours": "Expires in {hours} hours", "deliveryOrder": "Delivery Order", "reservationOrder": "Reservation Order", "notes": "Notes", "notesPlaceholder": "Any special instructions or notes...", "cashDescription": "Pay with cash on delivery/pickup", "cardDescription": "Pay with credit or debit card", "cash": "Cash", "orderNotes": "Order Notes", "orderNotesPlaceholder": "Any special instructions or notes for your order...", "cardholderName": "Cardholder Name", "cardholderNamePlaceholder": "Enter cardholder name", "cardNumber": "Card Number", "cardNumberPlaceholder": "1234 5678 9012 3456", "expiryDate": "Expiry Date", "expiryDatePlaceholder": "MM/YY", "cvv": "CVV", "cvvPlaceholder": "123", "insurance": "Insurance"}, "common": {"loading": "Loading...", "changingLanguage": "Changing language to English...", "all": "All", "period": "Period", "verification": "Verification", "verified": "Verified", "unverified": "Unverified", "allTime": "All Time", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "thisYear": "This Year", "note": "Note", "cancel": "Cancel", "current": "Current", "back": "Back", "save": "Save", "saveAll": "Save All", "saving": "Saving...", "delete": "Delete", "yes": "Yes", "no": "No", "requiredFields": "Required fields", "resetFilters": "Reset Filters", "status": "Status", "actions": "Actions", "dashboard": "Dashboard", "noDataDescription": "Try adjusting your filters or search criteria", "openMenu": "Open menu", "closeMenu": "Close menu", "dismiss": "<PERSON><PERSON><PERSON>", "error": "Error", "success": "Success", "currency": "EGP", "confirm": "Confirm", "warning": "Warning", "info": "Information", "readMore": "Read More", "refresh": "Refresh", "refreshing": "Refreshing...", "retry": "Try Again", "search": "Search...", "filter": "Filter", "reset": "Reset", "close": "Close", "bugFixes": "Bug Fixes"}, "reviews": {"title": "Customer Reviews", "reviews": "reviews", "averageRating": "Average Rating", "totalReviews": "Total Reviews", "starBreakdown": "Rating Breakdown", "customerName": "Customer", "anonymous": "Anonymous", "reviewDate": "Review Date", "medicationName": "Medication", "rating": "Rating", "reviewMessage": "Review", "noReviews": "No reviews available", "loadingReviews": "Loading reviews...", "stars": {"1": "1 Star", "2": "2 Stars", "3": "3 Stars", "4": "4 Stars", "5": "5 Stars"}, "tips": {"title": "Tips & Suggestions", "keepStockUpdated": "Keep your stock up-to-date to receive better ratings.", "politeCommunication": "Polite communication earns more trust.", "fastDelivery": "Quick delivery times improve customer satisfaction.", "qualityMedications": "Ensure medication quality and proper storage.", "respondToFeedback": "Respond to customer feedback professionally.", "improvementNote": "Following these tips can help improve your pharmacy's customer satisfaction and ratings."}, "subtitle": "View and manage customer feedback for your pharmacy", "searchPlaceholder": "Search reviews by customer name or message...", "noReviewsDescription": "Customer reviews will appear here once they start rating your pharmacy.", "errorLoading": "Failed to load reviews. Please try again.", "verifiedPurchase": "Verified Purchase", "orderId": "Order ID", "offlineDataWarning": "Online mode is disabled. The displayed reviews are cached from your last online session."}, "orders": {"title": "Orders & Invoices", "allOrders": "All Pharmacy Orders", "invoices": "Invoices", "revenueSummary": "Revenue Summary", "topSelling": "Top Selling", "topSellingMedications": "Top-Selling Medications", "deliveryOrders": "Delivery Orders", "reservedMedications": "Reserved Medications", "revenueTrends": "Revenue Trends", "filterBy": "Filter by", "sortBy": "Sort by", "thisMonth": "This Month", "last6Months": "Last 6 Months", "lastYear": "Last Year", "last5Years": "Last 5 Years", "allTime": "All Time", "today": "Today", "thisWeek": "This Week", "thisYear": "This Year", "last7Days": "Last 7 Days", "all": "All", "custom": "Custom", "customDateRange": "Custom Date Range", "startDate": "Start Date", "endDate": "End Date", "orderId": "Order ID", "invoiceId": "Invoice ID", "customer": "Customer", "totalAmount": "Total Amount", "date": "Date", "status": "Status", "paymentMethod": "Payment Method", "actions": "Actions", "downloadPdf": "Download PDF", "noPdfAvailable": "No PDF available", "totalRevenue": "Total Revenue", "numberOfOrders": "Number of Orders", "averageOrderValue": "Average Order Value", "medicationName": "Medication Name", "category": "Category", "unitsSold": "Units Sold", "totalSales": "Total Sales", "mostSold": "Most Sold", "highestRevenue": "Highest Revenue", "noOrdersFound": "No orders found for the selected period", "noInvoicesFound": "No invoices found for the selected period", "noMedicationsFound": "No medications found", "noDeliveryOrdersFound": "No delivery orders found for the selected criteria", "noReservedMedicationsFound": "No reserved medications found for the selected criteria", "searchReservedPlaceholder": "Search reserved medications by name or reserved by...", "searchOrdersPlaceholder": "Search orders by ID, customer name, or amount...", "searchInvoicesPlaceholder": "Search invoices by ID, order ID, or amount...", "searchDeliveryPlaceholder": "Search delivery orders by ID, customer, or address...", "searchRevenuePlaceholder": "Search revenue data by time period or amount...", "searchMedicationsPlaceholder": "Search medications by name or category...", "clickToViewOrderDetails": "Click on any order row to view details", "revenue": "Revenue", "address": "Address", "itemsCount": "Items Count", "deliveryTime": "Delivery Time", "deliveryPartner": "Delivery Partner", "contactNumber": "Contact Number", "orderNotes": "Notes", "quantityReserved": "Quantity Reserved", "reservedBy": "Reserved By", "reservationSource": "Reservation Source", "reservationDate": "Reservation Date", "expiryDate": "Expiry Date", "releaseReservation": "Release Reservation", "confirmRelease": "Are you sure you want to release this reservation?", "reservationReleased": "Reservation has been released successfully", "filterByStatus": "Filter by Status", "filterByPartner": "Filter by Partner", "filterBySource": "Filter by Source", "statuses": {"delivered": "Delivered", "pending": "Pending", "canceled": "Canceled", "out_for_delivery": "Out for Delivery", "active": "Active", "expired": "Expired"}, "deliveryOrderDetails": "Delivery Order Details", "phoneNumber": "Phone Number", "deliveryAddress": "Delivery Address", "expectedDeliveryTime": "Expected Delivery Time", "orderedMedications": "Ordered Medications", "notProvided": "Not Provided", "copyNumber": "Copy", "liveTracking": "Live Tracking", "clickToViewDetails": "Click on any order to view delivery details and track the delivery in real time.", "orderedItems": "Ordered Items", "items": "items", "each": "each", "subtotal": "Subtotal", "total": "Total", "trackingActive": "Live Tracking Active", "pharmacy": "Pharmacy", "driver": "Driver", "partners": {"talabat": "Talabat", "careem": "<PERSON><PERSON>", "pharmacy_driver": "Pharmacy Driver", "other": "Other"}, "sources": {"app": "Mobile App", "phone": "Phone Call", "in_person": "In Person", "website": "Website"}, "paymentMethods": {"credit": "Credit Card", "cash": "Cash", "bank-transfer": "Bank Transfer", "wallet": "Wallet"}, "orderDetails": "Order Details", "datePlaced": "Date Placed", "paymentStatus": "Payment Status", "shippingMethod": "Shipping Method", "estimatedDelivery": "Estimated Delivery", "liveDeliveryTracking": "Live Delivery Tracking", "defaultPharmacyName": "Dawinii Pharmacy - Main Branch", "pharmacyName": "Pharmacy", "print": "Print", "cancel": "Cancel Order", "reorder": "Reorder", "download": "Download PDF", "sendEmail": "Send Email", "confirmCancel": "Are you sure you want to cancel this order? This action cannot be undone.", "orderCancelled": "Order cancelled successfully", "orderReordered": "Items added to cart successfully", "emailSent": "Order summary sent successfully", "cancelError": "Failed to cancel order", "reorderError": "Failed to add items to cart", "emailError": "Failed to send email", "paymentStatuses": {"paid": "Paid", "pending": "Pending", "failed": "Failed", "refunded": "Refunded"}, "shippingMethods": {"standard": "Standard Delivery", "express": "Express Delivery", "pickup": "Pharmacy Pickup", "same_day": "Same Day Delivery"}, "categories": {"painRelief": "Pain Relief", "antibiotics": "Antibiotics", "vitamins": "Vitamins", "digestive": "Digestive"}, "metricType": "Metric Type", "allMetrics": "All Metrics", "revenueOnly": "Revenue Only", "ordersOnly": "Orders Only", "averageValue": "Average Value"}, "invoices": {"invoiceDetails": "Invoice Details", "invoiceId": "Invoice ID", "dateIssued": "Date Issued", "status": {"label": "Status", "paid": "Paid", "unpaid": "Unpaid", "canceled": "Canceled"}, "pharmacyName": "Pharmacy Name", "createdBy": "Created By", "paymentMethod": "Payment Method", "currency": "<PERSON><PERSON><PERSON><PERSON>", "itemsList": "Items List", "subtotal": "Subtotal", "discount": "Discount", "tax": "Tax", "grandTotal": "Grand Total", "print": "Print", "delete": "Delete", "downloadPdf": "Download PDF", "sendEmail": "Send Email", "duplicate": "Duplicate", "markAsPaid": "<PERSON> as <PERSON><PERSON>", "confirmDelete": "Are you sure you want to delete this invoice? This action cannot be undone.", "pharmacy": "Pharmacy", "orderInfo": "Order Information", "orderId": "Order ID", "paymentInfo": "Payment Information", "financialSummary": "Financial Summary", "notes": "Notes", "medication": "Medication", "quantity": "Quantity", "unitPrice": "Unit Price", "total": "Total", "invoiceStatuses": {"paid": "Paid", "unpaid": "Unpaid", "canceled": "Canceled"}, "paymentMethods": {"credit_card": "Credit Card", "cash": "Cash", "bank_transfer": "Bank Transfer", "online": "Online Payment"}}, "pagination": {"showing": "Showing", "to": "to", "of": "of", "results": "results", "previous": "Previous", "next": "Next", "itemsPerPage": "Items per page"}, "toasts": {"success": "Success", "error": "Error", "invoiceDeleted": "Invoice deleted successfully", "invoiceDuplicated": "Invoice duplicated successfully", "emailSent": "<PERSON>ail sent successfully", "statusChanged": "Status changed successfully", "deleteError": "Failed to delete invoice", "duplicateError": "Failed to duplicate invoice", "emailError": "Failed to send email", "statusChangeError": "Failed to change status"}, "network": {"offline": "You are offline. Online features are temporarily unavailable.", "cannotEnableOnlineMode": "Cannot enable Online Mode without an internet connection.", "onlineModeEnabled": "Online Mode enabled", "onlineModeDisabled": "Online Mode disabled"}, "orderRequests": {"title": "Order Request", "newOrderRequest": "New Order Request", "orderRequestDetails": "Order Request Details", "userInformation": "User Information", "userName": "User Name", "phoneNumber": "Phone Number", "serviceType": "Service Type", "location": "Location", "pointsToEarn": "Points to Earn", "orderTime": "Order Time", "timeSinceCreation": "Time Since Creation", "requestedMedications": "Requested Medications", "medicationName": "Medication Name", "quantity": "Quantity", "unitPrice": "Unit Price", "totalPrice": "Total Price", "status": "Status", "actions": "Actions", "acceptOrder": "Accept Order", "rejectOrder": "Reject Order", "releaseLocked": "Release Locked Item", "actionCompleted": "Action Completed", "confirmAccept": "Are you sure you want to accept this order request?", "confirmReject": "Are you sure you want to reject this order request?", "confirmRelease": "Are you sure you want to release this locked medication?", "orderAccepted": "Order request accepted successfully", "orderAcceptedAndAddedToCart": "Order from {userName} accepted and added to cart", "orderRejected": "Order request rejected successfully", "medicationReleased": "Medication released successfully", "acceptError": "Failed to accept order request", "rejectError": "Failed to reject order request", "releaseError": "Failed to release medication", "autoAcceptedNotification": "New order from {customerName} automatically accepted! {itemCount} items added to cart. Please pick and pack.", "serviceTypes": {"Reserved": "Reserved", "Delivery": "Delivery"}, "medicationStatuses": {"available": "Available", "out_of_stock": "Out of Stock", "locked": "Locked", "low_stock": "Low Stock"}, "lockedItemMessage": "This item has been locked for more than 6 hours. You may now release it to accept other orders.", "noActiveRequests": "No active order requests", "processing": "Processing...", "estimatedDelivery": "Estimated Delivery", "reservationExpiry": "Reservation Expires", "totalAmount": "Total Amount", "notes": "Notes", "noNotes": "No notes provided", "ago": "ago", "minutes": "minutes", "hours": "hours", "days": "days", "lockedFor": "Locked for {duration} hours", "canRelease": "Can be released", "newRequestNotification": "New {serviceType} order request from {userName}", "requestExpired": "This order request has expired", "requestCancelled": "This order request has been cancelled by the user", "noAvailableMedications": "No available medications to fulfill this order.", "cannotAcceptOrderExplanation": "All requested medications are either out of stock or locked. You cannot accept this order at this time.", "phoneCopiedSuccess": "Phone number copied to clipboard", "phoneCopyError": "Failed to copy phone number", "clickToEnableSound": "Click anywhere to enable notification sounds", "onlineModeRequired": "Online mode must be enabled to view order requests", "autoAcceptSuccess": "New order added to your app cart, please pick and pack it", "autoAcceptFailed": "Auto-accept failed: Maximum order limit reached. Please complete some orders first."}, "header": {"notifications": "Notifications", "userMenu": "User <PERSON>u"}, "stores": {"title": "Partner Stores", "search": "Search stores...", "noStores": "No stores found", "noStoresDescription": "No partner stores match your search criteria.", "loadingStores": "Loading stores...", "errorLoading": "Error loading stores", "retryLoading": "Retry", "seeStock": "See Stock", "order": "Order", "viewInventory": "View Inventory", "sendOrderRequest": "Send Order Request", "storeDetails": "Store Details", "location": "Location", "city": "City", "region": "Region", "storeType": "Store Type", "phone": "Phone", "workingHours": "Working Hours", "deliverySupport": "Delivery Support", "available": "Available", "unavailable": "Unavailable", "openNow": "Open Now", "closedNow": "Closed Now", "storeTypes": {"main": "Main Branch", "branch": "Branch", "partner": "Partner Store"}, "filters": {"city": "City", "storeType": "Store Type", "availability": "Availability", "openNow": "Open Now", "deliverySupport": "Delivery Support"}, "stockModal": {"title": "Store Inventory", "subtitle": "Available medications at {storeName}", "noStock": "No medications available", "noStockDescription": "This store currently has no medications in stock.", "searchStock": "Search medications...", "quantity": "Quantity", "price": "Price", "addToOrder": "Add to Order", "outOfStock": "Out of Stock", "lowStock": "Low Stock", "inStock": "In Stock"}, "orderRequest": {"title": "Send Order Request", "subtitle": "Request medications from {storeName}", "selectMedications": "Select medications to request", "requestSent": "Order request sent successfully", "requestFailed": "Failed to send order request", "noMedicationsSelected": "Please select at least one medication", "send": "Send Request", "cancel": "Cancel"}}}