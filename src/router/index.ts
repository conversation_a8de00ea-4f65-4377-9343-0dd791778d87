import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';

// Lazy load components for better performance
const LoginView = () => import('../views/LoginView.vue');
const DashboardView = () => import('../views/DashboardView.vue');
const ImportExportView = () => import('../views/ImportExportView.vue');
const StockView = () => import('../views/StockView.vue');
const ProfileView = () => import('../views/ProfileView.vue');
const PharmacyBalanceView = () => import('../views/PharmacyBalanceView.vue');
const OrdersInvoicesView = () => import('../views/OrdersInvoicesView.vue');
const NotificationsView = () => import('../views/NotificationsView.vue');
const SettingsView = () => import('../views/SettingsView.vue');
const CartView = () => import('../views/CartView.vue');
const CheckoutView = () => import('../views/CheckoutView.vue');
const ReviewsView = () => import('../views/ReviewsView.vue');
const StoresView = () => import('../views/StoresView.vue');
const StoreStockView = () => import('../views/StoreStockView.vue');
const MainLayout = () => import('../views/MainLayout.vue');

// Define route meta type
declare module 'vue-router' {
  interface RouteMeta {
    requiresAuth: boolean;
  }
}

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'login',
    component: LoginView,
    meta: {
      requiresAuth: false
    }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/',
    component: MainLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: DashboardView,
        meta: { requiresAuth: true }
      },
      {
        path: 'import-export',
        name: 'import-export',
        component: ImportExportView,
        meta: { requiresAuth: true }
      },
      {
        path: 'stock',
        name: 'stock',
        component: StockView,
        meta: { requiresAuth: true }
      },
      {
        path: 'profile',
        name: 'profile',
        component: ProfileView,
        meta: { requiresAuth: true }
      },
      {
        path: 'pharmacy-balance',
        name: 'pharmacy-balance',
        component: PharmacyBalanceView,
        meta: { requiresAuth: true }
      },
      {
        path: 'orders-invoices',
        name: 'orders-invoices',
        component: OrdersInvoicesView,
        meta: { requiresAuth: true }
      },
      {
        path: 'notifications',
        name: 'notifications',
        component: NotificationsView,
        meta: { requiresAuth: true }
      },
      {
        path: 'settings',
        name: 'settings',
        component: SettingsView,
        meta: { requiresAuth: true }
      },
      {
        path: 'cart',
        name: 'cart',
        component: CartView,
        meta: { requiresAuth: true }
      },
      {
        path: 'checkout',
        name: 'checkout',
        component: CheckoutView,
        meta: { requiresAuth: true }
      },
      {
        path: 'reviews',
        name: 'reviews',
        component: ReviewsView,
        meta: { requiresAuth: true }
      },
      {
        path: 'stores',
        name: 'stores',
        component: StoresView,
        meta: { requiresAuth: true }
      },
      {
        path: 'stores/:id/stock',
        name: 'store-stock',
        component: StoreStockView,
        meta: { requiresAuth: true }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// Navigation guard for authentication
router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('dawinii-auth-token') !== null;

  if (to.meta.requiresAuth && !isAuthenticated) {
    // Redirect to login if trying to access a protected route while not authenticated
    next({ name: 'login' });
  } else if ((to.path === '/login' || to.path === '/') && isAuthenticated) {
    // Redirect to dashboard if already authenticated and trying to access login page or root
    next({ name: 'dashboard' });
  } else {
    next();
  }
});

export default router;
