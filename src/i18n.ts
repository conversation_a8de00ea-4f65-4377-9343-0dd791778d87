import { createI18n } from 'vue-i18n';
import { inject } from 'vue';
import { useRouter } from 'vue-router';
import en from './locales/en.json';
import ar from './locales/ar.json';

// Get the preferred language from localStorage or use English as default
const savedLocale = localStorage.getItem('dawinii-locale') || 'en';

const i18n = createI18n({
  legacy: false, // Use Composition API
  locale: savedLocale,
  fallbackLocale: 'en',
  messages: {
    en,
    ar
  }
});

// Type for the setLanguageLoading function
type SetLanguageLoadingFn = (loading: boolean, message?: string) => void;

// Function to change the language
export const setLanguage = (lang: 'en' | 'ar') => {
  // Get the setLanguageLoading function from the provide/inject system
  const setLanguageLoading = inject('setLanguageLoading') as SetLanguageLoadingFn | undefined;

  if (i18n.global.locale.value !== lang) {
    // Show loading overlay
    if (setLanguageLoading) {
      // Use the translated message from the target language
      const message = lang === 'en' ?
        'Changing language to English...' :
        'جاري تغيير اللغة إلى العربية...';

      setLanguageLoading(true, message);
    }

    // Update i18n locale
    i18n.global.locale.value = lang;
    localStorage.setItem('dawinii-locale', lang);

    // Update the document direction
    const direction = lang === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('dir', direction);
    document.documentElement.setAttribute('lang', lang);

    // Add or remove RTL class
    if (direction === 'rtl') {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }

    // Navigate to login page with a slight delay to allow the loading overlay to appear
    setTimeout(() => {
      // Navigate to login page
      window.location.href = '/login';

      // Hide loading overlay after navigation (this will run after the page reloads)
      if (setLanguageLoading) {
        setTimeout(() => {
          setLanguageLoading(false);
        }, 500);
      }
    }, 1000);
  }
};

export default i18n;
