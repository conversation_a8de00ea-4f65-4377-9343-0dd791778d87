import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import orderSettingsStore from '../store/orderSettingsStore';
import onlineModeStore from '../store/onlineModeStore';
import orderPackStore from '../store/orderPackStore';
import type { OrderRequest } from '../types/orderRequests';

/**
 * Order Notifications Composable
 * Handles order notifications with auto-accept functionality
 */

// Types - Using existing OrderRequest type from the system

// Reactive state
const pendingOrders = ref<OrderRequest[]>([]);
const currentOrderPopup = ref<OrderRequest | null>(null);
const isProcessingOrder = ref(false);

// Computed properties
const hasAutoAcceptEnabled = computed(() => orderSettingsStore.state.autoAcceptOrders.value);
const isOnlineModeEnabled = computed(() => onlineModeStore.state.onlineModeEnabled.value);
const shouldShowPopup = computed(() => !hasAutoAcceptEnabled.value && isOnlineModeEnabled.value);

// Audio management
let notificationSound: HTMLAudioElement | null = null;
let soundInterval: number | null = null;

const initializeSound = () => {
  if (!notificationSound) {
    notificationSound = new Audio('/sounds/notification.mp3'); // Adjust path as needed
    notificationSound.volume = 0.7;
  }
};

const playNotificationSound = (repeat = false) => {
  initializeSound();

  if (notificationSound) {
    notificationSound.play().catch(console.warn);

    if (repeat) {
      soundInterval = setInterval(() => {
        notificationSound?.play().catch(console.warn);
      }, 3000); // Repeat every 3 seconds
    }
  }
};

const stopNotificationSound = () => {
  if (soundInterval) {
    clearInterval(soundInterval);
    soundInterval = null;
  }
};

// Order processing functions
const processOrderAutomatically = async (orderRequest: OrderRequest, t: any): Promise<boolean> => {
  console.log('Auto-accepting order:', orderRequest.id);
  isProcessingOrder.value = true;

  try {
    // Check if we can add more packs
    if (!orderPackStore.canAddMorePacks()) {
      console.warn('Cannot auto-accept order: Maximum order pack limit reached');
      // You could show a notification to the user here
      return false;
    }

    // Create a new order pack for this auto-accepted order
    const packId = orderPackStore.addAutoAcceptedOrder(orderRequest);

    if (!packId) {
      console.error('Failed to create order pack for auto-accepted order');
      return false;
    }

    console.log('Order auto-accepted successfully:', orderRequest.id, 'as pack:', packId);
    console.log('Added', orderRequest.medications.length, 'items to order pack');

    // Show success notification
    const notificationEvent = new CustomEvent('showNotification', {
      detail: {
        message: t('orderRequests.autoAcceptedNotification', {
          customerName: orderRequest.userName,
          itemCount: orderRequest.medications.length
        }),
        type: 'success',
        duration: 5000
      }
    });
    window.dispatchEvent(notificationEvent);

    // Dispatch event for other components to listen
    const event = new CustomEvent('orderAutoAccepted', {
      detail: { orderRequest }
    });
    window.dispatchEvent(event);

    return true;
  } catch (error) {
    console.error('Failed to auto-accept order:', error);
    return false;
  } finally {
    isProcessingOrder.value = false;
  }
};

// Create the main handler function that will be returned from the composable
const createOrderHandler = (t: any) => {
  return async (orderRequest: OrderRequest): Promise<boolean> => {
    console.log('🔔 New order request received:', orderRequest.id);
    console.log('📊 Auto-accept enabled:', hasAutoAcceptEnabled.value);
    console.log('🌐 Online mode enabled:', isOnlineModeEnabled.value);
    console.log('💾 localStorage value:', localStorage.getItem('dawinii-auto-accept-orders'));

    // Check if online mode is enabled
    if (!isOnlineModeEnabled.value) {
      console.log('❌ Online mode disabled, ignoring order');
      return false;
    }

    // Check auto-accept setting
    if (hasAutoAcceptEnabled.value) {
      console.log('✅ Auto-accept enabled, processing order automatically');
      const success = await processOrderAutomatically(orderRequest, t);

      if (success) {
        console.log('🎉 Order auto-accepted and added to cart');

        // Show success notification
        const event = new CustomEvent('showNotification', {
          detail: {
            message: t('orderRequests.autoAcceptSuccess', 'New order added to your app cart, please pick and pack it'),
            type: 'success',
            duration: 4000
          }
        });
        window.dispatchEvent(event);
      } else {
        // If auto-accept fails, don't show notification when cart is full
        console.log('❌ Auto-accept failed due to max limit reached');
        // No notification shown to avoid bothering the user when cart is full
      }

      // ALWAYS return true when auto-accept is enabled - never show popup
      return true; // Never show popup when auto-accept is enabled
    } else {
      // Auto-accept disabled, allow popup to show
      console.log('🔕 Auto-accept disabled, allowing popup to show');
      return false; // Allow popup to show
    }
  };
};

// Manual order actions - these will be created inside the composable function
const createAcceptOrder = (t: any) => {
  return async (orderId: string): Promise<boolean> => {
    const order = pendingOrders.value.find(o => o.id === orderId);
    if (!order) return false;

    stopNotificationSound();
    currentOrderPopup.value = null;

    const success = await processOrderAutomatically(order, t);
    if (success) {
      pendingOrders.value = pendingOrders.value.filter(o => o.id !== orderId);
    }

    return success;
  };
};

const rejectOrder = (orderId: string): void => {
  console.log('Order rejected:', orderId);

  stopNotificationSound();
  currentOrderPopup.value = null;

  // Remove from pending orders
  pendingOrders.value = pendingOrders.value.filter(o => o.id !== orderId);

  // Dispatch event for logging/analytics
  const order = pendingOrders.value.find(o => o.id === orderId);
  const event = new CustomEvent('orderRejected', {
    detail: { order }
  });
  window.dispatchEvent(event);
};

const closeOrderPopup = (): void => {
  stopNotificationSound();
  currentOrderPopup.value = null;
};

// Cleanup function
const cleanup = (): void => {
  stopNotificationSound();
  currentOrderPopup.value = null;
  pendingOrders.value = [];
};

// Export the composable
export default function useOrderNotifications() {
  const { t } = useI18n();

  // Create the functions with translation support
  const handleNewOrderRequest = createOrderHandler(t);
  const acceptOrder = createAcceptOrder(t);

  return {
    // State
    pendingOrders,
    currentOrderPopup,
    isProcessingOrder,

    // Computed
    hasAutoAcceptEnabled,
    isOnlineModeEnabled,
    shouldShowPopup,

    // Actions
    handleNewOrderRequest,
    acceptOrder,
    rejectOrder,
    closeOrderPopup,
    cleanup,

    // Utilities
    playNotificationSound,
    stopNotificationSound
  };
}
