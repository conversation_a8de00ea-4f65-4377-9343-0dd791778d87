import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

/**
 * Composable for RTL/LTR layout handling
 */
export function useRTL() {
  const { locale } = useI18n();

  // Check if current locale is RTL
  const isRTL = computed(() => locale.value === 'ar');

  // Get text direction
  const direction = computed(() => isRTL.value ? 'rtl' : 'ltr');

  // Get opposite direction
  const oppositeDirection = computed(() => isRTL.value ? 'ltr' : 'rtl');

  // RTL-aware positioning classes
  const positionClasses = computed(() => ({
    // Margin classes
    'ml-auto': !isRTL.value,
    'mr-auto': isRTL.value,
    'ml-0': isRTL.value,
    'mr-0': !isRTL.value,

    // Text alignment
    'text-left': !isRTL.value,
    'text-right': isRTL.value,

    // Flex direction
    'flex-row': !isRTL.value,
    'flex-row-reverse': isRTL.value,
  }));

  // Get RTL-aware margin/padding classes
  const getSpacingClass = (property: 'margin' | 'padding', side: 'left' | 'right', size: string) => {
    const prefix = property === 'margin' ? 'm' : 'p';
    const actualSide = isRTL.value ? (side === 'left' ? 'r' : 'l') : side.charAt(0);
    return `${prefix}${actualSide}-${size}`;
  };

  // Get RTL-aware border classes
  const getBorderClass = (side: 'left' | 'right', size: string = '1') => {
    const actualSide = isRTL.value ? (side === 'left' ? 'r' : 'l') : side.charAt(0);
    return `border-${actualSide}-${size}`;
  };

  // Get RTL-aware positioning classes
  const getPositionClass = (side: 'left' | 'right', size: string) => {
    const actualSide = isRTL.value ? (side === 'left' ? 'right' : 'left') : side;
    return `${actualSide}-${size}`;
  };

  // Get RTL-aware transform classes
  const getTransformClass = (direction: 'left' | 'right', size: string) => {
    const actualDirection = isRTL.value ? (direction === 'left' ? '' : '-') : (direction === 'left' ? '-' : '');
    return `translate-x${actualDirection}-${size}`;
  };

  // Get RTL-aware flex order
  const getFlexOrder = (order: number) => {
    // In RTL, we might want to reverse the order
    return isRTL.value ? `order-${order}` : `order-${order}`;
  };

  // Get RTL-aware grid column classes
  const getGridColumnClass = (start: number, end?: number) => {
    if (end) {
      return `col-span-${end - start + 1}`;
    }
    return `col-start-${start}`;
  };

  // Get icon rotation for RTL (use sparingly - prefer semantic icons)
  const getIconRotation = () => {
    return isRTL.value ? 'transform rotate-180' : '';
  };

  // Get semantic arrow icon for navigation
  const getNavigationArrow = (direction: 'next' | 'previous') => {
    if (direction === 'next') {
      return isRTL.value ? 'ChevronLeftIcon' : 'ChevronRightIcon';
    } else {
      return isRTL.value ? 'ChevronRightIcon' : 'ChevronLeftIcon';
    }
  };

  // Get semantic arrow classes for buttons
  const getArrowClasses = (direction: 'next' | 'previous') => {
    const baseClasses = 'w-4 h-4';
    const marginClass = direction === 'next'
      ? (isRTL.value ? 'mr-2' : 'ml-2')
      : (isRTL.value ? 'ml-2' : 'mr-2');
    return `${baseClasses} ${marginClass}`;
  };

  // Get dropdown positioning
  const getDropdownPosition = () => {
    return {
      'right-0': !isRTL.value,
      'left-0': isRTL.value,
      'rtl:right-auto': isRTL.value,
      'rtl:left-0': isRTL.value,
    };
  };

  // Get modal positioning
  const getModalPosition = () => {
    return {
      'text-left': !isRTL.value,
      'text-right': isRTL.value,
      'rtl:text-right': isRTL.value,
    };
  };

  // Get form input direction
  const getInputDirection = () => {
    return {
      dir: direction.value,
      class: {
        'text-left': !isRTL.value,
        'text-right': isRTL.value,
      }
    };
  };

  // Get table alignment
  const getTableAlignment = () => {
    return {
      'text-left': !isRTL.value,
      'text-right': isRTL.value,
      'rtl:text-right': isRTL.value,
    };
  };

  // Get sidebar positioning
  const getSidebarPosition = () => {
    return {
      'left-0': !isRTL.value,
      'right-0': isRTL.value,
      'rtl:left-auto': isRTL.value,
      'rtl:right-0': isRTL.value,
    };
  };

  // Get main content margin for sidebar
  const getMainContentMargin = () => {
    return {
      'ml-64': !isRTL.value,
      'mr-64': isRTL.value,
      'rtl:ml-0': isRTL.value,
      'rtl:mr-64': isRTL.value,
    };
  };

  return {
    isRTL,
    direction,
    oppositeDirection,
    positionClasses,
    getSpacingClass,
    getBorderClass,
    getPositionClass,
    getTransformClass,
    getFlexOrder,
    getGridColumnClass,
    getIconRotation,
    getNavigationArrow,
    getArrowClasses,
    getDropdownPosition,
    getModalPosition,
    getInputDirection,
    getTableAlignment,
    getSidebarPosition,
    getMainContentMargin,
  };
}
