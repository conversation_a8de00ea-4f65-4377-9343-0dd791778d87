@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {

    /* Font setup for Arabic support */
    html[lang="ar"] {
        font-family: '<PERSON><PERSON>wal', 'Inter', system-ui, sans-serif;
    }

    body {
        @apply bg-gray-100 text-gray-800 transition-colors duration-300 ease-in-out;
        @apply dark:bg-neutral-900 dark:text-neutral-100;
        @apply font-sans;
    }
}

@layer components {}

@layer utilities {

    /* RTL Support utilities */
    .rtl\:right-0 {
        right: 0;
    }

    .rtl\:left-auto {
        left: auto;
    }

    .rtl\:mr-0 {
        margin-right: 0;
    }

    .rtl\:ml-3 {
        margin-left: 0.75rem;
    }

    .rtl\:ml-4 {
        margin-left: 1rem;
    }

    .rtl\:text-right {
        text-align: right;
    }

    .rtl\:justify-start {
        justify-content: flex-start;
    }

    .rtl\:translate-x-0 {
        transform: translateX(0);
    }

    .rtl\:translate-x-full {
        transform: translateX(100%);
    }

    .rtl\:border-r-0 {
        border-right-width: 0;
    }

    .rtl\:border-l {
        border-left-width: 1px;
    }

    .rtl\:right-0\.5 {
        right: 0.125rem;
    }

    .rtl\:-translate-x-4 {
        transform: translateX(-1rem);
    }

    .rtl\:-translate-x-6 {
        transform: translateX(-1.5rem);
    }

    .rtl\:-translate-x-7 {
        transform: translateX(-1.75rem);
    }

    /* Custom utilities for specific layouts */
    .sidebar-width {
        width: 16rem;
    }

    .main-content-margin {
        margin-left: 16rem;
    }

    .rtl\:main-content-margin {
        margin-left: 0;
        margin-right: 16rem;
    }
}