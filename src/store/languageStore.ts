import { reactive, readonly, inject } from 'vue';
import { setLanguage } from '../i18n';

// Define the state interface
interface LanguageState {
  currentLanguage: string;
  availableLanguages: { code: string; name: string }[];
}

// Create a reactive state
const state = reactive<LanguageState>({
  currentLanguage: localStorage.getItem('dawinii-locale') || 'en',
  availableLanguages: [
    { code: 'en', name: 'English' },
    { code: 'ar', name: 'العربية' }
  ]
});

// Define actions to mutate the state
const actions = {
  changeLanguage(langCode: string) {
    if (langCode !== state.currentLanguage) {
      // Update state
      state.currentLanguage = langCode;

      // Update direction and language attributes
      const direction = langCode === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.setAttribute('dir', direction);
      document.documentElement.setAttribute('lang', langCode);

      // Update body class for RTL support
      if (direction === 'rtl') {
        document.body.classList.add('rtl');
      } else {
        document.body.classList.remove('rtl');
      }

      // Use the centralized setLanguage function from i18n.ts
      if (langCode === 'en' || langCode === 'ar') {
        setLanguage(langCode);
      }
    }
  },

  // Initialize language from localStorage
  initLanguage() {
    const direction = state.currentLanguage === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('dir', direction);
    document.documentElement.setAttribute('lang', state.currentLanguage);

    if (direction === 'rtl') {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
  }
};

// Create and export the store
export default {
  state: readonly(state),
  ...actions
};
