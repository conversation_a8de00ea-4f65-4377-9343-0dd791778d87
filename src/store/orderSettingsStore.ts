import { ref, computed } from 'vue';

/**
 * Order Settings Store
 * Manages order-related settings like auto-accept orders
 */

// Reactive state for order settings
const autoAcceptOrders = ref<boolean>(
  localStorage.getItem('dawinii-auto-accept-orders') === 'enabled'
);

// Computed properties
const canAutoAcceptOrders = computed(() => autoAcceptOrders.value);

// Actions
const actions = {
  /**
   * Toggle auto accept orders setting
   */
  toggleAutoAcceptOrders(): void {
    autoAcceptOrders.value = !autoAcceptOrders.value;
    console.log('Store: Auto accept orders toggled to:', autoAcceptOrders.value);

    // Save to localStorage
    localStorage.setItem('dawinii-auto-accept-orders', autoAcceptOrders.value ? 'enabled' : 'disabled');

    // Dispatch event for any listeners
    const event = new CustomEvent('autoAcceptOrdersChanged', {
      detail: { enabled: autoAcceptOrders.value }
    });
    window.dispatchEvent(event);
    console.log('Store: Auto accept orders change event dispatched');
  },

  /**
   * Set auto accept orders state directly (for initialization)
   */
  setAutoAcceptOrders(enabled: boolean): void {
    autoAcceptOrders.value = enabled;
    localStorage.setItem('dawinii-auto-accept-orders', enabled ? 'enabled' : 'disabled');
    
    // Dispatch event for any listeners
    const event = new CustomEvent('autoAcceptOrdersChanged', {
      detail: { enabled }
    });
    window.dispatchEvent(event);
  },

  /**
   * Initialize auto accept orders from localStorage
   */
  initAutoAcceptOrders(): void {
    const savedValue = localStorage.getItem('dawinii-auto-accept-orders');
    autoAcceptOrders.value = savedValue === 'enabled';
    
    // Dispatch initial event
    const event = new CustomEvent('autoAcceptOrdersChanged', {
      detail: { enabled: autoAcceptOrders.value }
    });
    window.dispatchEvent(event);
    console.log('Store: Auto accept orders initialized to:', autoAcceptOrders.value);
  }
};

// Create and export the store
export default {
  state: {
    autoAcceptOrders,
    canAutoAcceptOrders
  },
  ...actions
};
