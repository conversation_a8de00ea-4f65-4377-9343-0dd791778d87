import { ref, computed, watch } from 'vue';
import { DEFAULT_COUNTRY, DEFAULT_CURRENCY, getCountryByCode, getCurrencyByCode } from '../data/countries';
import type { Country, Currency } from '../data/countries';

// Regional settings state
interface RegionalSettingsState {
  selectedCountryCode: string;
  selectedCurrencyCode: string;
  isInitialized: boolean;
}

// Reactive state
const state = ref<RegionalSettingsState>({
  selectedCountryCode: DEFAULT_COUNTRY,
  selectedCurrencyCode: DEFAULT_CURRENCY,
  isInitialized: false
});

// Computed properties
const selectedCountry = computed((): Country | undefined => {
  return getCountryByCode(state.value.selectedCountryCode);
});

const selectedCurrency = computed((): Currency | undefined => {
  return getCurrencyByCode(state.value.selectedCurrencyCode);
});

// Storage keys
const STORAGE_KEYS = {
  COUNTRY: 'dawinii-selected-country'
} as const;

// Initialize from localStorage
const initRegionalSettings = () => {
  try {
    const savedCountry = localStorage.getItem(STORAGE_KEYS.COUNTRY);

    // Set default country first
    state.value.selectedCountryCode = DEFAULT_COUNTRY;

    // Override with saved country if it exists and is valid
    if (savedCountry && getCountryByCode(savedCountry)) {
      state.value.selectedCountryCode = savedCountry;
    }

    // Always set currency based on the selected country (currency is read-only)
    const selectedCountry = getCountryByCode(state.value.selectedCountryCode);
    if (selectedCountry) {
      state.value.selectedCurrencyCode = selectedCountry.currency;
    }

    // Save the current values to localStorage to ensure consistency
    saveToStorage();

    state.value.isInitialized = true;
    console.log('Regional settings initialized:', {
      country: state.value.selectedCountryCode,
      currency: state.value.selectedCurrencyCode,
      savedCountry,
      fullState: state.value
    });
  } catch (error) {
    console.error('Error initializing regional settings:', error);
    // Use defaults on error
    state.value.selectedCountryCode = DEFAULT_COUNTRY;
    state.value.selectedCurrencyCode = DEFAULT_CURRENCY;
    state.value.isInitialized = true;
    saveToStorage();
  }
};

// Save to localStorage
const saveToStorage = () => {
  try {
    console.log('Saving to localStorage:', state.value.selectedCountryCode);
    localStorage.setItem(STORAGE_KEYS.COUNTRY, state.value.selectedCountryCode);
    console.log('Saved successfully');
    // Currency is not saved since it's automatically derived from country
  } catch (error) {
    console.error('Error saving regional settings:', error);
  }
};

// Watch for country changes and save to storage
watch(
  () => state.value.selectedCountryCode,
  (newValue, oldValue) => {
    console.log('Country watch triggered:', { newValue, oldValue, isInitialized: state.value.isInitialized });
    if (state.value.isInitialized) {
      saveToStorage();
    }
  }
);

// Actions
const setCountry = (countryCode: string) => {
  console.log('setCountry called with:', countryCode);
  const country = getCountryByCode(countryCode);
  if (country) {
    console.log('Found country:', country);
    state.value.selectedCountryCode = countryCode;

    // Automatically set currency to match country's default currency
    state.value.selectedCurrencyCode = country.currency;

    console.log('Country changed:', {
      country: countryCode,
      currency: country.currency,
      stateAfter: state.value
    });
  } else {
    console.error('Country not found:', countryCode);
  }
};

// Remove setCurrency function since currency is now read-only and auto-set by country

// Currency formatting helper
const formatCurrency = (amount: number, options?: {
  showSymbol?: boolean;
  decimalPlaces?: number;
}): string => {
  const currency = selectedCurrency.value;
  if (!currency) return amount.toString();

  const { showSymbol = true, decimalPlaces = 2 } = options || {};
  const formattedAmount = amount.toFixed(decimalPlaces);

  if (!showSymbol) {
    return formattedAmount;
  }

  if (currency.symbolPosition === 'before') {
    return `${currency.symbol}${formattedAmount}`;
  } else {
    return `${formattedAmount} ${currency.symbol}`;
  }
};

// Export store
const regionalSettingsStore = {
  // State
  state: computed(() => state.value),

  // Computed
  selectedCountry,
  selectedCurrency,

  // Actions
  initRegionalSettings,
  setCountry,
  formatCurrency,

  // Getters
  getSelectedCountryCode: () => state.value.selectedCountryCode,
  getSelectedCurrencyCode: () => state.value.selectedCurrencyCode,
  isInitialized: computed(() => state.value.isInitialized)
};

export default regionalSettingsStore;
