import { reactive, computed } from 'vue';
import type { CartItem, CartState } from '../types/cart';

// Initialize cart state
const state = reactive<CartState>({
  items: [],
  subtotal: 0,
  tax: 0,
  total: 0
});

// Calculate cart totals
const calculateTotals = () => {
  state.subtotal = state.items.reduce((sum, item) => sum + item.totalPrice, 0);
  state.tax = 0; // Tax can be implemented later if needed
  state.total = state.subtotal + state.tax;
};

// Create cart store
export const useCartStore = () => {
  // Add item to cart
  const addToCart = (item: Omit<CartItem, 'totalPrice'>) => {
    // Calculate total price for the item
    const totalPrice = item.pricePerUnit * item.quantity;
    
    // Check if item already exists in cart
    const existingItemIndex = state.items.findIndex(cartItem => cartItem.id === item.id);
    
    if (existingItemIndex !== -1) {
      // Update existing item
      state.items[existingItemIndex] = {
        ...item,
        totalPrice
      };
    } else {
      // Add new item
      state.items.push({
        ...item,
        totalPrice
      });
    }
    
    // Recalculate totals
    calculateTotals();
  };
  
  // Update item quantity
  const updateQuantity = (id: number, quantity: number) => {
    const itemIndex = state.items.findIndex(item => item.id === id);
    
    if (itemIndex !== -1) {
      // Update quantity and total price
      state.items[itemIndex].quantity = quantity;
      state.items[itemIndex].totalPrice = state.items[itemIndex].pricePerUnit * quantity;
      
      // Recalculate totals
      calculateTotals();
    }
  };
  
  // Remove item from cart
  const removeFromCart = (id: number) => {
    state.items = state.items.filter(item => item.id !== id);
    
    // Recalculate totals
    calculateTotals();
  };
  
  // Clear cart
  const clearCart = () => {
    state.items = [];
    calculateTotals();
  };
  
  // Check if item is in cart
  const isInCart = (id: number) => {
    return state.items.some(item => item.id === id);
  };
  
  // Get item from cart
  const getCartItem = (id: number) => {
    return state.items.find(item => item.id === id);
  };
  
  // Computed properties
  const itemCount = computed(() => state.items.length);
  
  return {
    // State
    state,
    
    // Actions
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    isInCart,
    getCartItem,
    
    // Computed
    itemCount
  };
};

// Create a singleton instance
const cartStore = useCartStore();
export default cartStore;
