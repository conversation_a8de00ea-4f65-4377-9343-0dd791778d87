import { ref, computed } from 'vue';
import { currentThemeMode, toggleTheme as toggleThemeMode, initTheme as initThemeMode } from '../theme/themeConfig';

// Create reactive refs for better reactivity
const notificationsEnabled = ref<boolean>(
  localStorage.getItem('dawinii-notifications') !== 'disabled'
);

// Create computed property for isDarkMode that uses the centralized theme config
const isDarkMode = computed(() => currentThemeMode.value === 'dark');

// Define actions to mutate the state
const actions = {
  toggleDarkMode() {
    // Use the centralized toggle function
    toggleThemeMode();

    // Force re-render of components by adding a small delay
    setTimeout(() => {
      const event = new Event('themeChanged');
      document.dispatchEvent(event);
    }, 10);
  },

  toggleNotifications() {
    notificationsEnabled.value = !notificationsEnabled.value;
    console.log('Store: Notifications toggled to:', notificationsEnabled.value);

    // Save to localStorage
    localStorage.setItem('dawinii-notifications', notificationsEnabled.value ? 'enabled' : 'disabled');

    // Dispatch event for any listeners
    const event = new Event('notificationsChanged');
    document.dispatchEvent(event);
    console.log('Store: Notification change event dispatched');
  },

  // Initialize theme from localStorage
  initTheme() {
    // Use the centralized init function
    initThemeMode();

    // Force re-render of components
    const event = new Event('themeChanged');
    document.dispatchEvent(event);
  }
};

// Create and export the store
export default {
  state: {
    notificationsEnabled,
    isDarkMode
  },
  ...actions
};
