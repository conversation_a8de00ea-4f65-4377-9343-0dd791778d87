import { reactive, computed } from 'vue';
import type {
  OrderPack,
  OrderPackState,
  CartItem,
  OrderPackType,
  OrderPackStatus
} from '../types/cart';
import type { OrderRequest } from '../types/orderRequests';

// Initialize order pack state
const state = reactive<OrderPackState>({
  packs: [],
  currentPackId: null,
  nextLocalOrderNumber: 1
});

// Helper function to calculate pack totals
const calculatePackTotals = (pack: OrderPack) => {
  pack.subtotal = pack.items.reduce((sum, item) => sum + item.totalPrice, 0);
  pack.tax = 0; // Tax can be implemented later if needed
  pack.total = pack.subtotal + pack.tax;
  pack.updatedAt = new Date().toISOString();
};

// Helper function to generate unique pack ID
const generatePackId = (type: OrderPackType): string => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);

  switch (type) {
    case 'auto-accepted':
      return `AUTO-${timestamp}-${random}`;
    case 'local':
      return `LOCAL-${timestamp}-${random}`;
    case 'manual':
      return `MANUAL-${timestamp}-${random}`;
    default:
      return `PACK-${timestamp}-${random}`;
  }
};

// Constants
const MAX_ORDER_PACKS = 12;

// Create order pack store
export const useOrderPackStore = () => {
  // Check if we can add more packs
  const canAddMorePacks = (): boolean => {
    const activePacksCount = state.packs.filter(p => p.status === 'active').length;
    return activePacksCount < MAX_ORDER_PACKS;
  };

  // Create a new order pack from auto-accepted order
  const createAutoAcceptedPack = (orderRequest: OrderRequest): OrderPack => {
    const now = new Date().toISOString();
    const packId = generatePackId('auto-accepted');

    // Calculate expiry time (6 hours for reservations)
    let expiryTime: string | undefined;
    if (orderRequest.serviceType === 'Reserved') {
      const expiry = new Date();
      expiry.setHours(expiry.getHours() + 6);
      expiryTime = expiry.toISOString();
    }

    const pack: OrderPack = {
      id: packId,
      type: 'auto-accepted',
      status: 'active',
      items: [],
      subtotal: 0,
      tax: 0,
      total: 0,
      createdAt: now,
      updatedAt: now,
      userInfo: {
        userId: orderRequest.userId,
        userName: orderRequest.userName,
        userPhone: orderRequest.userPhone,
        serviceType: orderRequest.serviceType,
        location: orderRequest.location,
        pointsToEarn: orderRequest.pointsToEarn,
        orderRequestId: orderRequest.id
      },
      expiryTime
    };

    // Add medications from order request
    orderRequest.medications.forEach(medication => {
      const cartItem: CartItem = {
        id: medication.medicationId,
        name: medication.name,
        barcode: `MED-${medication.medicationId}`,
        type: 'medication' as any,
        quantity: medication.quantity,
        pricePerUnit: medication.unitPrice || 0,
        discount: { hasDiscount: false },
        totalPrice: (medication.unitPrice || 0) * medication.quantity
      };
      pack.items.push(cartItem);
    });

    calculatePackTotals(pack);
    return pack;
  };

  // Create a new local order pack
  const createLocalPack = (): OrderPack => {
    const now = new Date().toISOString();
    const packId = generatePackId('local');
    const orderNumber = state.nextLocalOrderNumber;

    const pack: OrderPack = {
      id: packId,
      type: 'local',
      status: 'active',
      items: [],
      subtotal: 0,
      tax: 0,
      total: 0,
      createdAt: now,
      updatedAt: now,
      localOrderInfo: {
        orderNumber,
        customerName: '',
        notes: ''
      }
    };

    state.nextLocalOrderNumber++;
    return pack;
  };

  // Add a new pack
  const addPack = (pack: OrderPack, makeActive: boolean = true) => {
    state.packs.push(pack);

    // Only make this pack active if requested and no other pack is currently active
    if (makeActive && !state.currentPackId) {
      state.currentPackId = pack.id;
    }
  };

  // Add auto-accepted order as new pack
  const addAutoAcceptedOrder = (orderRequest: OrderRequest): string | null => {
    if (!canAddMorePacks()) {
      console.warn('Cannot add more order packs. Maximum limit reached:', MAX_ORDER_PACKS);
      return null;
    }

    const pack = createAutoAcceptedPack(orderRequest);

    // Only make this pack active if there are no other active packs
    // This prevents disrupting the user's workflow when they're already working on another pack
    const hasOtherActivePacks = state.packs.some(p => p.status === 'active');
    addPack(pack, !hasOtherActivePacks);

    return pack.id;
  };

  // Add new local order pack
  const addLocalOrder = (): string | null => {
    if (!canAddMorePacks()) {
      console.warn('Cannot add more order packs. Maximum limit reached:', MAX_ORDER_PACKS);
      return null;
    }

    const pack = createLocalPack();

    // For local orders, always make them active since the user intentionally created them
    // This is different from auto-accepted orders which shouldn't disrupt ongoing work
    addPack(pack, true);
    return pack.id;
  };

  // Switch to a specific pack
  const switchToPack = (packId: string) => {
    const pack = state.packs.find(p => p.id === packId);
    if (pack && pack.status === 'active') {
      state.currentPackId = packId;
    }
  };

  // Get current active pack
  const getCurrentPack = (): OrderPack | null => {
    if (!state.currentPackId) return null;
    return state.packs.find(p => p.id === state.currentPackId && p.status === 'active') || null;
  };

  // Add item to current pack
  const addToCurrentPack = (item: Omit<CartItem, 'totalPrice'>) => {
    const currentPack = getCurrentPack();
    if (!currentPack) {
      // Create a new local pack if none exists
      addLocalOrder();
      return addToCurrentPack(item);
    }

    const totalPrice = item.pricePerUnit * item.quantity;
    const existingItemIndex = currentPack.items.findIndex(cartItem => cartItem.id === item.id);

    if (existingItemIndex !== -1) {
      // Update existing item
      currentPack.items[existingItemIndex] = {
        ...item,
        totalPrice
      };
    } else {
      // Add new item
      currentPack.items.push({
        ...item,
        totalPrice
      });
    }

    calculatePackTotals(currentPack);
  };

  // Add item to specific pack
  const addToSpecificPack = (packId: string, item: Omit<CartItem, 'totalPrice'>) => {
    const pack = state.packs.find(p => p.id === packId && p.status === 'active');
    if (!pack) {
      console.warn('Pack not found or not active:', packId);
      return;
    }

    const totalPrice = item.pricePerUnit * item.quantity;
    const existingItemIndex = pack.items.findIndex(cartItem => cartItem.id === item.id);

    if (existingItemIndex !== -1) {
      // Update existing item
      pack.items[existingItemIndex] = {
        ...item,
        totalPrice
      };
    } else {
      // Add new item
      pack.items.push({
        ...item,
        totalPrice
      });
    }

    calculatePackTotals(pack);
  };

  // Add item to new pack (creates pack and adds item)
  const addToNewPack = (item: Omit<CartItem, 'totalPrice'>): string | null => {
    const packId = addLocalOrder();
    if (packId) {
      addToSpecificPack(packId, item);
    }
    return packId;
  };

  // Update item quantity in current pack
  const updateQuantityInCurrentPack = (id: number, quantity: number) => {
    const currentPack = getCurrentPack();
    if (!currentPack) return;

    const itemIndex = currentPack.items.findIndex(item => item.id === id);
    if (itemIndex !== -1) {
      currentPack.items[itemIndex].quantity = quantity;
      currentPack.items[itemIndex].totalPrice = currentPack.items[itemIndex].pricePerUnit * quantity;
      calculatePackTotals(currentPack);
    }
  };

  // Remove item from current pack
  const removeFromCurrentPack = (id: number) => {
    const currentPack = getCurrentPack();
    if (!currentPack) return;

    currentPack.items = currentPack.items.filter(item => item.id !== id);
    calculatePackTotals(currentPack);
  };

  // Clear current pack
  const clearCurrentPack = () => {
    const currentPack = getCurrentPack();
    if (!currentPack) return;

    currentPack.items = [];
    calculatePackTotals(currentPack);
  };

  // Complete current pack (after checkout)
  const completeCurrentPack = () => {
    const currentPack = getCurrentPack();
    if (!currentPack) return;

    currentPack.status = 'completed';
    currentPack.updatedAt = new Date().toISOString();

    // Switch to next available pack or clear current
    const nextActivePack = state.packs.find(p => p.status === 'active' && p.id !== currentPack.id);
    state.currentPackId = nextActivePack?.id || null;
  };

  // Cancel/remove a pack
  const cancelPack = (packId: string) => {
    const packIndex = state.packs.findIndex(p => p.id === packId);
    if (packIndex !== -1) {
      state.packs[packIndex].status = 'cancelled';

      // If this was the current pack, switch to another
      if (state.currentPackId === packId) {
        const nextActivePack = state.packs.find(p => p.status === 'active' && p.id !== packId);
        state.currentPackId = nextActivePack?.id || null;
      }
    }
  };

  // Check if item is in current pack
  const isInCurrentPack = (id: number): boolean => {
    const currentPack = getCurrentPack();
    return currentPack ? currentPack.items.some(item => item.id === id) : false;
  };

  // Get item from current pack
  const getCurrentPackItem = (id: number): CartItem | undefined => {
    const currentPack = getCurrentPack();
    return currentPack ? currentPack.items.find(item => item.id === id) : undefined;
  };

  // Computed properties
  const activePacks = computed(() => state.packs.filter(p => p.status === 'active'));
  const activePackCount = computed(() => activePacks.value.length);
  const currentPackItemCount = computed(() => getCurrentPack()?.items.length || 0);
  const hasActivePacks = computed(() => activePackCount.value > 0);
  const canAddPacks = computed(() => activePackCount.value < MAX_ORDER_PACKS);
  const maxPacksReached = computed(() => activePackCount.value >= MAX_ORDER_PACKS);

  return {
    // State
    state,

    // Pack management
    addAutoAcceptedOrder,
    addLocalOrder,
    switchToPack,
    getCurrentPack,
    completeCurrentPack,
    cancelPack,

    // Item management
    addToCurrentPack,
    addToSpecificPack,
    addToNewPack,
    updateQuantityInCurrentPack,
    removeFromCurrentPack,
    clearCurrentPack,
    isInCurrentPack,
    getCurrentPackItem,

    // Computed
    activePacks,
    activePackCount,
    currentPackItemCount,
    hasActivePacks,
    canAddPacks,
    maxPacksReached,

    // Utility functions
    canAddMorePacks
  };
};

// Create a singleton instance
const orderPackStore = useOrderPackStore();
export default orderPackStore;
