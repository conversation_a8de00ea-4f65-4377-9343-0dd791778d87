import { ref, computed } from 'vue';
import { getNetworkStatus, checkInternetConnectivity, addNetworkStatusListener } from '../utils/networkUtils';

// Create reactive refs for online mode state
const onlineModeEnabled = ref<boolean>(
  localStorage.getItem('dawinii-online-mode') !== 'disabled'
);

const isTogglingOnlineMode = ref<boolean>(false);

// Get network status from utilities
const { isOnline } = getNetworkStatus();

// Computed property to determine if online features should be available
const canUseOnlineFeatures = computed(() => {
  return onlineModeEnabled.value && isOnline.value;
});

// Computed property to determine if we should show offline banner
const shouldShowOfflineBanner = computed(() => {
  return onlineModeEnabled.value && !isOnline.value;
});

// Define actions to mutate the state
const actions = {
  /**
   * Toggle online mode on/off
   */
  async toggleOnlineMode(): Promise<{ success: boolean; message?: string }> {
    const newValue = !onlineModeEnabled.value;
    
    // If trying to enable online mode, check internet connectivity first
    if (newValue) {
      isTogglingOnlineMode.value = true;
      
      try {
        const hasInternet = await checkInternetConnectivity();
        
        if (!hasInternet) {
          isTogglingOnlineMode.value = false;
          return {
            success: false,
            message: 'network.cannotEnableOnlineMode'
          };
        }
      } catch (error) {
        isTogglingOnlineMode.value = false;
        return {
          success: false,
          message: 'network.cannotEnableOnlineMode'
        };
      }
    }
    
    // Update the state
    onlineModeEnabled.value = newValue;
    
    // Save to localStorage
    localStorage.setItem('dawinii-online-mode', newValue ? 'enabled' : 'disabled');
    
    // Dispatch event for any listeners
    const event = new CustomEvent('onlineModeChanged', {
      detail: { enabled: newValue }
    });
    window.dispatchEvent(event);
    
    isTogglingOnlineMode.value = false;
    
    return {
      success: true,
      message: newValue ? 'network.onlineModeEnabled' : 'network.onlineModeDisabled'
    };
  },

  /**
   * Set online mode state directly (for initialization)
   */
  setOnlineMode(enabled: boolean): void {
    onlineModeEnabled.value = enabled;
    localStorage.setItem('dawinii-online-mode', enabled ? 'enabled' : 'disabled');
    
    // Dispatch event for any listeners
    const event = new CustomEvent('onlineModeChanged', {
      detail: { enabled }
    });
    window.dispatchEvent(event);
  },

  /**
   * Initialize online mode from localStorage
   */
  initOnlineMode(): void {
    const savedValue = localStorage.getItem('dawinii-online-mode');
    onlineModeEnabled.value = savedValue !== 'disabled';
    
    // Dispatch initial event
    const event = new CustomEvent('onlineModeChanged', {
      detail: { enabled: onlineModeEnabled.value }
    });
    window.dispatchEvent(event);
  },

  /**
   * Check if a feature requires online mode
   */
  requiresOnlineMode(featureName: string): boolean {
    const onlineFeatures = [
      'orders',
      'online-orders',
      'ratings',
      'points',
      'dawinii-services',
      'public-grid',
      'external-orders'
    ];
    
    return onlineFeatures.includes(featureName);
  },

  /**
   * Check if a feature is available based on online mode and network status
   */
  isFeatureAvailable(featureName: string): boolean {
    if (!actions.requiresOnlineMode(featureName)) {
      return true; // Local features are always available
    }
    
    return canUseOnlineFeatures.value;
  }
};

// Add network status listener to handle connectivity changes
let networkStatusCleanup: (() => void) | null = null;

const initNetworkListener = () => {
  if (networkStatusCleanup) {
    networkStatusCleanup();
  }
  
  networkStatusCleanup = addNetworkStatusListener((online: boolean) => {
    // Dispatch event when network status changes while online mode is enabled
    if (onlineModeEnabled.value) {
      const event = new CustomEvent('onlineFeatureAvailabilityChanged', {
        detail: { available: online }
      });
      window.dispatchEvent(event);
    }
  });
};

// Initialize network listener
initNetworkListener();

// Create and export the store
export default {
  state: {
    onlineModeEnabled,
    isTogglingOnlineMode,
    isOnline,
    canUseOnlineFeatures,
    shouldShowOfflineBanner
  },
  ...actions,
  
  // Cleanup function
  cleanup() {
    if (networkStatusCleanup) {
      networkStatusCleanup();
      networkStatusCleanup = null;
    }
  }
};
