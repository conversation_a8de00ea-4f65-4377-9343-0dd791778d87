import { ref, computed } from 'vue';

// Reactive network status
const isOnline = ref(navigator.onLine);
const lastOnlineCheck = ref<Date | null>(null);

// Network status listeners
const updateOnlineStatus = () => {
  isOnline.value = navigator.onLine;
  lastOnlineCheck.value = new Date();
  
  // Dispatch custom event for components to listen to
  const event = new CustomEvent('networkStatusChanged', {
    detail: { isOnline: isOnline.value, timestamp: lastOnlineCheck.value }
  });
  window.dispatchEvent(event);
};

// Add event listeners for online/offline events
window.addEventListener('online', updateOnlineStatus);
window.addEventListener('offline', updateOnlineStatus);

/**
 * Check if the device is currently online
 */
export function getNetworkStatus() {
  return {
    isOnline: computed(() => isOnline.value),
    lastOnlineCheck: computed(() => lastOnlineCheck.value)
  };
}

/**
 * Perform a more reliable network connectivity check
 * This goes beyond navigator.onLine which can be unreliable
 */
export async function checkInternetConnectivity(): Promise<boolean> {
  try {
    // Try to fetch a small resource with a timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    const response = await fetch('https://www.google.com/favicon.ico', {
      method: 'HEAD',
      mode: 'no-cors',
      signal: controller.signal,
      cache: 'no-cache'
    });
    
    clearTimeout(timeoutId);
    
    // Update our internal status
    isOnline.value = true;
    lastOnlineCheck.value = new Date();
    
    return true;
  } catch (error) {
    // Update our internal status
    isOnline.value = false;
    lastOnlineCheck.value = new Date();
    
    return false;
  }
}

/**
 * Add a listener for network status changes
 */
export function addNetworkStatusListener(callback: (isOnline: boolean) => void) {
  const handler = (event: CustomEvent) => {
    callback(event.detail.isOnline);
  };
  
  window.addEventListener('networkStatusChanged', handler as EventListener);
  
  // Return cleanup function
  return () => {
    window.removeEventListener('networkStatusChanged', handler as EventListener);
  };
}

/**
 * Initialize network status monitoring
 */
export function initNetworkMonitoring() {
  // Initial check
  updateOnlineStatus();
  
  // Periodic connectivity check (every 30 seconds when online)
  setInterval(async () => {
    if (isOnline.value) {
      await checkInternetConnectivity();
    }
  }, 30000);
}

/**
 * Clean up network monitoring
 */
export function cleanupNetworkMonitoring() {
  window.removeEventListener('online', updateOnlineStatus);
  window.removeEventListener('offline', updateOnlineStatus);
}
