import { useI18n } from 'vue-i18n';

/**
 * Utility functions for locale-aware formatting
 */

/**
 * Format currency based on current locale
 */
export const formatCurrency = (amount: number, locale?: string): string => {
  const currentLocale = locale || (useI18n().locale.value as string);

  // Define currency settings based on locale
  const currencySettings = {
    en: { currency: 'EGP', locale: 'en-EG' },
    ar: { currency: 'EGP', locale: 'ar-EG' }
  };

  const settings = currencySettings[currentLocale as keyof typeof currencySettings] || currencySettings.en;

  return new Intl.NumberFormat(settings.locale, {
    style: 'currency',
    currency: settings.currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

/**
 * Format date based on current locale
 */
export const formatDate = (date: string | Date | null | undefined, locale?: string): string => {
  if (!date) return '-';

  try {
    const currentLocale = locale || (useI18n().locale.value as string);
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatDate:', date);
      return '-';
    }

    const localeSettings = {
      en: 'en-US',
      ar: 'ar-EG'
    };

    const targetLocale = localeSettings[currentLocale as keyof typeof localeSettings] || localeSettings.en;

    return dateObj.toLocaleDateString(targetLocale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    console.warn('Error formatting date:', error, 'Date:', date);
    return '-';
  }
};

/**
 * Format time based on current locale
 */
export const formatTime = (date: string | Date | null | undefined, locale?: string): string => {
  if (!date) return '-';

  try {
    const currentLocale = locale || (useI18n().locale.value as string);
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatTime:', date);
      return '-';
    }

    const localeSettings = {
      en: 'en-US',
      ar: 'ar-EG'
    };

    const targetLocale = localeSettings[currentLocale as keyof typeof localeSettings] || localeSettings.en;

    return dateObj.toLocaleTimeString(targetLocale, {
      hour: '2-digit',
      minute: '2-digit',
      hour12: currentLocale === 'en'
    });
  } catch (error) {
    console.warn('Error formatting time:', error, 'Date:', date);
    return '-';
  }
};

/**
 * Format numbers based on current locale
 */
export const formatNumber = (number: number, locale?: string): string => {
  const currentLocale = locale || (useI18n().locale.value as string);

  const localeSettings = {
    en: 'en-US',
    ar: 'ar-EG'
  };

  const targetLocale = localeSettings[currentLocale as keyof typeof localeSettings] || localeSettings.en;

  return new Intl.NumberFormat(targetLocale).format(number);
};

/**
 * Get text direction based on locale
 */
export const getTextDirection = (locale?: string): 'ltr' | 'rtl' => {
  const currentLocale = locale || (useI18n().locale.value as string);
  return currentLocale === 'ar' ? 'rtl' : 'ltr';
};

/**
 * Format relative time (e.g., "2 hours", "30 minutes")
 */
export const formatRelativeTime = (targetTime: string, locale?: string): string => {
  const currentLocale = locale || (useI18n().locale.value as string);
  const now = new Date();
  const target = new Date(targetTime);
  const diffMs = target.getTime() - now.getTime();

  if (diffMs <= 0) {
    return currentLocale === 'ar' ? 'منتهي الصلاحية' : 'Expired';
  }

  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays > 0) {
    return currentLocale === 'ar'
      ? `${diffDays} ${diffDays === 1 ? 'يوم' : 'أيام'}`
      : `${diffDays} ${diffDays === 1 ? 'day' : 'days'}`;
  } else if (diffHours > 0) {
    return currentLocale === 'ar'
      ? `${diffHours} ${diffHours === 1 ? 'ساعة' : 'ساعات'}`
      : `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'}`;
  } else {
    return currentLocale === 'ar'
      ? `${diffMinutes} ${diffMinutes === 1 ? 'دقيقة' : 'دقائق'}`
      : `${diffMinutes} ${diffMinutes === 1 ? 'minute' : 'minutes'}`;
  }
};

/**
 * Get appropriate font family based on locale
 */
export const getFontFamily = (locale?: string): string => {
  const currentLocale = locale || (useI18n().locale.value as string);
  return currentLocale === 'ar' ? 'Tajawal, Inter, system-ui, sans-serif' : 'Inter, Tajawal, system-ui, sans-serif';
};

/**
 * Check if current locale is RTL
 */
export const isRTL = (locale?: string): boolean => {
  return getTextDirection(locale) === 'rtl';
};

/**
 * Get locale-specific CSS classes
 */
export const getLocaleClasses = (locale?: string): string[] => {
  const currentLocale = locale || (useI18n().locale.value as string);
  const classes: string[] = [];

  if (currentLocale === 'ar') {
    classes.push('rtl', 'font-arabic');
  } else {
    classes.push('ltr');
  }

  return classes;
};
