import { ref, computed, type Ref, type ComputedRef } from 'vue';

/**
 * Cache for memoized functions
 */
const memoCache = new Map<string, any>();

/**
 * Memoize a function with a cache key
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  keyFn?: (...args: Parameters<T>) => string
): T {
  return ((...args: Parameters<T>) => {
    const key = keyFn ? keyFn(...args) : JSON.stringify(args);
    
    if (memoCache.has(key)) {
      return memoCache.get(key);
    }
    
    const result = fn(...args);
    memoCache.set(key, result);
    
    return result;
  }) as T;
}

/**
 * Clear memoization cache
 */
export function clearMemoCache(pattern?: string): void {
  if (pattern) {
    const regex = new RegExp(pattern);
    for (const [key] of memoCache) {
      if (regex.test(key)) {
        memoCache.delete(key);
      }
    }
  } else {
    memoCache.clear();
  }
}

/**
 * Debounced computed property
 */
export function debouncedComputed<T>(
  fn: () => T,
  delay: number = 300
): ComputedRef<T> {
  const result = ref<T>();
  let timeoutId: number | null = null;
  
  return computed(() => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = window.setTimeout(() => {
      result.value = fn();
    }, delay);
    
    return result.value as T;
  });
}

/**
 * Throttled function
 */
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): T {
  let lastCall = 0;
  
  return ((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastCall >= delay) {
      lastCall = now;
      return fn(...args);
    }
  }) as T;
}

/**
 * Virtual scrolling helper
 */
export interface VirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}

export function useVirtualScroll<T>(
  items: Ref<T[]>,
  options: VirtualScrollOptions
) {
  const scrollTop = ref(0);
  const { itemHeight, containerHeight, overscan = 5 } = options;
  
  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight);
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight) + overscan,
      items.value.length
    );
    
    return {
      start: Math.max(0, start - overscan),
      end
    };
  });
  
  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value;
    return items.value.slice(start, end).map((item, index) => ({
      item,
      index: start + index
    }));
  });
  
  const totalHeight = computed(() => items.value.length * itemHeight);
  
  const offsetY = computed(() => visibleRange.value.start * itemHeight);
  
  return {
    scrollTop,
    visibleItems,
    totalHeight,
    offsetY,
    visibleRange
  };
}

/**
 * Performance monitoring
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private marks = new Map<string, number>();
  
  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }
  
  mark(name: string): void {
    this.marks.set(name, performance.now());
  }
  
  measure(name: string, startMark: string): number {
    const startTime = this.marks.get(startMark);
    if (!startTime) {
      console.warn(`Start mark "${startMark}" not found`);
      return 0;
    }
    
    const duration = performance.now() - startTime;
    console.log(`${name}: ${duration.toFixed(2)}ms`);
    return duration;
  }
  
  measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    
    return fn().then(result => {
      const duration = performance.now() - startTime;
      console.log(`${name}: ${duration.toFixed(2)}ms`);
      return result;
    });
  }
}

/**
 * Lazy loading helper
 */
export function useLazyLoading(threshold = 0.1) {
  const isVisible = ref(false);
  const target = ref<HTMLElement>();
  
  const observer = new IntersectionObserver(
    ([entry]) => {
      isVisible.value = entry.isIntersecting;
    },
    { threshold }
  );
  
  const observe = (element: HTMLElement) => {
    target.value = element;
    observer.observe(element);
  };
  
  const unobserve = () => {
    if (target.value) {
      observer.unobserve(target.value);
    }
  };
  
  return {
    isVisible,
    observe,
    unobserve
  };
}

/**
 * Memory usage monitoring
 */
export function getMemoryUsage(): {
  used: number;
  total: number;
  percentage: number;
} | null {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      used: Math.round(memory.usedJSHeapSize / 1048576), // MB
      total: Math.round(memory.totalJSHeapSize / 1048576), // MB
      percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
    };
  }
  return null;
}

/**
 * Bundle size analyzer
 */
export function analyzeBundleSize(): void {
  if (process.env.NODE_ENV === 'development') {
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
    
    console.group('Bundle Analysis');
    console.log('Scripts:', scripts.length);
    console.log('Stylesheets:', styles.length);
    
    scripts.forEach((script: any) => {
      console.log(`Script: ${script.src}`);
    });
    
    styles.forEach((style: any) => {
      console.log(`Stylesheet: ${style.href}`);
    });
    
    console.groupEnd();
  }
}

/**
 * Component render tracking
 */
export function trackRender(componentName: string) {
  if (process.env.NODE_ENV === 'development') {
    const monitor = PerformanceMonitor.getInstance();
    monitor.mark(`${componentName}-render-start`);
    
    return () => {
      monitor.measure(`${componentName} render`, `${componentName}-render-start`);
    };
  }
  
  return () => {};
}
