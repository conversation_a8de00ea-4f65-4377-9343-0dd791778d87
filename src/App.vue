<script setup lang="ts">
import { ref, onMounted, onErrorCaptured, provide, watch } from "vue";
import { useRouter } from "vue-router";
import ThemeProvider from "./theme/ThemeProvider.vue";
import LoadingOverlay from "./components/LoadingOverlay.vue";
import { initTheme, currentTheme } from "./theme/themeConfig";
import themeStore from "./store/themeStore";
import languageStore from "./store/languageStore";
import { initNetworkMonitoring } from "./utils/networkUtils";

// Initialize theme and language
onMounted(() => {
  try {
    // Initialize theme from centralized config
    initTheme();

    // Apply language direction
    const savedLocale = localStorage.getItem('dawinii-locale') || 'en';
    const direction = savedLocale === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('dir', direction);
    document.documentElement.setAttribute('lang', savedLocale);

    // Initialize stores
    themeStore.initTheme();
    languageStore.initLanguage();

    // Initialize network monitoring
    initNetworkMonitoring();

    console.log("Theme, language, and network monitoring initialized");
  } catch (error) {
    console.error("Error initializing theme or language:", error);
  }
});

// Error handling
const appError = ref<Error | null>(null);
onErrorCaptured((err) => {
  console.error("Application error:", err);
  appError.value = err as Error;
  return false; // prevent error from propagating
});

// Get router instance
const router = useRouter();

// User state
const isLoggedIn = ref(false);
const currentUser = ref<{
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
} | null>(null);

// Check if user is logged in on mount
onMounted(() => {
  const token = localStorage.getItem('dawinii-auth-token');
  if (token) {
    try {
      const userData = JSON.parse(localStorage.getItem('dawinii-user-data') || '{}');
      currentUser.value = {
        userId: userData.userId || '',
        role: userData.role || 'PharmacyUser',
        email: userData.email,
        deviceId: userData.deviceId,
        token: token
      };
      isLoggedIn.value = true;
    } catch (error) {
      console.error('Error parsing user data:', error);
      localStorage.removeItem('dawinii-auth-token');
      localStorage.removeItem('dawinii-user-data');
    }
  }
});

// Only redirect after explicit login/logout actions, not on every refresh.

// Handle successful login
const handleLoginSuccess = (userData: {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}) => {
  currentUser.value = userData;
  isLoggedIn.value = true;

  // Store auth data
  localStorage.setItem('dawinii-auth-token', userData.token);
  localStorage.setItem('dawinii-user-data', JSON.stringify(userData));

  // Redirect to dashboard ONLY after explicit login
  router.push('/dashboard');
  console.log('User logged in:', userData);
};

// Handle logout
const handleLogout = () => {
  currentUser.value = null;
  isLoggedIn.value = false;

  // Clear auth data
  localStorage.removeItem('dawinii-auth-token');
  localStorage.removeItem('dawinii-user-data');
  // Redirect to login page
  router.push('/login');
};

// Global loading state for language change
const isChangingLanguage = ref(false);
const languageChangeMessage = ref('');

// Provide loading state to child components
provide('isChangingLanguage', isChangingLanguage);
provide('setLanguageLoading', (loading: boolean, message?: string) => {
  isChangingLanguage.value = loading;
  languageChangeMessage.value = message || '';
});
</script>

<template>
  <ThemeProvider>
    <div class="w-full">
      <!-- Error display -->
      <div v-if="appError"
        class="fixed inset-0 bg-black bg-opacity-80 flex flex-col items-center justify-center p-8 text-white z-[9999] text-center">
        <h2 class="text-2xl mb-4 text-error-500">{{ $t('common.error') }}</h2>
        <p class="mb-8 max-w-2xl">{{ appError.message }}</p>
        <button @click="appError = null"
          class="inline-flex items-center gap-2 px-4 py-2.5 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-red-600">{{
            $t('common.dismiss') }}</button>
      </div>

      <!-- Main app content -->
      <div v-else>
        <!-- Router view will handle showing the correct component based on the URL -->
        <router-view :key="$route.fullPath" :user="currentUser" :is-logged-in="isLoggedIn"
          @login-success="handleLoginSuccess" @logout="handleLogout" />
      </div>

      <!-- Language change loading overlay -->
      <LoadingOverlay v-if="isChangingLanguage" :message="languageChangeMessage" />
    </div>
  </ThemeProvider>
</template>
