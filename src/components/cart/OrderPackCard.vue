<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { 
  UserIcon, 
  PhoneIcon, 
  MapPinIcon, 
  ClockIcon,
  StarIcon,
  ShoppingBagIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline';
import type { OrderPack } from '../../types/cart';
import { formatCurrency, formatRelativeTime } from '../../utils/localeUtils';

interface Props {
  pack: OrderPack;
  isActive?: boolean;
}

interface Emits {
  (e: 'select', packId: string): void;
  (e: 'cancel', packId: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false
});

const emit = defineEmits<Emits>();
const { t } = useI18n();

// Computed properties
const packTitle = computed(() => {
  switch (props.pack.type) {
    case 'auto-accepted':
      return props.pack.userInfo?.userName || t('cart.orderPacks.autoAcceptedOrder');
    case 'local':
      return t('cart.orderPacks.localOrder', { number: props.pack.localOrderInfo?.orderNumber });
    case 'manual':
      return t('cart.orderPacks.manualOrder');
    default:
      return t('cart.orderPacks.unknownOrder');
  }
});

const packSubtitle = computed(() => {
  if (props.pack.type === 'auto-accepted' && props.pack.userInfo) {
    return t('cart.orderPacks.serviceType.' + props.pack.userInfo.serviceType.toLowerCase());
  }
  return '';
});

const itemCount = computed(() => props.pack.items.length);

const isExpiringSoon = computed(() => {
  if (!props.pack.expiryTime) return false;
  const now = new Date();
  const expiry = new Date(props.pack.expiryTime);
  const hoursLeft = (expiry.getTime() - now.getTime()) / (1000 * 60 * 60);
  return hoursLeft <= 1 && hoursLeft > 0;
});

const isExpired = computed(() => {
  if (!props.pack.expiryTime) return false;
  const now = new Date();
  const expiry = new Date(props.pack.expiryTime);
  return now > expiry;
});

const timeRemaining = computed(() => {
  if (!props.pack.expiryTime) return '';
  return formatRelativeTime(props.pack.expiryTime);
});

// Event handlers
const handleSelect = () => {
  if (!isExpired.value) {
    emit('select', props.pack.id);
  }
};

const handleCancel = (event: Event) => {
  event.stopPropagation();
  emit('cancel', props.pack.id);
};
</script>

<template>
  <div 
    @click="handleSelect"
    class="relative bg-white dark:bg-slate-800 border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md"
    :class="{
      'border-primary-500 ring-2 ring-primary-200 dark:ring-primary-800': isActive,
      'border-gray-200 dark:border-slate-700 hover:border-primary-300 dark:hover:border-primary-600': !isActive && !isExpired,
      'border-error-300 dark:border-error-700 bg-error-50 dark:bg-error-900/20 cursor-not-allowed': isExpired,
      'border-warning-300 dark:border-warning-700 bg-warning-50 dark:bg-warning-900/20': isExpiringSoon && !isExpired
    }"
  >
    <!-- Cancel Button -->
    <button
      @click="handleCancel"
      class="absolute top-2 right-2 rtl:right-auto rtl:left-2 p-1 rounded-full text-gray-400 hover:text-error-500 hover:bg-error-50 dark:hover:bg-error-900/20 transition-colors duration-200"
      :aria-label="t('cart.orderPacks.cancelPack')"
    >
      <XMarkIcon class="w-4 h-4" />
    </button>

    <!-- Pack Header -->
    <div class="mb-3 pr-8 rtl:pr-0 rtl:pl-8">
      <div class="flex items-center gap-2 mb-1">
        <!-- Pack Type Icon -->
        <UserIcon v-if="pack.type === 'auto-accepted'" class="w-4 h-4 text-primary-600 dark:text-primary-400" />
        <ShoppingBagIcon v-else class="w-4 h-4 text-gray-600 dark:text-gray-400" />
        
        <!-- Pack Title -->
        <h3 class="font-semibold text-gray-900 dark:text-gray-100 text-sm truncate">
          {{ packTitle }}
        </h3>
      </div>
      
      <!-- Pack Subtitle -->
      <p v-if="packSubtitle" class="text-xs text-gray-600 dark:text-gray-400">
        {{ packSubtitle }}
      </p>
    </div>

    <!-- Auto-accepted Order Details -->
    <div v-if="pack.type === 'auto-accepted' && pack.userInfo" class="space-y-2 mb-3">
      <!-- Phone -->
      <div class="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
        <PhoneIcon class="w-3 h-3" />
        <span>{{ pack.userInfo.userPhone }}</span>
      </div>
      
      <!-- Location (for delivery) -->
      <div v-if="pack.userInfo.serviceType === 'Delivery' && pack.userInfo.location" 
           class="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
        <MapPinIcon class="w-3 h-3" />
        <span class="truncate">{{ pack.userInfo.location.address }}</span>
      </div>
      
      <!-- Points -->
      <div v-if="pack.userInfo.pointsToEarn" class="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
        <StarIcon class="w-3 h-3" />
        <span>{{ t('cart.orderPacks.pointsToEarn', { points: pack.userInfo.pointsToEarn }) }}</span>
      </div>
    </div>

    <!-- Timer for Reservations -->
    <div v-if="pack.expiryTime" class="mb-3">
      <div class="flex items-center gap-2 text-xs"
           :class="{
             'text-error-600 dark:text-error-400': isExpired,
             'text-warning-600 dark:text-warning-400': isExpiringSoon && !isExpired,
             'text-gray-600 dark:text-gray-400': !isExpiringSoon && !isExpired
           }">
        <ClockIcon class="w-3 h-3" />
        <span v-if="isExpired">{{ t('cart.orderPacks.expired') }}</span>
        <span v-else-if="isExpiringSoon">{{ t('cart.orderPacks.expiringSoon') }}</span>
        <span v-else>{{ t('cart.orderPacks.expiresIn') }}</span>
        <span v-if="!isExpired">{{ timeRemaining }}</span>
      </div>
    </div>

    <!-- Pack Summary -->
    <div class="flex items-center justify-between text-sm">
      <div class="text-gray-600 dark:text-gray-400">
        {{ t('cart.orderPacks.itemCount', { count: itemCount }) }}
      </div>
      <div class="font-semibold text-gray-900 dark:text-gray-100">
        {{ formatCurrency(pack.total) }}
      </div>
    </div>

    <!-- Status Indicators -->
    <div v-if="isExpired" class="mt-2 text-xs text-error-600 dark:text-error-400 font-medium">
      {{ t('cart.orderPacks.expiredStatus') }}
    </div>
    <div v-else-if="isExpiringSoon" class="mt-2 text-xs text-warning-600 dark:text-warning-400 font-medium">
      {{ t('cart.orderPacks.expiringSoonStatus') }}
    </div>
  </div>
</template>
