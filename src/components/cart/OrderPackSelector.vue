<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { PlusIcon } from '@heroicons/vue/24/outline';
import OrderPackCard from './OrderPackCard.vue';
import type { OrderPack } from '../../types/cart';

interface Props {
  packs: OrderPack[];
  currentPackId: string | null;
  canAddMorePacks?: boolean;
  maxPacksReached?: boolean;
}

interface Emits {
  (e: 'select-pack', packId: string): void;
  (e: 'cancel-pack', packId: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  canAddMorePacks: true,
  maxPacksReached: false
});
const emit = defineEmits<Emits>();
const { t } = useI18n();

// Computed properties
const activePacks = computed(() =>
  props.packs.filter(pack => pack.status === 'active')
);

const hasActivePacks = computed(() => activePacks.value.length > 0);

// Event handlers
const handleSelectPack = (packId: string) => {
  emit('select-pack', packId);
};

const handleCancelPack = (packId: string) => {
  emit('cancel-pack', packId);
};
</script>

<template>
  <div class="space-y-4">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        {{ t('cart.orderPacks.title') }}
      </h2>
    </div>

    <!-- Pack Grid -->
    <div v-if="hasActivePacks" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <OrderPackCard v-for="pack in activePacks" :key="pack.id" :pack="pack" :is-active="pack.id === currentPackId"
        @select="handleSelectPack" @cancel="handleCancelPack" />
    </div>

    <!-- Pack Count Info -->
    <div v-if="hasActivePacks" class="text-sm text-center">
      <div :class="[
        'inline-flex items-center gap-2 px-3 py-1 rounded-full',
        maxPacksReached
          ? 'bg-error-100 dark:bg-error-900/20 text-error-700 dark:text-error-400'
          : activePacks.length >= 8
            ? 'bg-warning-100 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400'
            : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400'
      ]">
        <span>{{ t('cart.orderPacks.activePackCount', { count: activePacks.length }) }}</span>
        <span v-if="maxPacksReached" class="text-xs">({{ t('cart.orderPacks.maxReached') }})</span>
        <span v-else-if="activePacks.length >= 8" class="text-xs">({{ 12 - activePacks.length }} {{
          t('cart.orderPacks.remaining') }})</span>
      </div>
    </div>
  </div>
</template>
