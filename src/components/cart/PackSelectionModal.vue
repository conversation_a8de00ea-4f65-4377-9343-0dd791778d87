<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import Modal from '../Modal.vue';
import { PlusIcon, ShoppingBagIcon } from '@heroicons/vue/24/outline';
import type { OrderPack } from '../../types/cart';

interface Props {
  show: boolean;
  packs: OrderPack[];
  currentPackId: string | null;
  canAddMorePacks?: boolean;
  itemName?: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'select-pack', packId: string): void;
  (e: 'create-new-pack'): void;
}

const props = withDefaults(defineProps<Props>(), {
  canAddMorePacks: true,
  itemName: ''
});

const emit = defineEmits<Emits>();
const { t } = useI18n();

// Computed properties
const activePacks = computed(() =>
  props.packs.filter(pack => pack.status === 'active')
);

const currentPack = computed(() =>
  activePacks.value.find(pack => pack.id === props.currentPackId)
);

// Event handlers
const handleSelectPack = (packId: string) => {
  emit('select-pack', packId);
  emit('close');
};

const handleCreateNewPack = () => {
  emit('create-new-pack');
  emit('close');
};

const handleClose = () => {
  emit('close');
};

// Format pack display name
const getPackDisplayName = (pack: OrderPack) => {
  if (pack.type === 'local' && pack.localOrderInfo) {
    return t('cart.orderPacks.localOrder', { number: pack.localOrderInfo.orderNumber });
  } else if (pack.type === 'auto-accepted') {
    return pack.userInfo?.userName || t('cart.orderPacks.autoAcceptedOrder');
  } else if (pack.type === 'manual') {
    return t('cart.orderPacks.manualOrder');
  }
  return t('cart.orderPacks.unknownOrder');
};

// Format pack item count
const getPackItemCount = (pack: OrderPack) => {
  const count = pack.items.length;
  return t('cart.orderPacks.itemCount', { count }, count);
};
</script>

<template>
  <Modal :show="show" :title="t('cart.orderPacks.packSelection')" size="md" @close="handleClose">
    <div class="space-y-6">
      <!-- Description -->
      <div class="text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          {{ t('cart.orderPacks.selectPackToAdd') }}
        </p>
        <p v-if="itemName" class="text-sm font-medium text-gray-900 dark:text-gray-100 mt-1">
          "{{ itemName }}"
        </p>
      </div>

      <!-- Current Pack (if exists) -->
      <div v-if="currentPack" class="space-y-3">
        <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">
          {{ t('cart.orderPacks.addToCurrentPack') }}
        </h3>
        <button @click="handleSelectPack(currentPack.id)"
          class="w-full p-4 bg-primary-50 dark:bg-primary-900/20 border-2 border-primary-200 dark:border-primary-800 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/30 transition-colors duration-200 text-start">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="flex items-center justify-center w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-lg">
                <ShoppingBagIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-gray-100">
                  {{ getPackDisplayName(currentPack) }}
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {{ getPackItemCount(currentPack) }}
                </p>
              </div>
            </div>
            <div class="text-sm font-medium text-primary-600 dark:text-primary-400">
              {{ t('common.current') }}
            </div>
          </div>
        </button>
      </div>

      <!-- Other Packs -->
      <div v-if="activePacks.length > 1" class="space-y-3">
        <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">
          {{ t('cart.orderPacks.selectPack') }}
        </h3>
        <div class="space-y-2 max-h-48 overflow-y-auto">
          <button v-for="pack in activePacks.filter(p => p.id !== currentPackId)" :key="pack.id"
            @click="handleSelectPack(pack.id)"
            class="w-full p-3 bg-gray-50 dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-200 text-start">
            <div class="flex items-center gap-3">
              <div class="flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-slate-600 rounded-lg">
                <ShoppingBagIcon class="w-4 h-4 text-gray-600 dark:text-gray-400" />
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-gray-100">
                  {{ getPackDisplayName(pack) }}
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {{ getPackItemCount(pack) }}
                </p>
              </div>
            </div>
          </button>
        </div>
      </div>

      <!-- Create New Pack -->
      <div class="space-y-3">
        <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">
          {{ t('cart.orderPacks.addToNewPack') }}
        </h3>
        <button @click="handleCreateNewPack" :disabled="!canAddMorePacks" :class="[
          'w-full p-4 border-2 border-dashed rounded-lg transition-colors duration-200 text-start',
          canAddMorePacks
            ? 'border-gray-300 dark:border-slate-600 hover:border-primary-400 dark:hover:border-primary-500 hover:bg-gray-50 dark:hover:bg-slate-800'
            : 'border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800 cursor-not-allowed opacity-50'
        ]">
          <div class="flex items-center gap-3">
            <div class="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-slate-700 rounded-lg">
              <PlusIcon class="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-gray-100">
                {{ t('cart.orderPacks.createNewPack') }}
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ t('cart.orderPacks.createLocalOrder') }}
              </p>
            </div>
          </div>
        </button>
      </div>
    </div>
  </Modal>
</template>
