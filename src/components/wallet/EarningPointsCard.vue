<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});
import { useTheme } from '../../theme/useTheme';
import {
  CheckCircleIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  PlusCircleIcon,
  ShoppingCartIcon,
  StarIcon,
  BuildingStorefrontIcon,
  BeakerIcon,
  UserPlusIcon,
  ChartBarIcon,
  CogIcon
} from '@heroicons/vue/24/outline';

// Setup i18n for translations
const { t } = useI18n();

// Get theme utilities
const { isDarkMode } = useTheme();

// State for collapsible section
const isMedicationListExpanded = ref(false);

// Toggle medication list expansion
const toggleMedicationList = () => {
  isMedicationListExpanded.value = !isMedicationListExpanded.value;
};

// Mock data for earning methods
const earningMethods = ref([
  {
    id: 1,
    title: 'Register a new pharmacy',
    points: 50,
    completed: true,
    icon: BuildingStorefrontIcon,
  },
  {
    id: 2,
    title: 'Sell 1000 medications through the Dawinii app',
    points: 100,
    completed: false,
    icon: ShoppingCartIcon,
  },
  {
    id: 3,
    title: 'Achieve 4.5+ customer rating',
    points: 75,
    completed: true,
    icon: StarIcon,
  },
  {
    id: 4,
    title: 'Order highly needed medications',
    points: null, // No specific points, depends on medications
    completed: false,
    icon: BeakerIcon,
    expandable: true,
    medications: [
      { name: 'Paracetamol', points: 15, needed: 'High' },
      { name: 'Insulin', points: 25, needed: 'Critical' },
      { name: 'Amoxicillin', points: 10, needed: 'Medium' }
    ]
  }
]);

// Optional extra missions
const extraMissions = ref([
  {
    id: 5,
    title: 'Refer another pharmacy to Dawinii',
    points: 75,
    completed: false,
    icon: UserPlusIcon,
  },
  {
    id: 6,
    title: 'Reach monthly sales target',
    points: 100,
    completed: false,
    icon: ChartBarIcon,
  },
  {
    id: 7,
    title: 'Enable Dawinii auto-inventory system',
    points: 50,
    completed: false,
    icon: CogIcon,
  }
]);

// Animation state
const isCardVisible = ref(false);
let animationTimeout: number | null = null;

// Set card to visible after component is mounted
onMounted(() => {
  animationTimeout = window.setTimeout(() => {
    isCardVisible.value = true;
  }, 300);
});

// Cleanup timeout on unmount
onBeforeUnmount(() => {
  if (animationTimeout) {
    clearTimeout(animationTimeout);
  }
});
</script>

<template>
  <div class="mb-6">
    <!-- Loading Skeleton -->
    <div v-if="props.loading" class="animate-pulse">
      <div class="bg-gray-50 dark:bg-neutral-800 rounded-lg shadow-sm p-4 sm:p-6">
        <!-- Header Skeleton -->
        <div class="mb-6">
          <div class="flex items-center gap-2.5">
            <div class="w-9 h-9 bg-gray-200 dark:bg-neutral-700 rounded-lg"></div>
            <div>
              <div class="w-32 h-5 bg-gray-200 dark:bg-neutral-700 rounded mb-1"></div>
              <div class="w-48 h-3 bg-gray-200 dark:bg-neutral-700 rounded"></div>
            </div>
          </div>
        </div>

        <!-- Methods Skeleton -->
        <div class="space-y-4 mb-8">
          <div v-for="i in 3" :key="i" class="bg-gray-100 dark:bg-neutral-700 rounded-xl p-3">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gray-200 dark:bg-neutral-600 rounded-xl"></div>
              <div class="flex-1">
                <div class="w-40 h-4 bg-gray-200 dark:bg-neutral-600 rounded mb-2"></div>
                <div class="w-20 h-3 bg-gray-200 dark:bg-neutral-600 rounded"></div>
              </div>
              <div class="w-16 h-6 bg-gray-200 dark:bg-neutral-600 rounded-full"></div>
            </div>
          </div>
        </div>

        <!-- Extra Missions Skeleton -->
        <div>
          <div class="flex items-center gap-2 mb-4">
            <div class="w-0.5 h-6 bg-gray-300 dark:bg-neutral-600 rounded-full"></div>
            <div class="w-24 h-4 bg-gray-200 dark:bg-neutral-700 rounded"></div>
            <div class="flex-1 h-px bg-gray-200 dark:bg-neutral-700"></div>
          </div>
          <div class="space-y-3">
            <div v-for="i in 2" :key="i" class="bg-gray-100 dark:bg-neutral-700 rounded-xl p-3">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gray-200 dark:bg-neutral-600 rounded-xl"></div>
                <div class="flex-1">
                  <div class="w-36 h-4 bg-gray-200 dark:bg-neutral-600 rounded mb-2"></div>
                  <div class="w-18 h-3 bg-gray-200 dark:bg-neutral-600 rounded"></div>
                </div>
                <div class="w-16 h-6 bg-gray-200 dark:bg-neutral-600 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actual Card -->
    <div v-else class="opacity-0 transform translate-y-3 transition-all duration-500 ease-out text-start"
      :class="{ 'opacity-100 translate-y-0': isCardVisible }">
      <div
        class="bg-gradient-to-br from-white to-gray-50/50 dark:from-slate-800 dark:to-slate-900/50 rounded-lg shadow-sm p-4 sm:p-6 hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
        <!-- Card Header -->
        <div class="mb-6">
          <div class="flex items-center gap-2.5">
            <div class="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-lg">
              <PlusCircleIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
            </div>
            <div>
              <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100">{{ t('wallet.earnMorePoints') }}</h3>
              <p class="text-xs text-gray-600 dark:text-gray-400 mt-0.5">{{ t('wallet.completeTasksToEarnPoints') }}</p>
            </div>
          </div>
        </div>

        <!-- Card Content -->
        <div>
          <!-- Main earning methods -->
          <ul class="space-y-4">
            <li v-for="method in earningMethods" :key="method.id"
              class="group relative bg-white dark:bg-slate-800 rounded-xl border border-gray-200 dark:border-slate-700 overflow-hidden transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg"
              :class="{
                'bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-800': method.completed,
                'hover:border-primary-300 dark:hover:border-primary-600': !method.completed
              }">
              <!-- Completion indicator -->
              <div v-if="method.completed"
                class="absolute top-0 left-0 w-full h-0.5 bg-primary-400 dark:bg-primary-500">
              </div>

              <div class="flex items-center p-3 sm:p-4 gap-2 sm:gap-3 cursor-default"
                :class="{ 'cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700/50': method.expandable }"
                @click="method.expandable ? toggleMedicationList() : null">
                <!-- Method Icon -->
                <div
                  class="relative flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-xl flex-shrink-0 transition-all duration-300"
                  :class="method.completed
                    ? 'bg-primary-200 dark:bg-primary-700'
                    : 'bg-primary-100 dark:bg-primary-900/30'">
                  <component :is="method.icon" class="w-4 h-4 sm:w-5 sm:h-5 text-primary-600 dark:text-primary-400" />
                  <CheckCircleIcon v-if="method.completed"
                    class="absolute -top-0.5 -right-0.5 w-3 h-3 sm:w-4 sm:h-4 text-primary-600 dark:text-primary-400 bg-white dark:bg-slate-800 rounded-full p-0.5" />
                </div>

                <!-- Method Info -->
                <div class="flex-1 min-w-0">
                  <div class="font-semibold text-gray-900 dark:text-gray-100 mb-1 text-xs sm:text-sm truncate">{{
                    method.title }}</div>
                  <div v-if="method.points" class="flex items-center gap-2">
                    <div
                      class="px-2 py-0.5 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 text-xs font-medium rounded-full">
                      +{{ method.points }} {{ t('wallet.points') }}
                    </div>
                  </div>
                </div>

                <!-- Method Status -->
                <div class="flex items-center gap-1 sm:gap-2 flex-shrink-0">
                  <div v-if="!method.completed"
                    class="px-2 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 text-xs font-medium rounded-full">
                    {{ t('wallet.pending') }}
                  </div>

                  <ChevronDownIcon v-if="method.expandable && !isMedicationListExpanded"
                    class="w-4 h-4 text-gray-400 dark:text-gray-500 transition-transform duration-200 group-hover:text-primary-500 dark:group-hover:text-primary-400" />
                  <ChevronUpIcon v-if="method.expandable && isMedicationListExpanded"
                    class="w-4 h-4 text-gray-400 dark:text-gray-500 transition-transform duration-200 group-hover:text-primary-500 dark:group-hover:text-primary-400" />
                </div>
              </div>

              <!-- Expandable medication list -->
              <div v-if="method.expandable && isMedicationListExpanded"
                class="px-4 pb-4 bg-gray-50 dark:bg-slate-700 border-t border-gray-200 dark:border-slate-600">
                <div class="mt-3 space-y-3">
                  <div v-for="(medication, index) in method.medications" :key="index"
                    class="flex justify-between items-center p-3 bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-600 hover:shadow-sm transition-all duration-200">
                    <div class="font-medium text-gray-900 dark:text-gray-100 text-sm">{{ medication.name }}</div>
                    <div class="flex items-center gap-2">
                      <span class="px-2 py-0.5 text-xs font-medium rounded-full" :class="{
                        'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300': medication.needed.toLowerCase() === 'critical',
                        'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300': medication.needed.toLowerCase() === 'high',
                        'bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300': medication.needed.toLowerCase() === 'medium'
                      }">
                        {{ medication.needed }}
                      </span>
                      <span
                        class="px-2 py-0.5 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 text-xs font-medium rounded-full">
                        +{{ medication.points }} {{ t('wallet.points') }}
                      </span>
                    </div>
                  </div>
                </div>
                <button
                  class="inline-flex items-center gap-2 px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800 w-full mt-4 text-sm justify-center">
                  {{ t('wallet.viewAllMedications') }}
                </button>
              </div>
            </li>
          </ul>

          <!-- Extra missions section -->
          <div class="mt-8">
            <div class="flex items-center gap-2 mb-4">
              <div class="w-0.5 h-6 bg-primary-400 dark:bg-primary-500 rounded-full"></div>
              <h4 class="text-base font-bold text-gray-900 dark:text-gray-100">{{ t('wallet.extraMissions') }}</h4>
              <div class="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent dark:from-slate-600"></div>
            </div>
            <ul class="space-y-3">
              <li v-for="mission in extraMissions" :key="mission.id"
                class="group relative bg-white dark:bg-slate-800 rounded-xl border border-gray-200 dark:border-slate-700 overflow-hidden transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg"
                :class="{
                  'bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-800': mission.completed,
                  'hover:border-primary-300 dark:hover:border-primary-600': !mission.completed
                }">
                <!-- Completion indicator -->
                <div v-if="mission.completed"
                  class="absolute top-0 left-0 w-full h-0.5 bg-primary-400 dark:bg-primary-500">
                </div>

                <div class="flex items-center p-4 gap-3">
                  <!-- Mission Icon -->
                  <div
                    class="relative flex items-center justify-center w-10 h-10 rounded-xl flex-shrink-0 transition-all duration-300"
                    :class="mission.completed
                      ? 'bg-primary-200 dark:bg-primary-700'
                      : 'bg-primary-100 dark:bg-primary-900/30'">
                    <component :is="mission.icon" class="w-5 h-5 text-primary-600 dark:text-primary-400" />
                    <CheckCircleIcon v-if="mission.completed"
                      class="absolute -top-0.5 -right-0.5 w-4 h-4 text-primary-600 dark:text-primary-400 bg-white dark:bg-slate-800 rounded-full p-0.5" />
                  </div>

                  <!-- Mission Info -->
                  <div class="flex-1">
                    <div class="font-semibold text-gray-900 dark:text-gray-100 mb-1 text-sm">{{ mission.title }}</div>
                    <div class="flex items-center gap-2">
                      <div
                        class="px-2 py-0.5 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 text-xs font-medium rounded-full">
                        +{{ mission.points }} {{ t('wallet.points') }}
                      </div>
                    </div>
                  </div>

                  <!-- Mission Status -->
                  <div class="flex items-center gap-2">
                    <div v-if="!mission.completed"
                      class="px-2 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 text-xs font-medium rounded-full">
                      {{ t('wallet.pending') }}
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
