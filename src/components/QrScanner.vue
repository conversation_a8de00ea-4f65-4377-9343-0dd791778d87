<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import jsQR from 'jsqr';

const props = defineProps<{
  onScan: (data: any) => void;
}>();

const { t } = useI18n();

const videoRef = ref<HTMLVideoElement | null>(null);
const canvasRef = ref<HTMLCanvasElement | null>(null);
const scanning = ref(false);
const errorMessage = ref('');

// Real QR code scanning functionality using jsQR
const startScanner = async () => {
  if (!videoRef.value || !canvasRef.value) return;

  try {
    scanning.value = true;
    errorMessage.value = '';

    const stream = await navigator.mediaDevices.getUserMedia({
      video: { facingMode: 'environment' }
    });

    videoRef.value.srcObject = stream;
    videoRef.value.play();

    // Start scanning for QR codes
    requestAnimationFrame(scanQRCode);

  } catch (error) {
    console.error('Error accessing camera:', error);
    errorMessage.value = t('qrScanner.cameraAccessError');
    scanning.value = false;
  }
};

const scanQRCode = () => {
  if (!videoRef.value || !canvasRef.value || !scanning.value) return;

  const video = videoRef.value;
  const canvas = canvasRef.value;
  const context = canvas.getContext('2d');

  if (!context) return;

  // Only process the frame if the video is playing
  if (video.readyState === video.HAVE_ENOUGH_DATA) {
    // Set canvas dimensions to match video
    canvas.height = video.videoHeight;
    canvas.width = video.videoWidth;

    // Draw current video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Get image data from canvas
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

    // Scan for QR code
    const code = jsQR(imageData.data, imageData.width, imageData.height, {
      inversionAttempts: "dontInvert",
    });

    // If QR code is found
    if (code) {
      console.log("QR Code detected:", code.data);

      try {
        // Try to parse the QR code data as JSON
        const qrData = JSON.parse(code.data);

        // Check if the QR code contains the required fields
        if (qrData.email && qrData.role && qrData.deviceId) {
          // Stop scanning and pass the data to the parent component
          stopScanner();
          props.onScan(qrData);
          return;
        }
      } catch (e) {
        console.error("Error parsing QR code data:", e);
        // If it's not valid JSON, just pass the raw data
        stopScanner();
        props.onScan({ token: code.data });
        return;
      }
    }
  }

  // If no QR code was found or it wasn't valid, continue scanning
  if (scanning.value) {
    requestAnimationFrame(scanQRCode);
  }
};

const stopScanner = () => {
  if (!videoRef.value) return;

  scanning.value = false;

  const stream = videoRef.value.srcObject as MediaStream;
  if (stream) {
    const tracks = stream.getTracks();
    tracks.forEach(track => track.stop());
    videoRef.value.srcObject = null;
  }
};

onMounted(() => {
  try {
    // Add a small delay to ensure the component is fully mounted
    setTimeout(() => {
      startScanner();
    }, 500);
  } catch (error) {
    console.error("Error in QrScanner onMounted:", error);
    errorMessage.value = t('qrScanner.initializationError');
  }
});

onUnmounted(() => {
  stopScanner();
});
</script>

<template>
  <div class="w-full flex flex-col items-center justify-center">
    <div class="relative w-full max-w-md mx-auto">
      <video ref="videoRef" class="w-full h-64 bg-gray-900 rounded-lg" playsinline></video>

      <canvas ref="canvasRef" class="hidden"></canvas>

      <div v-if="scanning" class="absolute inset-0 flex flex-col items-center justify-center">
        <div class="w-48 h-48 border-2 border-primary-600 rounded-lg shadow-[0_0_0_4000px_rgba(0,0,0,0.5)]"></div>
        <p class="text-center text-white mt-4 font-medium">{{ t('qrScanner.scanToConnect') }}</p>
      </div>

      <div v-if="errorMessage"
        class="absolute bottom-0 left-0 right-0 bg-red-500 text-white p-2 text-center rounded-b-lg">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>
