<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  MapPinIcon,
  PhoneIcon,
  ClockIcon,
  TruckIcon,
  CheckBadgeIcon,
  BuildingStorefrontIcon
} from '@heroicons/vue/24/outline';
import type { Store } from '../../types/stores';

interface Props {
  store: Store;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<{
  seeStock: [store: Store];
  order: [store: Store];
}>();

const { t } = useI18n();

// Computed properties
const storeTypeLabel = computed(() => {
  return t(`stores.storeTypes.${props.store.storeType}`);
});

const statusLabel = computed(() => {
  return props.store.isOpen ? t('stores.openNow') : t('stores.closedNow');
});

const statusColor = computed(() => {
  return props.store.isOpen
    ? 'text-green-600 dark:text-green-400'
    : 'text-red-600 dark:text-red-400';
});

const deliveryLabel = computed(() => {
  return props.store.deliverySupport ? t('stores.available') : t('stores.unavailable');
});



const handleSeeStock = () => {
  emit('seeStock', props.store);
};

const handleOrder = () => {
  emit('order', props.store);
};
</script>

<template>
  <div
    class="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-gray-200 dark:border-neutral-700 p-6 hover:shadow-md dark:hover:shadow-dark-medium transition-all duration-200 hover:-translate-y-0.5">
    <!-- Store Header -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex items-center gap-3 flex-1 min-w-0">
        <div class="flex-shrink-0">
          <BuildingStorefrontIcon class="w-10 h-10 text-primary-600 dark:text-primary-400" />
        </div>
        <div class="flex-1 min-w-0">
          <div class="flex items-center gap-2 mb-1">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
              {{ store.name }}
            </h3>
            <CheckBadgeIcon v-if="store.verified" class="w-5 h-5 text-blue-500 dark:text-blue-400 flex-shrink-0"
              :title="t('common.verified')" />
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ storeTypeLabel }}
          </p>
        </div>
      </div>

      <!-- Status Badge -->
      <div class="flex-shrink-0">
        <span class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium" :class="store.isOpen
          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'">
          <ClockIcon class="w-3 h-3" />
          {{ statusLabel }}
        </span>
      </div>
    </div>

    <!-- Store Details -->
    <div class="space-y-3 mb-4">
      <!-- Location -->
      <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
        <MapPinIcon class="w-4 h-4 flex-shrink-0" />
        <span class="truncate">{{ store.location.address }}</span>
      </div>



      <!-- Phone -->
      <div v-if="store.contact?.phone" class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
        <PhoneIcon class="w-4 h-4 flex-shrink-0" />
        <span>{{ store.contact.phone }}</span>
      </div>

      <!-- Delivery Support -->
      <div class="flex items-center gap-2 text-sm">
        <TruckIcon class="w-4 h-4 flex-shrink-0"
          :class="store.deliverySupport ? 'text-green-600 dark:text-green-400' : 'text-gray-400 dark:text-gray-600'" />
        <span
          :class="store.deliverySupport ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'">
          {{ t('stores.deliverySupport') }}: {{ deliveryLabel }}
        </span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-3 pt-4 border-t border-gray-200 dark:border-neutral-700">
      <button @click="handleSeeStock" :disabled="loading"
        class="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 dark:bg-neutral-700 dark:hover:bg-neutral-600 text-gray-800 dark:text-gray-200 font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-neutral-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-gray-100 dark:disabled:hover:bg-neutral-700">
        <BuildingStorefrontIcon class="w-4 h-4" />
        <span class="text-sm">{{ t('stores.seeStock') }}</span>
      </button>

      <button @click="handleOrder" :disabled="loading"
        class="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2.5 bg-primary-600 hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-neutral-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary-600 dark:disabled:hover:bg-primary-700">
        <TruckIcon class="w-4 h-4" />
        <span class="text-sm">{{ t('stores.order') }}</span>
      </button>
    </div>
  </div>
</template>
