<template>
  <Tooltip :content="isRefreshing ? t('common.refreshing') : t('common.refresh')" position="top">
    <BaseButton variant="secondary" size="md" :disabled="isRefreshing" :loading="isRefreshing" :icon="ArrowPathIcon"
      icon-position="left" elevated :aria-label="isRefreshing ? t('common.refreshing') : t('common.refresh')"
      @click="handleRefresh">
      {{ isRefreshing ? t('common.refreshing') : t('common.refresh') }}
    </BaseButton>
  </Tooltip>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ArrowPathIcon } from '@heroicons/vue/24/outline';
import BaseButton from './ui/BaseButton.vue';
import Tooltip from './ui/Tooltip.vue';

const { t } = useI18n();

const props = defineProps<{
  section: string;
  refreshingSection: string | null;
}>();

const emit = defineEmits<{
  (e: 'refresh', section: string): void;
}>();

// Refresh button functionality:
// - <PERSON><PERSON> is disabled when the section is being refreshed
// - Button emits a 'refresh' event to the parent component when clicked
// - The event includes the section to be refreshed

const isRefreshing = computed(() => {
  return props.refreshingSection === props.section || props.refreshingSection === 'all';
});

const handleRefresh = () => {
  if (!isRefreshing.value) {
    emit('refresh', props.section);
  }
};
</script>
