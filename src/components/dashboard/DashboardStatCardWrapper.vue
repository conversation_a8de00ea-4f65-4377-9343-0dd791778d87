<script setup lang="ts">
import StatCard from './StatCard.vue';
import Tooltip from '../ui/Tooltip.vue';
import type { Component } from 'vue';

interface Props {
    icon: Component;
    value: string | number;
    label: string;
    status?: 'default' | 'success' | 'warning' | 'error' | 'info';
    ariaLabel?: string;
    tooltip?: string;
    clickable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    status: 'default',
    ariaLabel: '',
    tooltip: '',
    clickable: true
});

const emit = defineEmits(['click']);

const handleClick = () => {
    if (props.clickable) {
        emit('click');
    }
};
</script>

<template>
    <Tooltip :content="tooltip" position="top" :disabled="!tooltip">
        <div class="text-start">
            <StatCard :icon="icon" :value="value" :label="label" :status="status" :aria-label="ariaLabel"
                :clickable="clickable" @click="handleClick" />
        </div>
    </Tooltip>
</template>
