<script setup lang="ts">
import { computed } from 'vue';
import DashboardStatCardWrapper from './DashboardStatCardWrapper.vue';
import type { Component } from 'vue';

interface StatItem {
    icon: Component;
    value: string | number;
    label: string;
    status?: 'default' | 'success' | 'warning' | 'error' | 'info';
    ariaLabel?: string;
    tooltip?: string;
    onClick?: () => void;
}

interface Props {
    stats: StatItem[];
    loading?: boolean;
    columns?: 1 | 2;
    gap?: 'sm' | 'md' | 'lg';
}

const props = withDefaults(defineProps<Props>(), {
    loading: false,
    columns: 1,
    gap: 'md'
});



const gridClasses = computed(() => {
    const baseClasses = 'grid text-start';

    const columnClasses = {
        1: 'grid-cols-1',
        2: 'grid-cols-2 sm:grid-cols-1'
    };

    const gapClasses = {
        sm: 'gap-2',
        md: 'gap-4',
        lg: 'gap-6'
    };

    return `${baseClasses} ${columnClasses[props.columns]} ${gapClasses[props.gap]}`;
});
</script>

<template>
    <div v-if="loading" class="animate-pulse">
        <div :class="gridClasses">
            <div v-for="i in (columns || 1)" :key="i"
                class="flex items-center p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 lg:p-3 sm:p-3">
                <div
                    class="flex items-center justify-center w-12 h-12 rounded-lg me-4 lg:w-10 lg:h-10 lg:me-3 sm:w-8 sm:h-8 sm:me-3 bg-gray-200 dark:bg-neutral-700">
                    <div class="w-6 h-6 lg:w-5 lg:h-5 sm:w-5 sm:h-5 bg-gray-300 dark:bg-neutral-600 rounded">
                    </div>
                </div>
                <div class="flex flex-col text-start min-w-0 flex-1">
                    <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded mb-2 lg:h-5 sm:h-4"></div>
                    <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-3/4 sm:h-3"></div>
                </div>
            </div>
        </div>
    </div>
    <div v-else :class="gridClasses">
        <DashboardStatCardWrapper v-for="(stat, index) in stats" :key="index" :icon="stat.icon" :value="stat.value"
            :label="stat.label" :status="stat.status" :aria-label="stat.ariaLabel" :tooltip="stat.tooltip"
            @click="stat.onClick" />
    </div>
</template>
