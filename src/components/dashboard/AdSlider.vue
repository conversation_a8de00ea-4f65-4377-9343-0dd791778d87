<template>
  <!-- Loading Placeholder -->
  <div v-if="loading" class="w-full mb-8">
    <div
      class="relative overflow-hidden rounded-2xl shadow-xl bg-white dark:bg-slate-800 h-48 md:h-56 lg:h-64 xl:h-72 border-t border-b border-gray-200 dark:border-slate-600">
      <div class="animate-pulse h-full">
        <!-- Background skeleton -->
        <div
          class="w-full h-full bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100 dark:from-neutral-800 dark:via-neutral-700 dark:to-neutral-800">
        </div>

        <!-- Content skeleton -->
        <div class="absolute inset-0 flex items-center">
          <div class="container mx-auto px-6 lg:px-8">
            <div class="max-w-2xl">
              <!-- Sponsored label skeleton -->
              <div class="inline-block w-20 h-6 bg-gray-200 dark:bg-neutral-700 rounded-full mb-3"></div>

              <!-- Title skeleton -->
              <div class="space-y-2 mb-4">
                <div class="h-8 bg-gray-200 dark:bg-neutral-700 rounded w-3/4"></div>
                <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded w-1/2"></div>
              </div>

              <!-- Description skeleton -->
              <div class="space-y-2 mb-6">
                <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-full"></div>
                <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-2/3"></div>
              </div>

              <!-- Button skeleton -->
              <div class="w-32 h-12 bg-gray-200 dark:bg-neutral-700 rounded-lg"></div>
            </div>
          </div>
        </div>

        <!-- Navigation skeleton -->
        <div class="absolute top-1/2 left-4 -translate-y-1/2 w-10 h-10 bg-gray-200 dark:bg-neutral-700 rounded-full">
        </div>
        <div class="absolute top-1/2 right-4 -translate-y-1/2 w-10 h-10 bg-gray-200 dark:bg-neutral-700 rounded-full">
        </div>

        <!-- Dots skeleton -->
        <div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex flex-row items-center gap-2">
          <div v-for="i in 3" :key="i" class="w-2 h-2 bg-gray-200 dark:bg-neutral-700 rounded-full"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actual Ad Slider -->
  <div v-else-if="ads.length > 0" class="w-full mb-8 relative" :class="{ 'rtl:direction-rtl': isRTL }">
    <div
      class="group relative overflow-hidden rounded-2xl shadow-xl bg-white dark:bg-slate-800 h-48 md:h-56 lg:h-64 xl:h-72 border-t border-b border-gray-200 dark:border-slate-600"
      @mouseenter="pauseAutoplay" @mouseleave="resumeAutoplay">
      <!-- Ad Slides -->
      <div class="flex transition-transform duration-500 ease-in-out h-full"
        :style="{ transform: `translateX(${isRTL ? currentSlide * 100 : -currentSlide * 100}%)` }">
        <div v-for="(ad, index) in ads" :key="ad.id" class="w-full flex-shrink-0 relative h-full">
          <!-- Background Image -->
          <img :src="ad.imageUrl" :alt="ad.medicationName" class="w-full h-full object-cover" @error="handleImageError"
            loading="lazy" />

          <!-- Enhanced Overlay Gradient -->
          <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-black/20"></div>
          <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>

          <!-- Content -->
          <div class="absolute inset-0 flex items-center pointer-events-none">
            <div class="container mx-auto px-6 lg:px-8">
              <div class="max-w-2xl">
                <!-- Sponsored Label -->
                <div
                  class="inline-flex items-center px-3 py-1 rounded-full bg-yellow-500/90 text-yellow-900 text-xs font-semibold mb-3">
                  <span>{{ t('dashboard.sponsored') }}</span>
                </div>

                <!-- Content -->
                <h3 class="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-2 leading-tight">{{
                  ad.medicationName }}</h3>
                <p class="text-lg md:text-xl text-gray-200 mb-2">{{ ad.companyName }}</p>
                <p class="text-sm md:text-base text-gray-300 mb-4 max-w-md">{{ ad.tagline }}</p>

                <!-- CTA Button -->
                <button v-if="ad.ctaLink"
                  class="inline-flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 pointer-events-auto"
                  @click="handleCTAClick(ad.ctaLink)" :aria-label="`${t('dashboard.learnMore')} ${ad.medicationName}`">
                  {{ t('dashboard.learnMore') }}
                  <component :is="ctaArrowIcon" :class="isRTL ? 'w-4 h-4 mr-2' : 'w-4 h-4 ml-2'" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Dots Indicator -->
      <div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex flex-row items-center gap-2 z-50">
        <button v-for="(ad, index) in ads" :key="`dot-${ad.id}`"
          class="w-2 h-2 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50"
          :class="index === currentSlide ? 'bg-white' : 'bg-white/50'" @click="goToSlide(index)"
          :aria-label="`${t('dashboard.goToSlide')} ${index + 1}`"></button>
      </div>

      <!-- Left Arrow (Previous in LTR, Next in RTL) -->
      <button
        class="absolute top-1/2 z-0 left-2 -translate-y-1/2 w-14 h-14 bg-black/70 hover:bg-black/90 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 z-[9999] cursor-pointer shadow-lg opacity-0 group-hover:opacity-100"
        @click="isRTL ? handleNextClick($event) : handlePreviousClick($event)" @mousedown="(e) => e.preventDefault()"
        :aria-label="isRTL ? t('dashboard.nextAd') : t('dashboard.previousAd')">
        <ChevronLeftIcon class="w-7 h-7" />
      </button>

      <!-- Right Arrow (Next in LTR, Previous in RTL) -->
      <button
        class="absolute top-1/2 z-0 right-2 -translate-y-1/2 w-14 h-14 bg-black/70 hover:bg-black/90 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 z-[9999] cursor-pointer shadow-lg opacity-0 group-hover:opacity-100"
        @click="isRTL ? handlePreviousClick($event) : handleNextClick($event)" @mousedown="(e) => e.preventDefault()"
        :aria-label="isRTL ? t('dashboard.previousAd') : t('dashboard.nextAd')">
        <ChevronRightIcon class="w-7 h-7" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/outline';
import { useRTL } from '../../composables/useRTL';

// Types
interface AdData {
  id: string;
  imageUrl: string;
  medicationName: string;
  companyName: string;
  tagline: string;
  ctaLink?: string;
}

// Props
interface Props {
  ads: AdData[];
  autoplayInterval?: number;
  lang?: 'ar' | 'en';
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoplayInterval: 10000, // 10 seconds
  lang: 'en',
  loading: false
});

// Composables
const { t, locale } = useI18n();
const { getNavigationArrow } = useRTL();

// Reactive state
const currentSlide = ref(0);
const autoplayTimer = ref<number | null>(null);
const isAutoplayPaused = ref(false);

// Computed
const isRTL = computed(() => props.lang === 'ar' || locale.value === 'ar');

// Get RTL-aware arrow icon for CTA button
const ctaArrowIcon = computed(() => {
  const iconName = getNavigationArrow('next');
  return iconName === 'ChevronLeftIcon' ? ChevronLeftIcon : ChevronRightIcon;
});

// Methods
const nextSlide = () => {
  if (props.ads.length <= 1) return;
  currentSlide.value = (currentSlide.value + 1) % props.ads.length;
};

const previousSlide = () => {
  if (props.ads.length <= 1) return;
  currentSlide.value = currentSlide.value === 0 ? props.ads.length - 1 : currentSlide.value - 1;
};

const handleNextClick = (event: Event) => {
  event.preventDefault();
  event.stopPropagation();
  stopAutoplay();
  nextSlide();
  startAutoplay();
};

const handlePreviousClick = (event: Event) => {
  event.preventDefault();
  event.stopPropagation();
  stopAutoplay();
  previousSlide();
  startAutoplay();
};

const goToSlide = (index: number) => {
  stopAutoplay();
  currentSlide.value = index;
  startAutoplay();
};

const startAutoplay = () => {
  if (props.ads.length <= 1) return;
  stopAutoplay();
  autoplayTimer.value = setInterval(() => {
    if (!isAutoplayPaused.value) {
      nextSlide();
    }
  }, props.autoplayInterval) as unknown as number;
};

const stopAutoplay = () => {
  if (autoplayTimer.value) {
    clearInterval(autoplayTimer.value);
    autoplayTimer.value = null;
  }
};

const pauseAutoplay = () => {
  isAutoplayPaused.value = true;
};

const resumeAutoplay = () => {
  isAutoplayPaused.value = false;
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = '/images/placeholder-medication.jpg';
};

const handleCTAClick = (link: string) => {
  console.log('CTA clicked:', link);
};

// Keyboard navigation
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'ArrowLeft') {
    isRTL.value ? handleNextClick(new Event('click')) : handlePreviousClick(new Event('click'));
  } else if (event.key === 'ArrowRight') {
    isRTL.value ? handlePreviousClick(new Event('click')) : handleNextClick(new Event('click'));
  }
};

// Watch for ads changes and restart autoplay
watch(() => props.ads, (newAds) => {
  if (newAds && newAds.length > 1) {
    currentSlide.value = 0;
    startAutoplay();
  }
}, { immediate: true });

// Lifecycle
onMounted(() => {
  setTimeout(() => {
    if (props.ads && props.ads.length > 1) {
      startAutoplay();
    }
  }, 100);
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  stopAutoplay();
  document.removeEventListener('keydown', handleKeydown);
});
</script>
