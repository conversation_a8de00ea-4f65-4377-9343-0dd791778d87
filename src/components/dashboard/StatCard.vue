<script setup lang="ts">
import { computed } from 'vue';
import type { Component } from 'vue';

interface Props {
    icon: Component;
    value: string | number;
    label: string;
    status?: 'default' | 'success' | 'warning' | 'error' | 'info';
    ariaLabel?: string;
    clickable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    status: 'default',
    ariaLabel: '',
    clickable: true
});

const emit = defineEmits(['click']);

const statusClasses = computed(() => {
    // All icons now use the same background style as the invoices card (default status)
    const iconBackground = 'bg-gray-100 dark:bg-neutral-800';

    switch (props.status) {
        case 'success':
            return {
                icon: iconBackground,
                iconColor: 'text-success-600 dark:text-success-400'
            };
        case 'warning':
            return {
                icon: iconBackground,
                iconColor: 'text-warning-600 dark:text-warning-400'
            };
        case 'error':
            return {
                icon: iconBackground,
                iconColor: 'text-error-600 dark:text-error-400'
            };
        case 'info':
            return {
                icon: iconBackground,
                iconColor: 'text-primary-600 dark:text-primary-400'
            };
        default:
            return {
                icon: iconBackground,
                iconColor: 'text-primary-600 dark:text-primary-400'
            };
    }
});

const formattedValue = computed(() => {
    if (typeof props.value === 'number') {
        return props.value.toLocaleString();
    }
    return props.value;
});

const accessibilityLabel = computed(() => {
    return props.ariaLabel || `${props.label}: ${formattedValue.value}`;
});

const handleClick = () => {
    if (props.clickable) {
        emit('click');
    }
};
</script>

<template>
    <div class="flex items-center p-4 bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-700 flex-1 min-w-0 transition-all duration-200 lg:p-3 sm:p-3 text-start"
        :class="{
            'cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 hover:-translate-y-0.5 hover:shadow-light focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-gray-500 dark:focus:ring-offset-slate-800': clickable,
            '': true
        }" :aria-label="accessibilityLabel" role="region" @click="handleClick" :tabindex="clickable ? 0 : -1"
        @keydown.enter="handleClick" @keydown.space="handleClick">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg mr-4 rtl:mr-0 rtl:ml-4 lg:w-10 lg:h-10 lg:mr-3 lg:rtl:mr-0 lg:rtl:ml-3 sm:w-8 sm:h-8 sm:mr-3 sm:rtl:mr-0 sm:rtl:ml-3"
            :class="statusClasses.icon" aria-hidden="true">
            <component :is="icon" class="w-6 h-6 lg:w-5 lg:h-5 sm:w-5 sm:h-5" :class="statusClasses.iconColor" />
        </div>
        <div class="flex flex-col text-start rtl:text-right min-w-0">
            <span class="text-2xl font-bold text-gray-800 dark:text-gray-100 leading-tight lg:text-xl sm:text-lg">{{
                formattedValue }}</span>
            <span class="text-sm text-gray-500 dark:text-gray-400 sm:text-xs">{{ label }}</span>
        </div>
    </div>
</template>
