<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import DashboardWidget from './DashboardWidget.vue';
import SkeletonLoader from '../ui/SkeletonLoader.vue';
import { Bar } from 'vue-chartjs';
import {
    Chart as ChartJS,
    Title,
    Tooltip,
    Legend,
    BarElement,
    CategoryScale,
    LinearScale,
    type ChartOptions
} from 'chart.js';
import type { RevenueDataPoint } from '../../types/dashboard';

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend
);

interface Props {
    data: RevenueDataPoint[] | { labels: string[], datasets: any[] };
    loading?: boolean;
    title?: string;
    height?: string;
}

const props = withDefaults(defineProps<Props>(), {
    loading: false,
    title: '',
    height: 'h-80'
});

const { t } = useI18n();

// Prepare chart data
const chartData = computed(() => {
    // Return empty data if props.data is undefined or null
    if (!props.data) {
        return {
            labels: [],
            datasets: []
        };
    }

    // Check if data is already in chart format
    if (Array.isArray(props.data) && props.data.length > 0 && 'month' in props.data[0]) {
        // Handle RevenueDataPoint[] format
        const revenueData = props.data as RevenueDataPoint[];
        return {
            labels: revenueData.map(item => item.month),
            datasets: [
                {
                    label: t('dashboard.revenue'),
                    data: revenueData.map(item => item.revenue),
                    backgroundColor: 'rgba(13, 148, 136, 0.5)', // Primary teal color
                    borderColor: 'rgba(13, 148, 136, 1)',
                    borderWidth: 1,
                    borderRadius: 5,
                    hoverBackgroundColor: 'rgba(13, 148, 136, 0.7)',
                }
            ]
        };
    } else if (props.data && typeof props.data === 'object' && 'labels' in props.data && 'datasets' in props.data) {
        // Handle chart format directly
        const chartFormat = props.data as { labels: string[], datasets: any[] };

        // Ensure datasets exist and is an array
        if (!chartFormat.datasets || !Array.isArray(chartFormat.datasets)) {
            return {
                labels: chartFormat.labels || [],
                datasets: []
            };
        }

        // Update colors to match primary theme
        const updatedDatasets = chartFormat.datasets.map(dataset => ({
            ...dataset,
            backgroundColor: 'rgba(13, 148, 136, 0.5)', // Primary teal color
            borderColor: 'rgba(13, 148, 136, 1)',
            borderWidth: 1,
            borderRadius: 5,
            hoverBackgroundColor: 'rgba(13, 148, 136, 0.7)',
        }));

        return {
            labels: chartFormat.labels || [],
            datasets: updatedDatasets
        };
    } else {
        // Fallback for unexpected data format
        return {
            labels: [],
            datasets: []
        };
    }
});

// Chart options
const chartOptions = computed(() => {
    const options: ChartOptions<'bar'> = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                callbacks: {
                    label: (context) => {
                        const value = context.raw as number;
                        return `${t('dashboard.revenue')}: $${value.toLocaleString()}`;
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function (value) {
                        return `$${value.toLocaleString()}`;
                    }
                }
            }
        },
        animation: {
            duration: 1000,
        }
    };

    return options;
});

// Handle window resize
onMounted(() => {
    const handleResize = () => {
        // Chart.js is responsive by default with responsive: true
    };

    window.addEventListener('resize', handleResize);

    return () => {
        window.removeEventListener('resize', handleResize);
    };
});
</script>

<template>
    <DashboardWidget :title="title || t('dashboard.monthlyRevenue')" id="revenue-chart" :full-width="true">
        <div v-if="loading" class="p-8" :class="height">
            <SkeletonLoader type="rectangle" width="w-full" height="h-64" />
        </div>
        <div v-else-if="chartData.datasets.length > 0" class="w-full text-start" :class="height">
            <Bar :data="chartData" :options="chartOptions" aria-label="Revenue chart" role="img" />
        </div>
        <div v-else class="flex flex-col items-center justify-center p-8 text-gray-500 dark:text-gray-400"
            :class="height">
            <p>{{ t('common.noDataDescription') }}</p>
        </div>
    </DashboardWidget>
</template>
