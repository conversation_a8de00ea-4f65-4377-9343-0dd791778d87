<script setup lang="ts">
interface Props {
    title: string;
    fullWidth?: boolean;
    id?: string;
}

const props = withDefaults(defineProps<Props>(), {
    fullWidth: false,
    id: ''
});

const widgetId = props.id || `widget-${props.title.toLowerCase().replace(/\s+/g, '-')}`;
</script>

<template>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-neutral-850 dark:border-neutral-700 flex flex-col transition-shadow duration-300 hover:shadow-md text-start"
        :class="{ 'col-span-full': fullWidth }" :id="widgetId" role="region" :aria-labelledby="`${widgetId}-title`">
        <h3 class="text-lg text-start font-semibold text-gray-800 dark:text-neutral-100 p-4 border-b border-gray-200 dark:border-neutral-700 m-0 text-start rtl:text-right lg:text-base lg:px-3 lg:py-3 sm:text-sm sm:px-3 sm:py-3"
            :id="`${widgetId}-title`">
            {{ title }}
        </h3>
        <div class="p-4 flex-1 lg:p-3 sm:p-3 text-start rtl:text-right">
            <slot></slot>
        </div>
    </div>
</template>
