<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DashboardWidget from './DashboardWidget.vue';
import DashboardStatGrid from './DashboardStatGrid.vue';
import {
    DocumentTextIcon,
    TruckIcon,
    CheckCircleIcon
} from '@heroicons/vue/24/outline';
import type { DeliveryData } from '../../types/dashboard';

interface Props {
    data: DeliveryData;
    loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
});

const emit = defineEmits(['navigate-to-delivery']);

const { t } = useI18n();

// Function to navigate to delivery orders view with appropriate filters
const navigateToDelivery = (status?: string) => {
    emit('navigate-to-delivery', status);
};

const deliveryStats = computed(() => [
    {
        icon: DocumentTextIcon,
        value: props.data.new,
        label: t('orders.statuses.pending'),
        status: 'info' as const,
        ariaLabel: `${t('orders.statuses.pending')}: ${props.data.new}`,
        tooltip: t('orders.clickToViewDetails'),
        onClick: () => navigateToDelivery('pending')
    },
    {
        icon: TruckIcon,
        value: props.data.inTransit,
        label: t('orders.statuses.out_for_delivery'),
        status: 'warning' as const,
        ariaLabel: `${t('orders.statuses.out_for_delivery')}: ${props.data.inTransit}`,
        tooltip: t('orders.clickToViewDetails'),
        onClick: () => navigateToDelivery('out_for_delivery')
    },
    {
        icon: CheckCircleIcon,
        value: props.data.completed,
        label: t('orders.statuses.delivered'),
        status: 'success' as const,
        ariaLabel: `${t('orders.statuses.delivered')}: ${props.data.completed}`,
        tooltip: t('orders.clickToViewDetails'),
        onClick: () => navigateToDelivery('delivered')
    }
]);
</script>

<template>
    <DashboardWidget :title="t('dashboard.deliveryOrders')" id="delivery-orders">
        <div v-if="loading" class="animate-pulse">
            <div class="grid grid-cols-1 gap-4 text-start">
                <div v-for="i in 3" :key="i"
                    class="flex items-center p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 lg:p-3 sm:p-3">
                    <div
                        class="flex items-center justify-center w-12 h-12 rounded-lg me-4 lg:w-10 lg:h-10 lg:me-3 sm:w-8 sm:h-8 sm:me-3 bg-gray-200 dark:bg-neutral-700">
                        <div class="w-6 h-6 lg:w-5 lg:h-5 sm:w-5 sm:h-5 bg-gray-300 dark:bg-neutral-600 rounded">
                        </div>
                    </div>
                    <div class="flex flex-col text-start min-w-0 flex-1">
                        <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded mb-2 lg:h-5 sm:h-4"></div>
                        <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-3/4 sm:h-3"></div>
                    </div>
                </div>
            </div>
        </div>
        <DashboardStatGrid v-else :stats="deliveryStats" :loading="false" :columns="1" />
    </DashboardWidget>
</template>
