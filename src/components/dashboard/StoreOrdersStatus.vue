<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DashboardWidget from './DashboardWidget.vue';
import DashboardStatGrid from './DashboardStatGrid.vue';
import {
  ClockIcon,
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/vue/24/outline';
import type { StoreOrdersData } from '../../types/dashboard';

interface Props {
  data: StoreOrdersData;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits(['navigate-to-store-orders']);

const { t } = useI18n();

// Function to navigate to store orders view with appropriate filters
const navigateToStoreOrders = (status?: string) => {
  emit('navigate-to-store-orders', status);
};

const storeOrdersStats = computed(() => [
  {
    icon: ClockIcon,
    value: props.data.pending,
    label: t('dashboard.storeOrders.pending'),
    status: 'warning' as const,
    ariaLabel: `${t('dashboard.storeOrders.pending')}: ${props.data.pending}`,
    tooltip: t('dashboard.tooltips.viewStoreOrders'),
    onClick: () => navigateToStoreOrders('pending')
  },
  {
    icon: TruckIcon,
    value: props.data.inTransit,
    label: t('dashboard.storeOrders.inTransit'),
    status: 'info' as const,
    ariaLabel: `${t('dashboard.storeOrders.inTransit')}: ${props.data.inTransit}`,
    tooltip: t('dashboard.tooltips.viewStoreOrders'),
    onClick: () => navigateToStoreOrders('inTransit')
  },
  {
    icon: CheckCircleIcon,
    value: props.data.completed,
    label: t('dashboard.storeOrders.completed'),
    status: 'success' as const,
    ariaLabel: `${t('dashboard.storeOrders.completed')}: ${props.data.completed}`,
    tooltip: t('dashboard.tooltips.viewStoreOrders'),
    onClick: () => navigateToStoreOrders('completed')
  },
  {
    icon: XCircleIcon,
    value: props.data.rejected,
    label: t('dashboard.storeOrders.rejected'),
    status: 'error' as const,
    ariaLabel: `${t('dashboard.storeOrders.rejected')}: ${props.data.rejected}`,
    tooltip: t('dashboard.tooltips.viewStoreOrders'),
    onClick: () => navigateToStoreOrders('rejected')
  }
]);
</script>

<template>
  <DashboardWidget :title="t('dashboard.storeOrders.title')" id="store-orders">
    <div v-if="loading" class="animate-pulse">
      <div class="grid grid-cols-1 gap-4 text-start">
        <div v-for="i in 4" :key="i"
          class="flex items-center p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 lg:p-3 sm:p-3">
          <div
            class="flex items-center justify-center w-12 h-12 rounded-lg me-4 lg:w-10 lg:h-10 lg:me-3 sm:w-8 sm:h-8 sm:me-3 bg-gray-200 dark:bg-neutral-700">
            <div class="w-6 h-6 lg:w-5 lg:h-5 sm:w-5 sm:h-5 bg-gray-300 dark:bg-neutral-600 rounded">
            </div>
          </div>
          <div class="flex flex-col text-start min-w-0 flex-1">
            <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded mb-2 lg:h-5 sm:h-4"></div>
            <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-3/4 sm:h-3"></div>
          </div>
        </div>
      </div>
    </div>
    <DashboardStatGrid v-else :stats="storeOrdersStats" :loading="false" :columns="1" />
  </DashboardWidget>
</template>
