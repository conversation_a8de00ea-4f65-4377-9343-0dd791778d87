<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DashboardWidget from './DashboardWidget.vue';
import StatCard from './StatCard.vue';
import {
    ShoppingBagIcon,
    CurrencyDollarIcon,
    DocumentTextIcon
} from '@heroicons/vue/24/outline';
import type { SalesData } from '../../types/dashboard';
import Tooltip from '../ui/Tooltip.vue';

interface Props {
    data: SalesData;
    loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
});

const emit = defineEmits(['navigate-to-orders']);

const { t } = useI18n();

// Function to navigate to orders view with appropriate filters
const navigateToOrders = (section?: string) => {
    emit('navigate-to-orders', section);
};

const ordersStats = computed(() => [
    {
        icon: ShoppingBagIcon,
        value: props.data.todayInvoices,
        label: t('dashboard.totalOrders'),
        status: 'default' as const,
        ariaLabel: `${t('dashboard.totalOrders')}: ${props.data.todayInvoices}`,
        tooltip: t('dashboard.tooltips.viewAllOrders'),
        onClick: () => navigateToOrders('orders')
    },
    {
        icon: CurrencyDollarIcon,
        value: `$${props.data.todayRevenue.toLocaleString()}`,
        label: t('dashboard.revenue'),
        status: 'success' as const,
        ariaLabel: `${t('dashboard.revenue')}: $${props.data.todayRevenue.toLocaleString()}`,
        tooltip: t('dashboard.tooltips.viewRevenue'),
        onClick: () => navigateToOrders('revenue')
    },
    {
        icon: DocumentTextIcon,
        value: props.data.todayInvoices,
        label: t('dashboard.invoicesToday'),
        status: 'info' as const,
        ariaLabel: `${t('dashboard.invoicesToday')}: ${props.data.todayInvoices}`,
        tooltip: t('dashboard.tooltips.viewTodayInvoices'),
        onClick: () => navigateToOrders('invoices-today')
    }
]);
</script>

<template>
    <DashboardWidget :title="t('dashboard.ordersInvoices')" id="orders-invoices-summary">
        <div v-if="loading" class="animate-pulse">
            <div class="stat-cards">
                <div v-for="i in 3" :key="i"
                    class="flex items-center p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 lg:p-3 sm:p-3">
                    <div
                        class="flex items-center justify-center w-12 h-12 rounded-lg me-4 lg:w-10 lg:h-10 lg:me-3 sm:w-8 sm:h-8 sm:me-3 bg-gray-200 dark:bg-neutral-700">
                        <div class="w-6 h-6 lg:w-5 lg:h-5 sm:w-5 sm:h-5 bg-gray-300 dark:bg-neutral-600 rounded">
                        </div>
                    </div>
                    <div class="flex flex-col text-start min-w-0 flex-1">
                        <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded mb-2 lg:h-5 sm:h-4"></div>
                        <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-3/4 sm:h-3"></div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else class="grid grid-cols-1 gap-4">
            <Tooltip v-for="(stat, index) in ordersStats" :key="index" :content="stat.tooltip" position="top">
                <div class="cursor-pointer relative transition-all duration-200 ease-in-out rounded-lg hover:-translate-y-1 hover:shadow-md hover:after:content-[''] hover:after:absolute hover:after:inset-0 hover:after:rounded-lg hover:after:border-2 hover:after:border-primary-500 hover:after:pointer-events-none hover:after:opacity-50"
                    @click="stat.onClick">
                    <StatCard :icon="stat.icon" :value="stat.value" :label="stat.label" :status="stat.status"
                        :aria-label="stat.ariaLabel" />
                </div>
            </Tooltip>
        </div>
    </DashboardWidget>
</template>
