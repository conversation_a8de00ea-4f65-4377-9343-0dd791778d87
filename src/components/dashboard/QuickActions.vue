<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import DashboardWidget from './DashboardWidget.vue';
import Tooltip from '../ui/Tooltip.vue';
import {
    PlusIcon,
    ShoppingCartIcon,
    QrCodeIcon,
    DocumentDuplicateIcon
} from '@heroicons/vue/24/outline';

interface Props {
    loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
});

// Setup router
const router = useRouter();

// Emits
const emit = defineEmits(['toggle-qr-generator', 'add-new-medication']);

const { t } = useI18n();

// Handle QR Generator toggle
const toggleQrGenerator = () => {
    emit('toggle-qr-generator');
};

// Handle Add New Medication
const openAddMedicationModal = () => {
    emit('add-new-medication');
};

// Navigate to cart for new sale invoice
const navigateToCart = () => {
    if (!props.loading) {
        router.push('/cart');
    }
};

// Navigate to reports page
const navigateToReports = () => {
    if (!props.loading) {
        router.push('/reports');
    }
};
</script>

<template>
    <DashboardWidget :title="t('dashboard.quickActions')" id="quick-actions">
        <div v-if="loading" class="animate-pulse">
            <div class="flex flex-col gap-3 text-start">
                <div v-for="i in 4" :key="i"
                    class="flex items-center px-4 py-3 bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-md">
                    <div class="w-5 h-5 bg-gray-200 dark:bg-neutral-700 rounded mr-3 rtl:mr-0 rtl:ml-3"></div>
                    <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded flex-1"></div>
                </div>
            </div>
        </div>
        <div v-else class="flex text-start flex-col gap-3 text-start">
            <Tooltip :content="t('medication.addNewMedicationTooltip')" position="top" class="w-full">
                <button
                    class="w-full flex items-center px-4 py-3 bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-md text-gray-800 dark:text-neutral-100 font-medium transition-all duration-200 text-start "
                    :class="loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 hover:-translate-y-0.5 hover:shadow-light focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-gray-500 dark:focus:ring-offset-neutral-850'"
                    @click="openAddMedicationModal" :disabled="loading" aria-label="Add new medication">
                    <PlusIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary-600 dark:text-primary-400"
                        aria-hidden="true" />
                    <span>{{ t('dashboard.addNewMedication') }}</span>
                </button>
            </Tooltip>

            <Tooltip :content="t('orders.clickToViewDetails')" position="top" class="w-full">
                <button
                    class="w-full flex items-center px-4 py-3 bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-md text-gray-800 dark:text-neutral-100 font-medium transition-all duration-200 text-start "
                    :class="loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 hover:-translate-y-0.5 hover:shadow-light focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-gray-500 dark:focus:ring-offset-neutral-850'"
                    @click="navigateToCart" :disabled="loading" aria-label="Create new sale invoice">
                    <ShoppingCartIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary-600 dark:text-primary-400"
                        aria-hidden="true" />
                    <span>{{ t('dashboard.newSaleInvoice') }}</span>
                </button>
            </Tooltip>

            <Tooltip :content="t('dashboard.generateReportsTooltip', 'Generate detailed reports and analytics')"
                position="top" class="w-full">
                <button
                    class="w-full text-start flex items-center px-4 py-3 bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-md text-gray-800 dark:text-neutral-100 font-medium transition-all duration-200 text-start "
                    :class="loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 hover:-translate-y-0.5 hover:shadow-light focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-gray-500 dark:focus:ring-offset-neutral-850'"
                    @click="navigateToReports" :disabled="loading" aria-label="Generate reports and analytics">
                    <DocumentDuplicateIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary-600 dark:text-primary-400"
                        aria-hidden="true" />
                    <span>{{ t('dashboard.generateReports', 'Generate Reports') }}</span>
                </button>
            </Tooltip>

            <Tooltip :content="t('orders.clickToViewDetails')" position="top" class="w-full">
                <button
                    class="w-full flex items-center px-4 py-3 bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-md text-gray-800 dark:text-neutral-100 font-medium transition-all duration-200 text-start "
                    :class="loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 hover:-translate-y-0.5 hover:shadow-light focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-gray-500 dark:focus:ring-offset-neutral-850'"
                    @click="toggleQrGenerator" :disabled="loading" aria-label="Scan QR code to connect device">
                    <QrCodeIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-primary-600 dark:text-primary-400"
                        aria-hidden="true" />
                    <span>{{ t('dashboard.scanQrToConnect') }}</span>
                </button>
            </Tooltip>
        </div>
    </DashboardWidget>
</template>
