<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DashboardWidget from './DashboardWidget.vue';
import DashboardStatGrid from './DashboardStatGrid.vue';
import {
    ArchiveBoxIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
    PlusIcon,
    ExclamationCircleIcon
} from '@heroicons/vue/24/outline';
import type { InventoryData } from '../../types/dashboard';

interface Props {
    data: InventoryData;
    loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
});

const emit = defineEmits(['navigate-to-stock']);

const { t } = useI18n();

// Function to navigate to stock view with appropriate filters
const navigateToStock = (status?: string) => {
    emit('navigate-to-stock', status);
};

const inventoryStats = computed(() => [
    {
        icon: ArchiveBoxIcon,
        value: props.data.totalMedications,
        label: t('dashboard.totalMedications'),
        status: 'default' as const,
        ariaLabel: `${t('dashboard.totalMedications')}: ${props.data.totalMedications}`,
        route: '/stock',
        tooltip: t('dashboard.tooltips.viewAllMedications'),
        onClick: () => navigateToStock()
    },
    {
        icon: XCircleIcon,
        value: props.data.outOfStock,
        label: t('dashboard.outOfStock'),
        status: 'error' as const,
        ariaLabel: `${t('dashboard.outOfStock')}: ${props.data.outOfStock}`,
        route: '/stock?status=out_of_stock',
        tooltip: t('dashboard.tooltips.viewOutOfStock'),
        onClick: () => navigateToStock('out_of_stock')
    },
    {
        icon: ExclamationTriangleIcon,
        value: props.data.expiringSoon,
        label: t('dashboard.expiringSoon'),
        status: 'warning' as const,
        ariaLabel: `${t('dashboard.expiringSoon')}: ${props.data.expiringSoon}`,
        route: '/stock?expiryStatus=expiring_soon',
        tooltip: t('dashboard.tooltips.viewExpiringSoon'),
        onClick: () => navigateToStock('expiring_soon')
    },
    {
        icon: PlusIcon,
        value: props.data.recentlyAdded,
        label: t('dashboard.recentlyAdded'),
        status: 'info' as const,
        ariaLabel: `${t('dashboard.recentlyAdded')}: ${props.data.recentlyAdded}`,
        route: '/stock?status=recent',
        tooltip: t('dashboard.tooltips.viewRecentlyAdded'),
        onClick: () => navigateToStock('recent')
    },
    {
        icon: ExclamationCircleIcon,
        value: props.data.lowStock,
        label: t('dashboard.lowStock', 'Low Stock'),
        status: 'warning' as const,
        ariaLabel: `${t('dashboard.lowStock', 'Low Stock')}: ${props.data.lowStock}`,
        route: '/stock?status=low_stock',
        tooltip: t('dashboard.tooltips.viewLowStock', 'View medications with low stock levels'),
        onClick: () => navigateToStock('low_stock')
    }
]);
</script>

<template>
    <DashboardWidget :title="t('dashboard.inventorySummary')" id="inventory-summary">
        <div v-if="loading" class="animate-pulse">
            <div class="grid grid-cols-1 gap-4 text-start">
                <div v-for="i in 5" :key="i"
                    class="flex items-center p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 lg:p-3 sm:p-3">
                    <div
                        class="flex items-center justify-center w-12 h-12 rounded-lg me-4 lg:w-10 lg:h-10 lg:me-3 sm:w-8 sm:h-8 sm:me-3 bg-gray-200 dark:bg-neutral-700">
                        <div class="w-6 h-6 lg:w-5 lg:h-5 sm:w-5 sm:h-5 bg-gray-300 dark:bg-neutral-600 rounded">
                        </div>
                    </div>
                    <div class="flex flex-col text-start min-w-0 flex-1">
                        <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded mb-2 lg:h-5 sm:h-4"></div>
                        <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-3/4 sm:h-3"></div>
                    </div>
                </div>
            </div>
        </div>
        <DashboardStatGrid v-else :stats="inventoryStats" :loading="false" :columns="1" />
    </DashboardWidget>
</template>
