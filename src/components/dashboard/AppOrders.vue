<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DashboardWidget from './DashboardWidget.vue';
import DashboardStatGrid from './DashboardStatGrid.vue';
import {
    UserGroupIcon,
    UserIcon,
    ShoppingBagIcon,
    CreditCardIcon,
    StarIcon
} from '@heroicons/vue/24/outline';
import type { AppOrdersData } from '../../types/dashboard';

interface Props {
    data: AppOrdersData;
    loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
});

const emit = defineEmits(['navigate-to-app-orders']);

const { t } = useI18n();

// Function to navigate to app orders view with appropriate filters
const navigateToAppOrders = (section?: string) => {
    emit('navigate-to-app-orders', section);
};

const userStats = computed(() => [
    {
        icon: UserGroupIcon,
        value: props.data.totalUsers,
        label: t('dashboard.totalUsers'),
        status: 'default' as const,
        ariaLabel: `${t('dashboard.totalUsers')}: ${props.data.totalUsers}`,
        tooltip: t('orders.clickToViewDetails'),
        onClick: () => navigateToAppOrders('total-users')
    },
    {
        icon: UserIcon,
        value: props.data.activeUsers,
        label: t('dashboard.activeUsers'),
        status: 'success' as const,
        ariaLabel: `${t('dashboard.activeUsers')}: ${props.data.activeUsers}`,
        tooltip: t('orders.clickToViewDetails'),
        onClick: () => navigateToAppOrders('active-users')
    }
]);

const orderStats = computed(() => [
    {
        icon: ShoppingBagIcon,
        value: props.data.totalOrders,
        label: t('dashboard.totalOrders'),
        status: 'info' as const,
        ariaLabel: `${t('dashboard.totalOrders')}: ${props.data.totalOrders}`,
        tooltip: t('orders.clickToViewDetails'),
        onClick: () => navigateToAppOrders('total-orders')
    },
    {
        icon: CreditCardIcon,
        value: `$${props.data.averageOrderValue.toFixed(2)}`,
        label: t('dashboard.averageOrderValue'),
        status: 'warning' as const,
        ariaLabel: `${t('dashboard.averageOrderValue')}: $${props.data.averageOrderValue.toFixed(2)}`,
        tooltip: t('orders.clickToViewDetails'),
        onClick: () => navigateToAppOrders('average-order-value')
    },
    {
        icon: StarIcon,
        value: props.data.pendingReviews,
        label: t('dashboard.pendingReviews', 'Pending Reviews'),
        status: 'info' as const,
        ariaLabel: `${t('dashboard.pendingReviews', 'Pending Reviews')}: ${props.data.pendingReviews}`,
        tooltip: t('dashboard.tooltips.viewPendingReviews', 'View customer reviews awaiting response'),
        onClick: () => navigateToAppOrders('pending-reviews')
    }
]);
</script>

<template>
    <DashboardWidget :title="t('dashboard.appOrders')" id="app-orders">
        <div v-if="loading" class="animate-pulse">
            <div class="grid grid-cols-1 gap-4 text-start">
                <!-- User Stats Skeleton -->
                <div v-for="i in 2" :key="`user-${i}`"
                    class="flex items-center p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 lg:p-3 sm:p-3">
                    <div
                        class="flex items-center justify-center w-12 h-12 rounded-lg me-4 lg:w-10 lg:h-10 lg:me-3 sm:w-8 sm:h-8 sm:me-3 bg-gray-200 dark:bg-neutral-700">
                        <div class="w-6 h-6 lg:w-5 lg:h-5 sm:w-5 sm:h-5 bg-gray-300 dark:bg-neutral-600 rounded">
                        </div>
                    </div>
                    <div class="flex flex-col text-start min-w-0 flex-1">
                        <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded mb-2 lg:h-5 sm:h-4"></div>
                        <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-3/4 sm:h-3"></div>
                    </div>
                </div>

                <!-- Order Stats Skeleton -->
                <div v-for="i in 3" :key="`order-${i}`"
                    class="flex items-center p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 lg:p-3 sm:p-3">
                    <div
                        class="flex items-center justify-center w-12 h-12 rounded-lg me-4 lg:w-10 lg:h-10 lg:me-3 sm:w-8 sm:h-8 sm:me-3 bg-gray-200 dark:bg-neutral-700">
                        <div class="w-6 h-6 lg:w-5 lg:h-5 sm:w-5 sm:h-5 bg-gray-300 dark:bg-neutral-600 rounded">
                        </div>
                    </div>
                    <div class="flex flex-col text-start min-w-0 flex-1">
                        <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded mb-2 lg:h-5 sm:h-4"></div>
                        <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-3/4 sm:h-3"></div>
                    </div>
                </div>
            </div>
        </div>
        <template v-else>
            <DashboardStatGrid :stats="userStats" :loading="false" :columns="1" />
            <div class="mt-4"></div>
            <DashboardStatGrid :stats="orderStats" :loading="false" :columns="1" />
        </template>
    </DashboardWidget>
</template>
