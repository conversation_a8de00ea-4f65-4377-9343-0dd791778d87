<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DashboardWidget from './DashboardWidget.vue';
import DashboardStatGrid from './DashboardStatGrid.vue';
import {
    ShoppingCartIcon,
    CurrencyDollarIcon,
    DocumentTextIcon
} from '@heroicons/vue/24/outline';
import type { SalesData } from '../../types/dashboard';

interface Props {
    data: SalesData;
    loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
});

const emit = defineEmits(['navigate-to-orders']);

const { t } = useI18n();

// Function to navigate to orders view with appropriate filters
const navigateToOrders = (section?: string) => {
    emit('navigate-to-orders', section);
};

const salesStats = computed(() => [
    {
        icon: ShoppingCartIcon,
        value: props.data.todayInvoices,
        label: t('dashboard.invoices'),
        status: 'default' as const,
        ariaLabel: `${t('dashboard.invoices')}: ${props.data.todayInvoices}`,
        tooltip: t('dashboard.tooltips.viewTodayInvoices'),
        onClick: () => navigateToOrders('invoices-today')
    },
    {
        icon: CurrencyDollarIcon,
        value: `$${props.data.todayRevenue.toLocaleString()}`,
        label: t('dashboard.revenue'),
        status: 'success' as const,
        ariaLabel: `${t('dashboard.revenue')}: $${props.data.todayRevenue.toLocaleString()}`,
        tooltip: t('dashboard.tooltips.viewRevenue'),
        onClick: () => navigateToOrders('revenue')
    }
]);
</script>

<template>
    <DashboardWidget :title="t('dashboard.todaysSales')" id="sales-summary">
        <div v-if="loading" class="animate-pulse">
            <!-- Sales Stats Skeleton (2 items) -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-start">
                <div v-for="i in 2" :key="i"
                    class="flex items-center p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 lg:p-3 sm:p-3">
                    <div
                        class="flex items-center justify-center w-12 h-12 rounded-lg me-4 lg:w-10 lg:h-10 lg:me-3 sm:w-8 sm:h-8 sm:me-3 bg-gray-200 dark:bg-neutral-700">
                        <div class="w-6 h-6 lg:w-5 lg:h-5 sm:w-5 sm:h-5 bg-gray-300 dark:bg-neutral-600 rounded">
                        </div>
                    </div>
                    <div class="flex flex-col text-start min-w-0 flex-1">
                        <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded mb-2 lg:h-5 sm:h-4"></div>
                        <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-3/4 sm:h-3"></div>
                    </div>
                </div>
            </div>

            <!-- Top Selling Medications Skeleton -->
            <div
                class="mt-6 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 p-4">
                <div class="h-5 bg-gray-200 dark:bg-neutral-700 rounded w-48 mb-3"></div>
                <div class="space-y-3">
                    <div v-for="i in 5" :key="`top-${i}`"
                        class="flex items-center p-3 border-b border-gray-200 dark:border-neutral-700 last:border-b-0">
                        <div class="w-6 h-6 bg-gray-200 dark:bg-neutral-700 rounded-full me-3"></div>
                        <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded flex-1 me-3"></div>
                        <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-16"></div>
                    </div>
                </div>
            </div>
        </div>
        <template v-else>
            <DashboardStatGrid :stats="salesStats" :loading="false" :columns="2" gap="md" />
            <div class="mt-6"></div>

            <div
                class="mt-6 bg-white dark:bg-neutral-850 rounded-lg border border-gray-200 dark:border-neutral-700 p-4 text-start">
                <h4 class="text-base font-semibold text-gray-800 dark:text-gray-100 mt-0 mb-3 text-start"
                    id="top-selling-title">
                    {{ t('dashboard.topSellingMedications') }}
                </h4>
                <ul class="list-none p-0 m-0" aria-labelledby="top-selling-title">
                    <li v-for="(item, index) in data.topSelling" :key="index"
                        class="flex items-center p-3 border-b border-gray-200 dark:border-neutral-700 last:border-b-0 "
                        :aria-label="`${index + 1}. ${item.name}: ${item.count} ${t('dashboard.units')}`">
                        <span
                            class="flex items-center justify-center w-6 h-6 bg-primary-600 text-white rounded-full font-semibold text-sm me-3"
                            aria-hidden="true">{{ index + 1 }}</span>
                        <span class="flex-1 font-medium text-gray-800 dark:text-gray-100 text-start">{{ item.name
                        }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400 text-start">{{ item.count }} {{
                            t('dashboard.units') }}</span>
                    </li>
                </ul>
            </div>
        </template>
    </DashboardWidget>
</template>
