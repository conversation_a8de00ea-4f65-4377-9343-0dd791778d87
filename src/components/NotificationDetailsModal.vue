<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { 
  XMarkIcon, 
  BellIcon, 
  BellAlertIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline';

interface Notification {
  id: number;
  title: string | { value: string };
  message: string | { value: string };
  time: string | { value: string };
  read: boolean;
  type?: string;
  priority?: 'low' | 'medium' | 'high';
  category?: string;
}

interface Props {
  notification: Notification | null;
  show: boolean;
}

interface Emits {
  (e: 'close'): void;
  (e: 'mark-as-read', notificationId: number): void;
  (e: 'mark-as-unread', notificationId: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t } = useI18n();

// Computed properties for notification data
const notificationTitle = computed(() => {
  if (!props.notification) return '';
  return typeof props.notification.title === 'string' 
    ? props.notification.title 
    : props.notification.title.value;
});

const notificationMessage = computed(() => {
  if (!props.notification) return '';
  return typeof props.notification.message === 'string' 
    ? props.notification.message 
    : props.notification.message.value;
});

const notificationTime = computed(() => {
  if (!props.notification) return '';
  return typeof props.notification.time === 'string' 
    ? props.notification.time 
    : props.notification.time.value;
});

// Priority badge styling
const priorityClasses = computed(() => {
  if (!props.notification?.priority) return 'bg-gray-100 text-gray-800 dark:bg-neutral-800 dark:text-neutral-200';
  
  switch (props.notification.priority) {
    case 'high':
      return 'bg-error-100 text-error-800 dark:bg-error-900/20 dark:text-error-200';
    case 'medium':
      return 'bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-200';
    case 'low':
    default:
      return 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-200';
  }
});

// Handle actions
const closeModal = () => {
  emit('close');
};

const toggleReadStatus = () => {
  if (!props.notification) return;
  
  if (props.notification.read) {
    emit('mark-as-unread', props.notification.id);
  } else {
    emit('mark-as-read', props.notification.id);
  }
};

// Handle keyboard events
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeModal();
  }
};
</script>

<template>
  <!-- Modal Backdrop -->
  <div v-if="show && notification" 
    class="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-[100] p-4"
    @click="closeModal"
    @keydown="handleKeydown"
    tabindex="-1">
    
    <!-- Modal Content -->
    <div 
      class="bg-white dark:bg-neutral-850 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden border border-gray-200 dark:border-neutral-700"
      @click.stop>
      
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-neutral-700">
        <div class="flex items-center gap-3">
          <div class="flex items-center justify-center w-10 h-10 rounded-lg"
            :class="notification.read ? 'bg-gray-100 dark:bg-neutral-800' : 'bg-teal-100 dark:bg-teal-900/20'">
            <BellAlertIcon v-if="!notification.read" class="w-5 h-5 text-teal-600 dark:text-teal-400" />
            <BellIcon v-else class="w-5 h-5 text-gray-500 dark:text-neutral-400" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100">
              {{ t('notifications.notificationDetails') }}
            </h2>
            <p class="text-sm text-gray-500 dark:text-neutral-400">
              {{ notification.read ? t('notifications.read') : t('notifications.unread') }}
            </p>
          </div>
        </div>
        
        <button @click="closeModal"
          class="p-2 hover:bg-gray-100 dark:hover:bg-neutral-800 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-neutral-850"
          :aria-label="t('common.close')">
          <XMarkIcon class="w-5 h-5 text-gray-500 dark:text-neutral-400" />
        </button>
      </div>
      
      <!-- Modal Body -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
        <!-- Notification Content -->
        <div class="space-y-6">
          <!-- Title -->
          <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-neutral-100 mb-2">
              {{ notificationTitle }}
            </h3>
            
            <!-- Metadata -->
            <div class="flex flex-wrap items-center gap-3 text-sm text-gray-500 dark:text-neutral-400">
              <div class="flex items-center gap-1">
                <ClockIcon class="w-4 h-4" />
                <span>{{ notificationTime }}</span>
              </div>
              
              <span v-if="notification.priority" 
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="priorityClasses">
                {{ t(`notifications.priority.${notification.priority}`) }}
              </span>
              
              <span v-if="notification.category"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-200">
                {{ notification.category }}
              </span>
            </div>
          </div>
          
          <!-- Message -->
          <div class="prose prose-sm dark:prose-invert max-w-none">
            <p class="text-gray-700 dark:text-neutral-300 leading-relaxed">
              {{ notificationMessage }}
            </p>
          </div>
        </div>
      </div>
      
      <!-- Modal Footer -->
      <div class="flex items-center justify-between p-6 border-t border-gray-200 dark:border-neutral-700 bg-gray-50 dark:bg-neutral-800/50">
        <button @click="toggleReadStatus"
          class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-850"
          :class="notification.read 
            ? 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-neutral-700 dark:text-neutral-200 dark:hover:bg-neutral-600 focus:ring-gray-500' 
            : 'bg-primary-600 text-white hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600 focus:ring-primary-500'">
          <CheckCircleIcon class="w-4 h-4" />
          <span>{{ notification.read ? t('notifications.markAsUnread') : t('notifications.markAsRead') }}</span>
        </button>
        
        <button @click="closeModal"
          class="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 dark:bg-neutral-700 dark:text-neutral-200 dark:hover:bg-neutral-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-neutral-850">
          {{ t('common.close') }}
        </button>
      </div>
    </div>
  </div>
</template>
