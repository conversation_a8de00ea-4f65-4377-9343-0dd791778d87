<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import SkeletonLoader from './ui/SkeletonLoader.vue';

// Props
defineProps<{
  message?: string;
}>();

// i18n
const { t } = useI18n();
</script>

<template>
  <div class="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-70 flex justify-center items-center z-[9999]">
    <div class="bg-white dark:bg-neutral-850 rounded-lg p-8 flex flex-col items-center shadow-lg max-w-sm w-full mx-4">
      <SkeletonLoader type="card" class="w-full" />
      <p class="text-gray-700 dark:text-neutral-300 mt-4 font-medium text-center">{{ message || t('common.loading') }}
      </p>
    </div>
  </div>
</template>
