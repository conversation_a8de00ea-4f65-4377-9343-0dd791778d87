<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useTheme } from '../theme/useTheme';

// Lazy load Leaflet only when component is mounted
let L: any = null;
let leafletLoaded = false;

const loadLeaflet = async () => {
  if (leafletLoaded) return L;

  // Dynamically import Leaflet CSS and JS
  await import('leaflet/dist/leaflet.css');
  L = (await import('leaflet')).default;

  // Fix for Leaflet marker icons in production build
  const iconRetinaUrl = (await import('leaflet/dist/images/marker-icon-2x.png')).default;
  const iconUrl = (await import('leaflet/dist/images/marker-icon.png')).default;
  const shadowUrl = (await import('leaflet/dist/images/marker-shadow.png')).default;

  // Delete the default icon
  delete L.Icon.Default.prototype._getIconUrl;

  // Set up the default icon
  L.Icon.Default.mergeOptions({
    iconRetinaUrl,
    iconUrl,
    shadowUrl
  });

  leafletLoaded = true;
  return L;
};

interface Props {
  pharmacyLocation: [number, number]; // [latitude, longitude]
  customerLocation: [number, number]; // [latitude, longitude]
  driverLocation?: [number, number]; // [latitude, longitude]
  simulateDriverMovement?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  driverLocation: () => [0, 0],
  simulateDriverMovement: true
});

const { t } = useI18n();
const { isDarkMode } = useTheme();
const mapContainer = ref<HTMLElement | null>(null);
const map = ref<L.Map | null>(null);
const driverMarker = ref<L.Marker | null>(null);
const driverPath = ref<L.Polyline | null>(null);
const simulationInterval = ref<number | null>(null);

// Custom icons
const createCustomIcon = (color: string, iconUrl: string) => {
  return L.divIcon({
    className: 'custom-div-icon',
    html: `<div style="background-color: ${color}; width: 30px; height: 30px; border-radius: 50%; display: flex; justify-content: center; align-items: center; box-shadow: 0 0 10px rgba(0,0,0,0.3);">
            <img src="${iconUrl}" style="width: 20px; height: 20px;" />
           </div>`,
    iconSize: [30, 30],
    iconAnchor: [15, 15]
  });
};

// Initialize map
const initMap = async () => {
  if (!mapContainer.value) return;

  // Load Leaflet first
  await loadLeaflet();
  if (!L) return;

  // Create map
  map.value = L.map(mapContainer.value).setView(props.pharmacyLocation, 13);

  // Add tile layer
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
  }).addTo(map.value);

  // Add pharmacy marker
  const pharmacyIcon = createCustomIcon('#4F46E5', '/icons/pharmacy.svg');
  L.marker(props.pharmacyLocation, { icon: pharmacyIcon })
    .addTo(map.value)
    .bindPopup(t('orders.pharmacy'));

  // Add customer marker
  const customerIcon = createCustomIcon('#10B981', '/icons/customer.svg');
  L.marker(props.customerLocation, { icon: customerIcon })
    .addTo(map.value)
    .bindPopup(t('orders.customer'));

  // Add driver marker if available
  if (props.driverLocation[0] !== 0 && props.driverLocation[1] !== 0) {
    const driverIcon = createCustomIcon('#F59E0B', '/icons/driver.svg');
    driverMarker.value = L.marker(props.driverLocation, { icon: driverIcon })
      .addTo(map.value)
      .bindPopup(t('orders.driver'));

    // Create a polyline for the driver's path
    driverPath.value = L.polyline([props.driverLocation], { color: '#F59E0B', weight: 3 })
      .addTo(map.value);

    // Start simulation if enabled
    if (props.simulateDriverMovement) {
      startDriverSimulation();
    }
  }

  // Fit bounds to show all markers
  const bounds = L.latLngBounds([props.pharmacyLocation, props.customerLocation]);
  if (props.driverLocation[0] !== 0 && props.driverLocation[1] !== 0) {
    bounds.extend(props.driverLocation);
  }
  map.value.fitBounds(bounds, { padding: [50, 50] });
};

// Simulate driver movement
const startDriverSimulation = () => {
  if (!map.value || !driverMarker.value || !driverPath.value) return;

  const start = props.driverLocation;
  const end = props.customerLocation;
  const totalSteps = 20;
  let currentStep = 0;

  // Calculate step size
  const latStep = (end[0] - start[0]) / totalSteps;
  const lngStep = (end[1] - start[1]) / totalSteps;

  // Update driver position every 2 seconds
  simulationInterval.value = window.setInterval(() => {
    if (currentStep >= totalSteps) {
      if (simulationInterval.value) {
        clearInterval(simulationInterval.value);
        simulationInterval.value = null;
      }
      return;
    }

    currentStep++;
    const newLat = start[0] + latStep * currentStep;
    const newLng = start[1] + lngStep * currentStep;
    const newPos: [number, number] = [newLat, newLng];

    // Update marker position
    driverMarker.value?.setLatLng(newPos);

    // Add point to path
    const currentPath = driverPath.value?.getLatLngs() as L.LatLng[];
    currentPath.push(L.latLng(newPos));
    driverPath.value?.setLatLngs(currentPath);

  }, 2000);
};

// Watch for dark mode changes to update map tiles
watch(() => isDarkMode.value, () => {
  // If we had a dark mode tile provider, we would switch here
  // For now, we'll just use the default OpenStreetMap tiles
});

onMounted(() => {
  // Initialize map after component is mounted
  initMap();
});

onBeforeUnmount(() => {
  // Clean up map and simulation when component is unmounted
  if (simulationInterval.value) {
    clearInterval(simulationInterval.value);
  }
  if (map.value) {
    map.value.remove();
  }
});
</script>

<template>
  <div class="w-full h-full min-h-[300px] rounded-lg overflow-hidden border border-gray-300 dark:border-slate-600">
    <div ref="mapContainer" class="w-full h-full min-h-[300px] dark:brightness-75 dark:contrast-125"></div>
  </div>
</template>
