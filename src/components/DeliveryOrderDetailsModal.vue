<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useTheme } from '../theme/useTheme';
import Modal from './Modal.vue';
import LiveTrackingMap from './LiveTrackingMap.vue';
import {
  PhoneIcon,
  ClipboardDocumentIcon,
  MapPinIcon,
  UserIcon,
  TruckIcon,
  ClockIcon,
  CheckCircleIcon,
  BeakerIcon as PillIcon
} from '@heroicons/vue/24/outline';
import type { DeliveryOrder } from '../types/orders';

interface Props {
  show: boolean;
  order: DeliveryOrder | null;
}

const props = withDefaults(defineProps<Props>(), {
  order: null
});

const emit = defineEmits(['close']);

const { t } = useI18n();
const { isDarkMode } = useTheme();

// Mock data for the map
const pharmacyLocation = ref<[number, number]>([30.0444, 31.2357]); // Cairo coordinates
const customerLocation = ref<[number, number]>([30.0571, 31.2272]); // Nearby location
const driverLocation = ref<[number, number]>([30.0500, 31.2300]); // Between pharmacy and customer

// Mock data for medications in the order
const orderMedications = ref([
  { id: 1, name: 'Paracetamol 500mg', quantity: 2 },
  { id: 2, name: 'Vitamin C 1000mg', quantity: 1 },
  { id: 3, name: 'Amoxicillin 250mg', quantity: 1 }
]);

// Computed property to determine if we should show the tracking map
const showTrackingMap = computed(() => {
  return props.order?.status === 'out_for_delivery';
});

// Computed property to get status class
const statusClass = computed(() => {
  if (!props.order) return '';

  switch (props.order.status) {
    case 'pending':
      return 'status-pending';
    case 'out_for_delivery':
      return 'status-in-transit';
    case 'delivered':
      return 'status-delivered';
    default:
      return '';
  }
});

// Function to copy phone number to clipboard
const copyPhoneNumber = () => {
  if (props.order?.contactNumber) {
    navigator.clipboard.writeText(props.order.contactNumber);
    // Could add a notification here
  }
};
</script>

<template>
  <Modal :show="show" :title="t('orders.deliveryOrderDetails')" size="lg" @close="emit('close')">
    <div v-if="order" class="flex flex-col gap-6 text-start">
      <!-- Order Info Section -->
      <div class="flex flex-col gap-4">
        <div class="flex justify-between items-center mb-4 flex-wrap gap-4">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white m-0">{{ t('orders.orderId') }}: {{
            order.orderId }}</h3>
          <div class="flex items-center gap-2 px-4 py-2 rounded-full font-medium" :class="{
            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': order.status === 'pending',
            'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200': order.status === 'out_for_delivery',
            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': order.status === 'delivered'
          }">
            <component :is="order.status === 'out_for_delivery' ? TruckIcon : CheckCircleIcon" class="w-5 h-5" />
            <span>{{ t(`orders.statuses.${order.status}`) }}</span>
            <span v-if="order.status === 'out_for_delivery'"
              class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs ml-2 animate-pulse">
              {{ t('orders.liveTracking') }}
            </span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Customer Info -->
          <div class="flex gap-4">
            <div
              class="flex items-start justify-center w-10 h-10 bg-gray-100 dark:bg-slate-700 rounded-lg p-2 flex-shrink-0">
              <UserIcon class="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ t('orders.customer') }}</h4>
              <p class="text-base text-gray-900 dark:text-white m-0">{{ order.customerName }}</p>
            </div>
          </div>

          <!-- Phone Number -->
          <div class="flex gap-4">
            <div
              class="flex items-start justify-center w-10 h-10 bg-gray-100 dark:bg-slate-700 rounded-lg p-2 flex-shrink-0">
              <PhoneIcon class="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ t('orders.phoneNumber') }}</h4>
              <div class="flex items-center gap-2">
                <p class="text-base text-gray-900 dark:text-white m-0">{{ order.contactNumber || t('orders.notProvided')
                }}</p>
                <button v-if="order.contactNumber" @click="copyPhoneNumber"
                  class="bg-gray-100 dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded px-2 py-1 text-xs cursor-pointer transition-colors hover:bg-gray-200 dark:hover:bg-slate-600">
                  {{ t('orders.copyNumber') }}
                </button>
              </div>
            </div>
          </div>

          <!-- Delivery Address -->
          <div class="flex gap-4">
            <div
              class="flex items-start justify-center w-10 h-10 bg-gray-100 dark:bg-slate-700 rounded-lg p-2 flex-shrink-0">
              <MapPinIcon class="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ t('orders.deliveryAddress') }}
              </h4>
              <p class="text-base text-gray-900 dark:text-white m-0">{{ order.address }}</p>
            </div>
          </div>

          <!-- Expected Delivery Time -->
          <div class="flex gap-4">
            <div
              class="flex items-start justify-center w-10 h-10 bg-gray-100 dark:bg-slate-700 rounded-lg p-2 flex-shrink-0">
              <ClockIcon class="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">{{
                t('orders.expectedDeliveryTime') }}</h4>
              <p class="text-base text-gray-900 dark:text-white m-0">{{ new Date(order.deliveryTime).toLocaleString() }}
              </p>
            </div>
          </div>

          <!-- Delivery Partner -->
          <div class="flex gap-4">
            <div
              class="flex items-start justify-center w-10 h-10 bg-gray-100 dark:bg-slate-700 rounded-lg p-2 flex-shrink-0">
              <TruckIcon class="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ t('orders.deliveryPartner') }}
              </h4>
              <p class="text-base text-gray-900 dark:text-white m-0">{{ t(`orders.partners.${order.partner}`) }}</p>
            </div>
          </div>

          <!-- Order Items -->
          <div class="flex gap-4 col-span-full">
            <div
              class="flex items-start justify-center w-10 h-10 bg-gray-100 dark:bg-slate-700 rounded-lg p-2 flex-shrink-0">
              <PillIcon class="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ t('orders.orderedMedications')
              }}</h4>
              <ul class="list-none p-0 m-0 flex flex-col gap-2">
                <li v-for="med in orderMedications" :key="med.id"
                  class="flex justify-between p-2 bg-gray-100 dark:bg-slate-700 rounded">
                  <span class="font-medium text-gray-900 dark:text-white">{{ med.name }}</span>
                  <span class="font-semibold text-primary-600 dark:text-primary-400">x{{ med.quantity }}</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- Notes if available -->
          <div v-if="order.notes" class="flex gap-4 col-span-full">
            <div
              class="flex items-start justify-center w-10 h-10 bg-gray-100 dark:bg-slate-700 rounded-lg p-2 flex-shrink-0">
              <ClipboardDocumentIcon class="w-6 h-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ t('orders.notes') }}</h4>
              <p class="text-base text-gray-900 dark:text-white m-0">{{ order.notes }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Live Tracking Map Section -->
      <div v-if="showTrackingMap" class="flex flex-col gap-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 text-start dark:text-gray-100">{{ t('orders.liveTracking')
          }}</h3>
        <LiveTrackingMap :pharmacy-location="pharmacyLocation" :customer-location="customerLocation"
          :driver-location="driverLocation" :simulate-driver-movement="true" />
      </div>
    </div>

    <template #footer>
      <button
        class="bg-gray-100 dark:bg-slate-700 text-gray-900 dark:text-white border border-gray-300 dark:border-slate-600 rounded px-4 py-2 font-medium cursor-pointer transition-colors hover:bg-gray-200 dark:hover:bg-slate-600"
        @click="emit('close')">{{ t('common.close') }}</button>
    </template>
  </Modal>
</template>
