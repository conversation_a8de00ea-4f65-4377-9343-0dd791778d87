<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { CheckCircleIcon, ExclamationCircleIcon, XMarkIcon } from '@heroicons/vue/24/outline';

interface Props {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  autoClose?: boolean;
  duration?: number;
  showIcon?: boolean;
  showCloseButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoClose: true,
  duration: 3000,
  showIcon: true,
  showCloseButton: true
});

const emit = defineEmits(['close']);

const isVisible = ref(true);
let autoCloseTimeout: number | null = null;

const getTypeClasses = () => {
  switch (props.type) {
    case 'success':
      return 'bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800';
    case 'error':
      return 'bg-red-50 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800';
    case 'warning':
      return 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800';
    case 'info':
      return 'bg-teal-50 text-teal-800 border-teal-200 dark:bg-teal-900/20 dark:text-teal-400 dark:border-teal-800';
    default:
      return 'bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800';
  }
};

const getIcon = () => {
  switch (props.type) {
    case 'success':
      return CheckCircleIcon;
    case 'error':
    case 'warning':
    case 'info':
      return ExclamationCircleIcon;
    default:
      return CheckCircleIcon;
  }
};

const closeAlert = () => {
  isVisible.value = false;
  if (autoCloseTimeout) {
    window.clearTimeout(autoCloseTimeout);
    autoCloseTimeout = null;
  }
  emit('close');
};

const startAutoCloseTimer = () => {
  if (props.autoClose && props.duration > 0) {
    autoCloseTimeout = window.setTimeout(() => {
      closeAlert();
    }, props.duration);
  }
};

onMounted(() => {
  startAutoCloseTimer();
});

// Reset timer when message changes
watch(() => props.message, () => {
  if (autoCloseTimeout) {
    window.clearTimeout(autoCloseTimeout);
    autoCloseTimeout = null;
  }
  isVisible.value = true;
  startAutoCloseTimer();
});
</script>

<template>
  <transition enter-active-class="transition-all duration-300 ease-in-out"
    leave-active-class="transition-all duration-300 ease-in-out" enter-from-class="opacity-0 -translate-y-2"
    leave-to-class="opacity-0 -translate-y-2">
    <div v-if="isVisible" class="flex items-center justify-between p-3 rounded-md border mb-4 shadow-sm"
      :class="getTypeClasses()">
      <div class="flex items-center gap-2">
        <component v-if="showIcon" :is="getIcon()" class="w-5 h-5 flex-shrink-0" />
        <span class="text-sm font-medium">{{ message }}</span>
      </div>
      <button v-if="showCloseButton" @click="closeAlert"
        class="bg-transparent border-none p-1 flex items-center justify-center cursor-pointer text-current opacity-70 transition-opacity duration-200 hover:opacity-100"
        aria-label="Close alert">
        <XMarkIcon class="w-4 h-4" />
      </button>
    </div>
  </transition>
</template>
