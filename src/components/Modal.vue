<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { XMarkIcon } from '@heroicons/vue/24/outline';

interface Props {
  show: boolean;
  title: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnEsc?: boolean;
  closeOnClickOutside?: boolean;
  showCloseButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closeOnEsc: true,
  closeOnClickOutside: true,
  showCloseButton: true
});

const emit = defineEmits(['close']);
const modalRef = ref<HTMLDivElement | null>(null);

// Handle ESC key press to close modal
const handleKeyDown = (event: KeyboardEvent) => {
  if (props.closeOnEsc && event.key === 'Escape' && props.show) {
    emit('close');
  }
};

// <PERSON>le click outside to close modal
const handleClickOutside = (event: MouseEvent) => {
  if (
    props.closeOnClickOutside &&
    modalRef.value &&
    !modalRef.value.contains(event.target as Node)
  ) {
    emit('close');
  }
};

// Prevent body scrolling when modal is open
watch(() => props.show, (newVal) => {
  if (newVal) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
});

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown);
  document.addEventListener('mousedown', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyDown);
  document.removeEventListener('mousedown', handleClickOutside);
  document.body.style.overflow = '';
});
</script>

<template>
  <transition enter-active-class="transition-opacity duration-300 ease-in-out"
    leave-active-class="transition-opacity duration-300 ease-in-out" enter-from-class="opacity-0"
    leave-to-class="opacity-0">
    <div v-if="show"
      class="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-[9999]">
      <div class="flex justify-center items-center h-full w-full p-4 sm:p-0">
        <div
          class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-neutral-850 dark:border-neutral-700 flex flex-col max-h-[90vh] w-full overflow-hidden"
          :class="{
            'max-w-md': size === 'sm',
            'max-w-2xl': size === 'md',
            'max-w-4xl': size === 'lg',
            'max-w-6xl': size === 'xl',
            'max-h-full rounded-none sm:max-h-[90vh] sm:rounded-lg': true
          }" ref="modalRef">
          <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200 dark:border-neutral-700 ">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-neutral-100 m-0 rtl:text-right">{{ title }}</h2>
            <button v-if="showCloseButton"
              class="bg-transparent border-none cursor-pointer p-2 flex items-center justify-center rounded text-gray-600 dark:text-neutral-400 transition-colors duration-200 hover:bg-gray-100 dark:hover:bg-neutral-800 hover:text-gray-800 dark:hover:text-neutral-200"
              @click="emit('close')" :aria-label="$t('common.close')">
              <XMarkIcon class="w-5 h-5" />
            </button>
          </div>
          <div class="p-6 overflow-y-auto flex-1">
            <slot></slot>
          </div>
          <div
            class="px-6 py-4 border-t border-gray-200 dark:border-neutral-700 flex justify-end gap-2 rtl:justify-start">
            <slot name="footer"></slot>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>
