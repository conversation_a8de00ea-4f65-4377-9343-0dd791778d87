<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import Modal from '../Modal.vue';
import AlertMessage from '../AlertMessage.vue';
import Notification from '../Notification.vue';
import PackSelectionModal from '../cart/PackSelectionModal.vue';
import {
  PlusIcon,
  MinusIcon,
  ShoppingCartIcon,
  TrashIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';
import type { DetailedMedicationItem } from '../../types/cart';
import orderPackStore from '../../store/orderPackStore';

// Props
interface Props {
  show: boolean;
  medication: DetailedMedicationItem | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits(['close', 'delete']);

// Setup i18n
const { t } = useI18n();

// Local state
const quantity = ref(1);
const showDeleteConfirm = ref(false);
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationType = ref<'success' | 'error' | 'info'>('success');
const showPackSelection = ref(false);

// Check if item is already in current pack
const isInCart = computed(() => {
  if (!props.medication) return false;
  return orderPackStore.isInCurrentPack(props.medication.id);
});

// Get current quantity in current pack if item exists
const cartQuantity = computed(() => {
  if (!props.medication) return 0;
  const cartItem = orderPackStore.getCurrentPackItem(props.medication.id);
  return cartItem ? cartItem.quantity : 0;
});

// Pack management computed properties
const activePacks = computed(() => orderPackStore.activePacks.value);
const hasActivePacks = computed(() => activePacks.value.length > 0);
const hasMultiplePacks = computed(() => activePacks.value.length > 1);
const currentPackId = computed(() => orderPackStore.state.currentPackId);
const canAddMorePacks = computed(() => orderPackStore.canAddPacks.value);
const maxPacksReached = computed(() => orderPackStore.maxPacksReached.value);

// Determine what button to show
const shouldShowPackSelection = computed(() => {
  // Show pack selection if:
  // 1. There are multiple packs, OR
  // 2. There's at least one pack and we can add more packs
  return hasMultiplePacks.value || (hasActivePacks.value && canAddMorePacks.value);
});

// Show max packs error when trying to add to new pack but limit is reached
const showMaxPacksError = computed(() => {
  return maxPacksReached.value && !hasActivePacks.value;
});

// Helper function to get pack display name
const getPackDisplayName = (pack: any) => {
  if (pack.type === 'local' && pack.localOrderInfo) {
    return t('cart.orderPacks.localOrder', { number: pack.localOrderInfo.orderNumber });
  } else if (pack.type === 'auto-accepted') {
    return pack.userInfo?.userName || t('cart.orderPacks.autoAcceptedOrder');
  } else if (pack.type === 'manual') {
    return t('cart.orderPacks.manualOrder');
  }
  return t('cart.orderPacks.unknownOrder');
};

// Calculate price with discount if applicable
const finalPrice = computed(() => {
  if (!props.medication) return 0;

  if (props.medication.discount?.hasDiscount && props.medication.discount.percentage) {
    const discountAmount = props.medication.pricePerUnit * (props.medication.discount.percentage / 100);
    return props.medication.pricePerUnit - discountAmount;
  }

  return props.medication.pricePerUnit;
});

// Check if medication is expiring soon (within 30 days)
const isExpiringSoon = computed(() => {
  if (!props.medication) return false;

  const expiryDate = new Date(props.medication.expiryDate);
  const today = new Date();
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(today.getDate() + 30);

  return expiryDate > today && expiryDate <= thirtyDaysFromNow;
});

// Check if medication is expired
const isExpired = computed(() => {
  if (!props.medication) return false;

  const expiryDate = new Date(props.medication.expiryDate);
  const today = new Date();

  return expiryDate < today;
});



// Format date for display
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Format price for display
const formatPrice = (price: number) => {
  return new Intl.NumberFormat(undefined, {
    style: 'currency',
    currency: 'USD'
  }).format(price);
};

// Increase quantity
const increaseQuantity = () => {
  if (!props.medication) return;

  // Don't allow more than available stock
  if (quantity.value < props.medication.quantity) {
    quantity.value++;
  }
};

// Decrease quantity
const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--;
  }
};

// Add to cart - handles pack selection logic
const addToCart = () => {
  if (!props.medication) return;

  // Check if max packs reached and no current pack exists
  if (maxPacksReached.value && !hasActivePacks.value) {
    // Show error notification for max packs reached
    notificationMessage.value = t('cart.maxPacksReachedError');
    notificationType.value = 'error';
    showNotification.value = true;
    return;
  }

  // If we should show pack selection, open the modal
  if (shouldShowPackSelection.value && !isInCart.value) {
    showPackSelection.value = true;
    return;
  }

  // Otherwise, add to current pack (or create new pack if none exists)
  const currentPack = orderPackStore.getCurrentPack();
  const wasEmpty = !currentPack;

  orderPackStore.addToCurrentPack({
    id: props.medication.id,
    name: props.medication.name,
    barcode: props.medication.barcode,
    type: props.medication.type,
    quantity: quantity.value,
    pricePerUnit: finalPrice.value,
    discount: props.medication.discount
  });

  // Show success notification with specific pack info
  const updatedPack = orderPackStore.getCurrentPack();
  if (wasEmpty && updatedPack) {
    // New pack was created
    notificationMessage.value = t('cart.addedToNewPack');
  } else if (updatedPack) {
    // Added to existing current pack
    const packName = getPackDisplayName(updatedPack);
    notificationMessage.value = t('cart.addedToSpecificPack', { packName });
  } else {
    // Fallback
    notificationMessage.value = t('cart.addedToCart');
  }

  notificationType.value = 'success';
  showNotification.value = true;
};

// Update current pack
const updateCart = () => {
  if (!props.medication) return;

  orderPackStore.updateQuantityInCurrentPack(props.medication.id, quantity.value);

  // Show success notification
  notificationMessage.value = t('cart.updatedInCart', `${props.medication.name} quantity updated`);
  notificationType.value = 'success';
  showNotification.value = true;
};

// Remove from current pack
const removeFromCart = () => {
  if (!props.medication) return;

  orderPackStore.removeFromCurrentPack(props.medication.id);

  // Show info notification (not error since it's a user action)
  notificationMessage.value = t('cart.removedFromCart', `${props.medication.name} removed from cart`);
  notificationType.value = 'info';
  showNotification.value = true;
};

// Confirm delete
const confirmDelete = () => {
  showDeleteConfirm.value = true;
};

// Cancel delete
const cancelDelete = () => {
  showDeleteConfirm.value = false;
};

// Delete medication
const deleteMedication = () => {
  if (!props.medication) return;

  // Emit delete event
  emit('delete', props.medication.id);

  // Close delete confirmation
  showDeleteConfirm.value = false;

  // Close modal
  emit('close');
};

// Reset state when modal is closed
watch(() => props.show, (newValue) => {
  if (!newValue) {
    quantity.value = 1;
    showDeleteConfirm.value = false;
    showNotification.value = false;
  } else if (props.medication && isInCart.value) {
    // If item is in cart, set quantity to current cart quantity
    quantity.value = cartQuantity.value;
  } else {
    // Otherwise, reset to 1
    quantity.value = 1;
  }
});

// Handle notification close
const handleNotificationClose = () => {
  showNotification.value = false;
};

// Pack selection handlers
const handlePackSelection = (packId: string) => {
  if (!props.medication) return;

  // Find the pack to get its name
  const selectedPack = activePacks.value.find(pack => pack.id === packId);

  orderPackStore.addToSpecificPack(packId, {
    id: props.medication.id,
    name: props.medication.name,
    barcode: props.medication.barcode,
    type: props.medication.type,
    quantity: quantity.value,
    pricePerUnit: finalPrice.value,
    discount: props.medication.discount
  });

  // Show success notification with specific pack name
  if (selectedPack) {
    const packName = getPackDisplayName(selectedPack);
    notificationMessage.value = t('cart.addedToSpecificPack', { packName });
  } else {
    notificationMessage.value = t('cart.addedToCart');
  }
  notificationType.value = 'success';
  showNotification.value = true;
};

const handleCreateNewPack = () => {
  if (!props.medication) return;

  const packId = orderPackStore.addToNewPack({
    id: props.medication.id,
    name: props.medication.name,
    barcode: props.medication.barcode,
    type: props.medication.type,
    quantity: quantity.value,
    pricePerUnit: finalPrice.value,
    discount: props.medication.discount
  });

  if (packId) {
    // Get the newly created pack to show its name
    const newPack = activePacks.value.find(pack => pack.id === packId);
    if (newPack) {
      const packName = getPackDisplayName(newPack);
      notificationMessage.value = t('cart.addedToSpecificPack', { packName });
    } else {
      notificationMessage.value = t('cart.addedToNewPack');
    }
    notificationType.value = 'success';
    showNotification.value = true;
  }
};

const handleClosePackSelection = () => {
  showPackSelection.value = false;
};
</script>

<template>
  <Modal :show="show" :title="medication ? medication.name : ''" size="lg" @close="emit('close')">
    <!-- Delete Confirmation -->
    <div v-if="showDeleteConfirm" class="flex flex-col items-center gap-6 text-center">
      <div class="flex items-center justify-center w-16 h-16 bg-warning-100 dark:bg-warning-900/20 rounded-full">
        <ExclamationTriangleIcon class="w-8 h-8 text-warning-600 dark:text-warning-400" />
      </div>

      <div class="space-y-2">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ t('medication.confirmDelete') }}</h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 max-w-sm">{{ t('medication.deleteWarning') }}</p>
      </div>

      <div class="flex gap-3 w-full max-w-xs">
        <button @click="cancelDelete"
          class="flex-1 px-4 py-2.5 bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-slate-600 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
          {{ t('common.cancel') }}
        </button>
        <button @click="deleteMedication"
          class="flex-1 flex items-center justify-center gap-2 px-4 py-2.5 bg-error-600 text-white rounded-lg font-medium hover:bg-error-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2">
          <TrashIcon class="w-4 h-4" />
          {{ t('common.delete') }}
        </button>
      </div>
    </div>

    <!-- Medication Details -->
    <div v-else-if="medication" class="space-y-6 text-start">
      <!-- Alert Messages -->
      <div class="space-y-3">
        <!-- Max Packs Reached Alert -->
        <AlertMessage v-if="showMaxPacksError" :message="t('cart.maxPacksReachedAlert')" type="error"
          :showCloseButton="false" :autoClose="false" />

        <!-- Out of Stock Alert -->
        <AlertMessage v-if="medication.quantity === 0" :message="t('medication.outOfStock')" type="error"
          :showCloseButton="false" :autoClose="false" />

        <!-- Low Stock Alert -->
        <AlertMessage v-if="medication.quantity > 0 && medication.quantity < 20"
          :message="t('medication.lowStockWarning', { quantity: medication.quantity })" type="warning"
          :showCloseButton="false" :autoClose="false" />

        <!-- Expiring Soon Alert -->
        <AlertMessage v-if="isExpiringSoon"
          :message="t('medication.expiringSoonWarning', { date: formatDate(medication.expiryDate) })" type="warning"
          :showCloseButton="false" :autoClose="false" />

        <!-- Expired Alert -->
        <AlertMessage v-if="isExpired"
          :message="t('medication.expiredWarning', { date: formatDate(medication.expiryDate) })" type="error"
          :showCloseButton="false" :autoClose="false" />
      </div>

      <!-- Medication Information Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Left Column -->
        <div class="flex flex-col space-y-4">
          <!-- Basic Information Card -->
          <div
            class="bg-gray-50 dark:bg-slate-700/50 rounded-lg p-4 border border-gray-200 dark:border-slate-600 flex-1">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
              <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
              {{ t('medication.basicInfo') }}
            </h4>
            <div class="space-y-3">
              <div class="flex justify-between items-start">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                  t('medication.barcode') }}</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">{{ medication.barcode ||
                  t('common.notAvailable') }}</span>
              </div>
              <div class="flex justify-between items-start">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                  t('medication.type') }}</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">{{
                  t(`medication.types.${medication.type}`) }}</span>
              </div>
              <div class="flex justify-between items-start">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                  t('medication.category') }}</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">{{ medication.category
                  }}</span>
              </div>
            </div>
          </div>

          <!-- Stock & Expiry Card -->
          <div
            class="bg-gray-50 dark:bg-slate-700/50 rounded-lg p-4 border border-gray-200 dark:border-slate-600 flex-1">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
              <div class="w-2 h-2 bg-warning-500 rounded-full"></div>
              {{ t('medication.stockInfo') }}
            </h4>
            <div class="space-y-3">
              <div class="flex justify-between items-start">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                  t('medication.expiryDate') }}</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">{{
                  formatDate(medication.expiryDate) }}</span>
              </div>
              <div class="flex justify-between items-start">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                  t('medication.inStock') }}</span>
                <span class="text-sm font-medium text-right" :class="{
                  'text-success-600 dark:text-success-400': medication.quantity >= 20,
                  'text-warning-600 dark:text-warning-400': medication.quantity > 0 && medication.quantity < 20,
                  'text-error-600 dark:text-error-400': medication.quantity === 0
                }">
                  {{ medication.quantity }} {{ t('medication.units') }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="flex flex-col space-y-4">
          <!-- Pricing Card -->
          <div
            class="bg-gray-50 dark:bg-slate-700/50 rounded-lg p-4 border border-gray-200 dark:border-slate-600 flex-1">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
              <div class="w-2 h-2 bg-success-500 rounded-full"></div>
              {{ t('medication.pricingInfo') }}
            </h4>
            <div class="space-y-3">
              <div class="flex justify-between items-start">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                  t('medication.price') }}</span>
                <div class="text-right">
                  <span class="text-lg font-bold text-primary-600 dark:text-primary-400">{{ formatPrice(finalPrice)
                  }}</span>
                  <span v-if="medication.discount?.hasDiscount"
                    class="block text-sm text-gray-500 dark:text-gray-400 line-through">
                    {{ formatPrice(medication.pricePerUnit) }}
                  </span>
                </div>
              </div>
              <div class="flex justify-between items-start">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                  t('medication.purchasePrice') }}</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">{{
                  medication.purchasePrice ? formatPrice(medication.purchasePrice) : t('common.notAvailable') }}</span>
              </div>
            </div>
          </div>

          <!-- Supply Chain Card -->
          <div
            class="bg-gray-50 dark:bg-slate-700/50 rounded-lg p-4 border border-gray-200 dark:border-slate-600 flex-1">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
              <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
              {{ t('medication.supplyInfo') }}
            </h4>
            <div class="space-y-3">
              <div class="flex justify-between items-start">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                  t('medication.supplier') }}</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">{{ medication.supplier
                }}</span>
              </div>
              <div class="flex justify-between items-start">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">{{
                  t('medication.storageLocation') }}</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">{{
                  medication.storageLocation }}</span>
              </div>
            </div>
          </div>

          <!-- Discount Card (if applicable) -->
          <div v-if="medication.discount?.hasDiscount"
            class="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 rounded-lg p-4 border border-primary-200 dark:border-primary-700">
            <h4 class="text-sm font-semibold text-primary-900 dark:text-primary-100 mb-3 flex items-center gap-2">
              <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
              {{ t('medication.discount') }}
            </h4>
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <span class="text-xs font-medium text-primary-700 dark:text-primary-300 uppercase tracking-wide">{{
                  t('medication.discountRate') }}</span>
                <span class="text-xl font-bold text-primary-600 dark:text-primary-400">{{ medication.discount.percentage
                }}%</span>
              </div>
              <div v-if="medication.discount.validUntil" class="flex justify-between items-start">
                <span class="text-xs font-medium text-primary-700 dark:text-primary-300 uppercase tracking-wide">{{
                  t('medication.validUntil') }}</span>
                <span class="text-sm font-medium text-primary-900 dark:text-primary-100 text-right">{{
                  formatDate(medication.discount.validUntil) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Description Section -->
      <div v-if="medication.description"
        class="bg-gray-50 dark:bg-slate-700/50 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
        <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
          <div class="w-2 h-2 bg-indigo-500 rounded-full"></div>
          {{ t('medication.description') }}
        </h4>
        <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">{{ medication.description }}</p>
      </div>

      <!-- Cart Actions -->
      <div class="bg-white dark:bg-slate-800 rounded-lg p-6 border border-gray-200 dark:border-slate-700 shadow-sm">
        <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
          <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
          {{ t('cart.actions') }}
        </h4>

        <!-- Quantity Selector -->
        <div class="flex items-center gap-3 mb-6">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-0">{{ t('cart.quantity') }}:</span>
          <div
            class="flex items-center bg-gray-50 dark:bg-slate-700 rounded-lg border border-gray-200 dark:border-slate-600">
            <button @click="decreaseQuantity" :disabled="quantity <= 1"
              class="flex items-center justify-center w-10 h-10 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-slate-600 rounded-l-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Decrease quantity">
              <MinusIcon class="w-4 h-4" />
            </button>

            <input type="number" v-model.number="quantity" min="1" :max="medication.quantity"
              class="w-16 h-10 text-center bg-transparent border-0 text-gray-900 dark:text-gray-100 font-semibold focus:outline-none focus:ring-0"
              :aria-label="t('cart.quantity')" />

            <button @click="increaseQuantity" :disabled="quantity >= medication.quantity"
              class="flex items-center justify-center w-10 h-10 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-slate-600 rounded-r-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Increase quantity">
              <PlusIcon class="w-4 h-4" />
            </button>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">{{ t('medication.maxAvailable') }}: {{
            medication.quantity }}</span>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-3">
          <button v-if="!isInCart" @click="addToCart" :disabled="medication.quantity === 0"
            class="flex items-center gap-2 px-4 py-2.5 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed">
            <ShoppingCartIcon class="w-4 h-4" />
            {{ t('cart.addToCart') }}
          </button>

          <button v-else @click="updateCart"
            class="flex items-center gap-2 px-4 py-2.5 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            <ShoppingCartIcon class="w-4 h-4" />
            {{ t('cart.updateCart') }}
          </button>

          <button v-if="isInCart" @click="removeFromCart"
            class="flex items-center gap-2 px-4 py-2.5 bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-slate-600 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
            <TrashIcon class="w-4 h-4" />
            {{ t('cart.removeFromCart') }}
          </button>

          <button @click="confirmDelete"
            class="flex items-center gap-2 px-4 py-2.5 bg-error-100 dark:bg-error-900/20 text-error-700 dark:text-error-400 border border-error-200 dark:border-error-700 rounded-lg font-medium hover:bg-error-200 dark:hover:bg-error-900/30 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2">
            <TrashIcon class="w-4 h-4" />
            {{ t('medication.delete') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Cart Action Notification -->
    <Notification :show="showNotification" :message="notificationMessage" :type="notificationType" :duration="3000"
      @close="handleNotificationClose" />
  </Modal>

  <!-- Pack Selection Modal -->
  <PackSelectionModal :show="showPackSelection" :packs="activePacks" :current-pack-id="currentPackId"
    :can-add-more-packs="canAddMorePacks" :item-name="medication?.name" @close="handleClosePackSelection"
    @select-pack="handlePackSelection" @create-new-pack="handleCreateNewPack" />
</template>
