<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useTheme } from '../../theme/useTheme';
import Modal from '../Modal.vue';
import {
  PlusIcon,
  TrashIcon,
  ExclamationCircleIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline';
import ToggleSwitch from '../ui/ToggleSwitch.vue';
import type {
  NewMedicationFormData,
  MedicationFormErrors,
  MedicationType
} from '../../types/medicationForm';
import {
  defaultMedicationFormData,
  medicationTypeOptions,
  commonCategories
} from '../../types/medicationForm';

// Props
interface Props {
  show: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits(['close', 'save']);

// Setup i18n
const { t } = useI18n();

// Get theme
const { isDarkMode } = useTheme();

// Form state
const medications = ref<NewMedicationFormData[]>([{ ...defaultMedicationFormData }]);
const errors = ref<MedicationFormErrors[]>([{}]);
const isSubmitting = ref(false);
const showSuccessMessage = ref(false);

// Add another medication form
const addAnotherMedication = () => {
  medications.value.push({ ...defaultMedicationFormData });
  errors.value.push({});
};

// Remove a medication form
const removeMedication = (index: number) => {
  if (medications.value.length > 1) {
    medications.value.splice(index, 1);
    errors.value.splice(index, 1);
  }
};

// Toggle discount for a medication
const toggleDiscount = (index: number) => {
  const medication = medications.value[index];
  if (medication.discount) {
    medication.discount.hasDiscount = !medication.discount.hasDiscount;
  }
};

// Helper function to get discount model for a specific index
const getDiscountValue = (index: number) => {
  return medications.value[index]?.discount?.hasDiscount || false;
};

const setDiscountValue = (index: number, value: boolean) => {
  const medication = medications.value[index];
  if (medication.discount) {
    medication.discount.hasDiscount = value;
  }
};

// Handle category change
const handleCategoryChange = (index: number) => {
  const medication = medications.value[index];

  // If "Other" is selected, show custom category input
  if (medication.category === 'Other') {
    // Reset to empty to trigger the custom input
    medication.customCategory = '';
  } else {
    // Clear custom category when a predefined category is selected
    medication.customCategory = undefined;
  }
};

// Validate a single medication form
const validateMedication = (medication: NewMedicationFormData, index: number): boolean => {
  const formErrors: MedicationFormErrors = {};
  let isValid = true;

  // Validate required fields
  if (!medication.name.trim()) {
    formErrors.name = t('validation.required');
    isValid = false;
  }

  if (medication.category === 'Other') {
    if (!medication.customCategory || !medication.customCategory.trim()) {
      formErrors.category = t('validation.required');
      isValid = false;
    }
  } else if (!medication.category.trim()) {
    formErrors.category = t('validation.required');
    isValid = false;
  }

  if (!medication.type) {
    formErrors.type = t('validation.required');
    isValid = false;
  }

  if (medication.quantity <= 0) {
    formErrors.quantity = t('validation.positiveNumber');
    isValid = false;
  }

  if (!medication.expiryDate) {
    formErrors.expiryDate = t('validation.required');
    isValid = false;
  } else {
    const today = new Date();
    const expiryDate = new Date(medication.expiryDate);
    if (expiryDate <= today) {
      formErrors.expiryDate = t('validation.futureDateRequired');
      isValid = false;
    }
  }

  if (medication.price <= 0) {
    formErrors.price = t('validation.positiveNumber');
    isValid = false;
  }

  if (medication.purchasePrice !== undefined && medication.purchasePrice < 0) {
    formErrors.purchasePrice = t('validation.nonNegativeNumber');
    isValid = false;
  }

  // Validate discount if it's enabled
  if (medication.discount?.hasDiscount) {
    if (!medication.discount.percentage || medication.discount.percentage <= 0 || medication.discount.percentage > 100) {
      formErrors.discount = { ...formErrors.discount, percentage: t('validation.percentageRange') };
      isValid = false;
    }

    if (medication.discount.validUntil) {
      const today = new Date();
      const validUntil = new Date(medication.discount.validUntil);
      if (validUntil <= today) {
        formErrors.discount = { ...formErrors.discount, validUntil: t('validation.futureDateRequired') };
        isValid = false;
      }
    }
  }

  if (!medication.supplier.trim()) {
    formErrors.supplier = t('validation.required');
    isValid = false;
  }

  if (!medication.storageLocation.trim()) {
    formErrors.storageLocation = t('validation.required');
    isValid = false;
  }

  // Update errors for this medication
  errors.value[index] = formErrors;

  return isValid;
};

// Validate all medication forms
const validateAllMedications = (): boolean => {
  let isValid = true;

  medications.value.forEach((medication, index) => {
    if (!validateMedication(medication, index)) {
      isValid = false;
    }
  });

  return isValid;
};

// Handle form submission
const handleSubmit = async () => {
  if (isSubmitting.value) return;

  isSubmitting.value = true;

  // Validate all medications
  const isValid = validateAllMedications();

  if (isValid) {
    try {
      // Process medications before saving
      const processedMedications = medications.value.map(med => {
        // Create a copy of the medication to avoid modifying the original
        const processedMed = { ...med };

        // If category is "Other", use the customCategory value
        if (processedMed.category === 'Other' && processedMed.customCategory) {
          processedMed.category = processedMed.customCategory;
        }

        // Remove the customCategory property as it's not needed for saving
        delete processedMed.customCategory;

        return processedMed;
      });

      // In a real app, this would be an API call
      // For now, we'll simulate a network delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Show success message
      showSuccessMessage.value = true;

      // Emit save event with processed medications data
      emit('save', processedMedications);

      // Reset form after a delay
      setTimeout(() => {
        medications.value = [{ ...defaultMedicationFormData }];
        errors.value = [{}];
        showSuccessMessage.value = false;
        emit('close');
      }, 1500);
    } catch (error) {
      console.error('Error saving medications:', error);
    } finally {
      isSubmitting.value = false;
    }
  } else {
    isSubmitting.value = false;
  }
};

// Reset form when modal is closed
watch(() => props.show, (newValue) => {
  if (!newValue) {
    setTimeout(() => {
      medications.value = [{ ...defaultMedicationFormData }];
      errors.value = [{}];
      showSuccessMessage.value = false;
    }, 300);
  }
});
</script>

<template>
  <Modal :show="show" :title="t('medication.addNewMedication')" size="lg" @close="emit('close')">
    <!-- Success Message -->
    <div v-if="showSuccessMessage" class="flex flex-col items-center justify-center p-8 text-center">
      <CheckCircleIcon class="w-12 h-12 text-green-600 dark:text-green-400 mb-4" />
      <p class="text-lg font-medium text-gray-900 dark:text-white">{{ t('medication.addSuccess') }}</p>
    </div>

    <form v-else @submit.prevent="handleSubmit" class="flex flex-col gap-6 max-h-[60vh] overflow-y-auto p-2 text-start">
      <!-- Dynamic Medication Forms -->
      <div v-for="(medication, index) in medications" :key="index"
        class="border border-gray-200 dark:border-slate-600 rounded-xl p-5 bg-gray-50 dark:bg-slate-700/50 transition-all duration-300 hover:shadow-sm">
        <div class="flex justify-between items-center mb-4 pb-3 border-b border-gray-200 dark:border-slate-600">
          <h3 class="text-lg font-semibold text-primary-600 dark:text-primary-400">{{ t('medication.medicationDetails')
            }} #{{ index + 1 }}</h3>
          <button v-if="medications.length > 1" type="button"
            class="flex items-center justify-center w-8 h-8 bg-red-600 hover:bg-red-700 text-white border-none rounded-lg cursor-pointer transition-all duration-200 hover:-translate-y-0.5 shadow-sm hover:shadow-md"
            @click="removeMedication(index)" :aria-label="t('medication.removeMedication')">
            <TrashIcon class="w-5 h-5" />
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Medication Name -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`name-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.name') }} *</label>
            <input :id="`name-${index}`" v-model="medication.name" type="text"
              class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :class="errors[index]?.name ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'"
              :placeholder="t('medication.namePlaceholder')" />
            <p v-if="errors[index]?.name" class="text-xs text-red-600 dark:text-red-400 mt-1">{{ errors[index]?.name }}
            </p>
          </div>

          <!-- Barcode (Optional) -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`barcode-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.barcode') }}</label>
            <input :id="`barcode-${index}`" v-model="medication.barcode" type="text"
              class="px-3 py-2.5 border border-gray-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :placeholder="t('medication.barcodePlaceholder')" />
          </div>

          <!-- Category -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`category-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.category') }} *</label>
            <select :id="`category-${index}`" v-model="medication.category"
              class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :class="errors[index]?.category ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'"
              @change="handleCategoryChange(index)">
              <option value="" disabled selected>{{ t('medication.categoryPlaceholder') }}</option>
              <option v-for="category in commonCategories" :key="category" :value="category">
                {{ t(`medication.categories.${category}`) }}
              </option>
              <option value="Other">{{ t('medication.categories.Other') }}</option>
            </select>

            <!-- Custom Category Input (shown only when "Other" is selected) -->
            <input v-if="medication.category === 'Other'" :id="`customCategory-${index}`"
              v-model="medication.customCategory" type="text"
              class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 mt-2"
              :class="errors[index]?.category ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'"
              :placeholder="t('medication.customCategoryPlaceholder')" />

            <p v-if="errors[index]?.category" class="text-xs text-red-600 dark:text-red-400 mt-1">{{
              errors[index]?.category }}</p>
          </div>

          <!-- Type -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`type-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.type') }} *</label>
            <select :id="`type-${index}`" v-model="medication.type"
              class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :class="errors[index]?.type ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'">
              <option v-for="option in medicationTypeOptions" :key="option.value" :value="option.value">
                {{ t(`medication.types.${option.value}`) || option.label }}
              </option>
            </select>
            <p v-if="errors[index]?.type" class="text-xs text-red-600 dark:text-red-400 mt-1">{{ errors[index]?.type }}
            </p>
          </div>

          <!-- Quantity -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`quantity-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.quantity') }} *</label>
            <input :id="`quantity-${index}`" v-model.number="medication.quantity" type="number" min="0"
              class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :class="errors[index]?.quantity ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'" />
            <p v-if="errors[index]?.quantity" class="text-xs text-red-600 dark:text-red-400 mt-1">{{
              errors[index]?.quantity }}</p>
          </div>

          <!-- Expiry Date -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`expiryDate-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.expiryDate') }} *</label>
            <input :id="`expiryDate-${index}`" v-model="medication.expiryDate" type="date"
              class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :class="errors[index]?.expiryDate ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'" />
            <p v-if="errors[index]?.expiryDate" class="text-xs text-red-600 dark:text-red-400 mt-1">{{
              errors[index]?.expiryDate }}</p>
          </div>

          <!-- Price -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`price-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.price') }} *</label>
            <input :id="`price-${index}`" v-model.number="medication.price" type="number" min="0" step="0.01"
              class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :class="errors[index]?.price ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'" />
            <p v-if="errors[index]?.price" class="text-xs text-red-600 dark:text-red-400 mt-1">{{ errors[index]?.price
            }}</p>
          </div>

          <!-- Purchase Price (Optional) -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`purchasePrice-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.purchasePrice') }}</label>
            <input :id="`purchasePrice-${index}`" v-model.number="medication.purchasePrice" type="number" min="0"
              step="0.01"
              class="px-3 py-2.5 border border-gray-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :class="{ 'border-red-500 dark:border-red-400': errors[index]?.purchasePrice }" />
            <p v-if="errors[index]?.purchasePrice" class="text-xs text-red-600 dark:text-red-400 mt-1">{{
              errors[index]?.purchasePrice }}</p>
          </div>

          <!-- Supplier -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`supplier-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.supplier') }} *</label>
            <input :id="`supplier-${index}`" v-model="medication.supplier" type="text"
              class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :class="errors[index]?.supplier ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'" />
            <p v-if="errors[index]?.supplier" class="text-xs text-red-600 dark:text-red-400 mt-1">{{
              errors[index]?.supplier }}</p>
          </div>

          <!-- Storage Location -->
          <div class="flex flex-col gap-2 text-start">
            <label :for="`storageLocation-${index}`" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{
              t('medication.storageLocation') }} *</label>
            <input :id="`storageLocation-${index}`" v-model="medication.storageLocation" type="text"
              class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :class="errors[index]?.storageLocation ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'" />
            <p v-if="errors[index]?.storageLocation" class="text-xs text-red-600 dark:text-red-400 mt-1">{{
              errors[index]?.storageLocation }}</p>
          </div>

          <!-- Discount Toggle -->
          <div class="col-span-1 md:col-span-2 flex flex-col gap-2">
            <ToggleSwitch :model-value="getDiscountValue(index)"
              @update:model-value="(value) => setDiscountValue(index, value)" :label="t('medication.discount')"
              :description="medication.discount?.hasDiscount ? t('common.yes') : t('common.no')" size="sm"
              :show-icon="false" />
          </div>

          <!-- Discount Details (Conditional) -->
          <div v-if="medication.discount?.hasDiscount"
            class="col-span-1 md:col-span-2 p-4 bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-800 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex flex-col gap-2 text-start">
                <label :for="`discountPercentage-${index}`"
                  class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ t('medication.discountPercentage')
                  }}</label>
                <input :id="`discountPercentage-${index}`" v-model.number="medication.discount.percentage" type="number"
                  min="0" max="100"
                  class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  :class="errors[index]?.discount?.percentage ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'" />
                <p v-if="errors[index]?.discount?.percentage" class="text-xs text-red-600 dark:text-red-400 mt-1">{{
                  errors[index]?.discount?.percentage }}</p>
              </div>
              <div class="flex flex-col gap-2 text-start">
                <label :for="`discountValidUntil-${index}`"
                  class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ t('medication.validUntil') }}</label>
                <input :id="`discountValidUntil-${index}`" v-model="medication.discount.validUntil" type="date"
                  class="px-3 py-2.5 border rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  :class="errors[index]?.discount?.validUntil ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-slate-600'" />
                <p v-if="errors[index]?.discount?.validUntil" class="text-xs text-red-600 dark:text-red-400 mt-1">{{
                  errors[index]?.discount?.validUntil }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Another Medication Button -->
      <div class="flex justify-center mt-4">
        <button type="button"
          class="flex items-center gap-2 px-5 py-3 bg-gray-50 dark:bg-slate-700 border border-dashed border-gray-300 dark:border-slate-600 rounded-lg text-primary-600 dark:text-primary-400 text-sm font-medium cursor-pointer transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 hover:-translate-y-0.5 hover:shadow-sm"
          @click="addAnotherMedication">
          <PlusIcon class="w-5 h-5" />
          {{ t('medication.addAnother') }}
        </button>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end gap-3 mt-6">
        <button type="button"
          class="px-5 py-2.5 bg-gray-50 dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded-lg text-gray-900 dark:text-white text-sm font-medium cursor-pointer transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600"
          @click="emit('close')">{{ t('common.cancel') }}</button>
        <button type="submit"
          class="px-5 py-2.5 bg-primary-600 hover:bg-primary-700 disabled:opacity-70 disabled:cursor-not-allowed text-white border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 shadow-sm hover:shadow-md"
          :disabled="isSubmitting">
          <span v-if="isSubmitting">{{ t('common.saving') }}</span>
          <span v-else>{{ t('common.saveAll') }}</span>
        </button>
      </div>
    </form>

    <!-- Footer Slot -->
    <template #footer>
      <div class="flex justify-start w-full">
        <p class="text-xs text-gray-500 dark:text-gray-400">* {{ t('common.requiredFields') }}</p>
      </div>
    </template>
  </Modal>
</template>
