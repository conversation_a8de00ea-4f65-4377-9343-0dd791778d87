<script setup lang="ts">
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import Modal from './Modal.vue';
import { formatCurrency, formatDate } from '../utils/localeUtils';
import {
  PrinterIcon,
  TrashIcon,
  DocumentArrowDownIcon,
  EnvelopeIcon,
  DocumentDuplicateIcon,
  PencilIcon,
  CalendarDaysIcon,
  CreditCardIcon,
  BanknotesIcon,
  UserIcon,
  BuildingStorefrontIcon
} from '@heroicons/vue/24/outline';

interface InvoiceItem {
  id: string;
  medicationName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

interface Invoice {
  id: string;
  date: string;
  orderId: string;
  totalAmount: number;
  paymentMethod: string;
  status: 'paid' | 'unpaid' | 'canceled';
  pharmacyName: string;
  createdBy: string;
  currency: string;
  subtotal: number;
  discount: number;
  tax: number;
  items: InvoiceItem[];
  notes?: string;
  pdfUrl?: string;
}

interface Props {
  show: boolean;
  invoice: Invoice | null;
}

const props = withDefaults(defineProps<Props>(), {
  invoice: null
});

const emit = defineEmits([
  'close',
  'delete-invoice',
  'print-invoice',
  'download-pdf',
  'send-email',
  'duplicate-invoice',
  'change-status'
]);

const { t } = useI18n();

// State for status editing
const isEditingStatus = ref(false);
const editingStatus = ref<'paid' | 'unpaid' | 'canceled'>('paid');

// Note: Using imported locale-aware formatting functions

// Get payment method icon
const getPaymentMethodIcon = (method: string) => {
  const icons = {
    credit_card: CreditCardIcon,
    cash: BanknotesIcon,
    bank_transfer: BanknotesIcon,
    online: CreditCardIcon
  };
  return icons[method] || BanknotesIcon;
};

// Get status color classes
const getStatusClasses = (status: string) => {
  const classes = {
    paid: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    unpaid: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    canceled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  };
  return classes[status] || classes.unpaid;
};

// Format payment method display using translations
const formatPaymentMethod = (method: string) => {
  return t(`invoices.paymentMethods.${method}`, method.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
};

// Event handlers
const handleDelete = () => {
  if (confirm(t('invoices.confirmDelete'))) {
    emit('delete-invoice', props.invoice);
  }
};

const handlePrint = () => {
  emit('print-invoice', props.invoice);
};

const handleDownloadPDF = () => {
  emit('download-pdf', props.invoice);
};

const handleSendEmail = () => {
  emit('send-email', props.invoice);
};

const handleDuplicate = () => {
  emit('duplicate-invoice', props.invoice);
};

const startEditingStatus = () => {
  if (props.invoice) {
    editingStatus.value = props.invoice.status;
    isEditingStatus.value = true;
  }
};

const saveStatus = () => {
  emit('change-status', { invoice: props.invoice, newStatus: editingStatus.value });
  isEditingStatus.value = false;
};

const cancelEditingStatus = () => {
  isEditingStatus.value = false;
  if (props.invoice) {
    editingStatus.value = props.invoice.status;
  }
};
</script>

<template>
  <Modal :show="show" :title="t('invoices.invoiceDetails')" size="xl" @close="emit('close')">
    <div v-if="invoice" class="invoice-details">
      <!-- Invoice Header -->
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 rounded-xl p-6 mb-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ invoice.id }}</h2>
            <div class="flex items-center gap-2 text-gray-600 dark:text-gray-300">
              <CalendarDaysIcon class="w-4 h-4" />
              <span>{{ formatDate(invoice.date) }}</span>
            </div>
          </div>
          <div class="text-right">
            <div class="flex items-center gap-2 mb-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ t('invoices.status.label') }}:</span>
              <div v-if="!isEditingStatus" class="flex items-center gap-2">
                <span class="px-3 py-1 rounded-full text-xs font-medium" :class="getStatusClasses(invoice.status)">
                  {{ t(`invoices.status.${invoice.status}`) }}
                </span>
                <button @click="startEditingStatus" class="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded">
                  <PencilIcon class="w-4 h-4 text-gray-500" />
                </button>
              </div>
              <div v-else class="flex items-center gap-2">
                <select v-model="editingStatus" class="px-2 py-1 text-xs border rounded">
                  <option value="paid">{{ t('invoices.status.paid') }}</option>
                  <option value="unpaid">{{ t('invoices.status.unpaid') }}</option>
                  <option value="canceled">{{ t('invoices.status.canceled') }}</option>
                </select>
                <button @click="saveStatus"
                  class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                  {{ t('common.save') }}
                </button>
                <button @click="cancelEditingStatus"
                  class="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600">
                  {{ t('common.cancel') }}
                </button>
              </div>
            </div>
            <div class="text-3xl font-bold text-primary-600 dark:text-primary-400">
              {{ formatCurrency(invoice.totalAmount, invoice.currency) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Invoice Details Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Pharmacy Info -->
        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <div class="flex items-center gap-3 mb-3">
            <BuildingStorefrontIcon class="w-5 h-5 text-primary-600" />
            <h3 class="font-semibold text-gray-900 dark:text-white">{{ t('invoices.pharmacy') }}</h3>
          </div>
          <p class="text-gray-700 dark:text-gray-300">{{ invoice.pharmacyName }}</p>
        </div>

        <!-- Order Info -->
        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <h3 class="font-semibold text-gray-900 dark:text-white mb-3">{{ t('invoices.orderInfo') }}</h3>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">{{ t('invoices.orderId') }}:</span>
              <span class="font-medium">{{ invoice.orderId }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">{{ t('invoices.createdBy') }}:</span>
              <span class="font-medium">{{ invoice.createdBy }}</span>
            </div>
          </div>
        </div>

        <!-- Payment Info -->
        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <h3 class="font-semibold text-gray-900 dark:text-white mb-3">{{ t('invoices.paymentInfo') }}</h3>
          <div class="flex items-center gap-2">
            <component :is="getPaymentMethodIcon(invoice.paymentMethod)" class="w-5 h-5 text-primary-600" />
            <span class="font-medium">{{ formatPaymentMethod(invoice.paymentMethod) }}</span>
          </div>
        </div>

        <!-- Currency Info -->
        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <h3 class="font-semibold text-gray-900 dark:text-white mb-3">{{ t('invoices.currency') }}</h3>
          <p class="text-lg font-semibold text-primary-600">{{ invoice.currency }}</p>
        </div>
      </div>

      <!-- Items List -->
      <div class="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-600 mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-slate-600">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ t('invoices.itemsList') }}</h3>
        </div>
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50 dark:bg-slate-700">
              <tr>
                <th
                  class="text-start px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ t('invoices.medication') }}
                </th>
                <th
                  class="text-start px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ t('invoices.quantity') }}
                </th>
                <th
                  class="text-start px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ t('invoices.unitPrice') }}
                </th>
                <th
                  class="text-start px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ t('invoices.total') }}
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-slate-600">
              <tr v-for="item in invoice.items" :key="item.id" class="hover:bg-gray-50 dark:hover:bg-slate-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ item.medicationName }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ item.quantity }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ formatCurrency(item.unitPrice, invoice.currency)
                    }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ formatCurrency(item.totalPrice,
                    invoice.currency) }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Financial Summary -->
      <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-slate-700 dark:to-slate-600 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ t('invoices.financialSummary') }}</h3>
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-300">{{ t('invoices.subtotal') }}:</span>
            <span class="font-medium text-gray-900 dark:text-white">{{ formatCurrency(invoice.subtotal,
              invoice.currency) }}</span>
          </div>
          <div v-if="invoice.discount > 0" class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-300">{{ t('invoices.discount') }}:</span>
            <span class="font-medium text-red-600 dark:text-red-400">-{{ formatCurrency(invoice.discount,
              invoice.currency) }}</span>
          </div>
          <div v-if="invoice.tax > 0" class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-300">{{ t('invoices.tax') }}:</span>
            <span class="font-medium text-gray-900 dark:text-white">{{ formatCurrency(invoice.tax, invoice.currency)
              }}</span>
          </div>
          <div class="border-t border-gray-300 dark:border-slate-500 pt-3">
            <div class="flex justify-between items-center">
              <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ t('invoices.grandTotal') }}:</span>
              <span class="text-xl font-bold text-primary-600 dark:text-primary-400">{{
                formatCurrency(invoice.totalAmount, invoice.currency) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes Section -->
      <div v-if="invoice.notes" class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 mb-6">
        <h3 class="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2">{{ t('invoices.notes') }}</h3>
        <p class="text-sm text-yellow-700 dark:text-yellow-300">{{ invoice.notes }}</p>
      </div>
    </div>

    <!-- Sticky Footer with Actions -->
    <template #footer>
      <div class="flex flex-wrap gap-3 justify-between">
        <div class="flex gap-2">
          <button @click="handlePrint"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800">
            <PrinterIcon class="w-4 h-4" />
            {{ t('invoices.print') }}
          </button>
          <button @click="handleDownloadPDF"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800">
            <DocumentArrowDownIcon class="w-4 h-4" />
            {{ t('invoices.downloadPdf') }}
          </button>
          <button @click="handleSendEmail"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800">
            <EnvelopeIcon class="w-4 h-4" />
            {{ t('invoices.sendEmail') }}
          </button>
          <button @click="handleDuplicate"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800">
            <DocumentDuplicateIcon class="w-4 h-4" />
            {{ t('invoices.duplicate') }}
          </button>
        </div>
        <div class="flex gap-2">
          <button @click="handleDelete"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-red-600">
            <TrashIcon class="w-4 h-4" />
            {{ t('invoices.delete') }}
          </button>
          <button @click="emit('close')"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary-600">
            {{ t('common.close') }}
          </button>
        </div>
      </div>
    </template>
  </Modal>
</template>
