<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { WifiIcon, GlobeAltIcon, ExclamationTriangleIcon, XCircleIcon, InformationCircleIcon } from '@heroicons/vue/24/outline';
import { getNetworkStatus } from '../utils/networkUtils';
import onlineModeStore from '../store/onlineModeStore';
import StatusPopup from './StatusPopup.vue';

// Setup i18n
const { t } = useI18n();

// Date and time state
const currentDate = ref('');
const currentTime = ref('');

// Hover states
const isVersionHovered = ref(false);
const isNetworkStatusHovered = ref(false);
const isOnlineModeHovered = ref(false);

// Version data
const releaseNotes = {
  version: '1.2.0'
};



// Network status - using reactive refs to ensure updates
const { isOnline } = getNetworkStatus();
const onlineModeEnabled = computed(() => onlineModeStore.state.onlineModeEnabled.value);

// Reactive trigger to force updates
const updateTrigger = ref(0);
const forceUpdate = () => {
  updateTrigger.value++;
};

// Computed properties for network status display
// The effective status considers both network connectivity and online mode setting
const effectiveOnlineStatus = computed(() => {
  updateTrigger.value; // Force reactivity
  // If online mode is disabled, the system is effectively offline regardless of network
  return onlineModeEnabled.value && isOnline.value;
});

const networkStatusText = computed(() => {
  updateTrigger.value; // Force reactivity
  // Network status shows actual internet connectivity regardless of online mode
  return isOnline.value ? t('footer.online') : t('footer.offline');
});

// Detailed status for tooltip or description
const systemStatusDescription = computed(() => {
  updateTrigger.value; // Force reactivity
  if (!onlineModeEnabled.value) {
    return t('footer.statusDescriptions.localMode');
  }
  if (!isOnline.value) {
    return t('footer.statusDescriptions.noInternet');
  }
  return t('footer.statusDescriptions.onlineActive');
});

const networkStatusColor = computed(() => {
  updateTrigger.value; // Force reactivity
  // Network status color shows actual internet connectivity
  return isOnline.value ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
});

const networkStatusIcon = computed(() => {
  updateTrigger.value; // Force reactivity
  // Network status icon shows actual internet connectivity
  return isOnline.value ? WifiIcon : ExclamationTriangleIcon;
});

const onlineModeText = computed(() => {
  updateTrigger.value; // Force reactivity
  return onlineModeEnabled.value ? t('footer.enabled') : t('footer.disabled');
});

const onlineModeColor = computed(() => {
  updateTrigger.value; // Force reactivity
  return onlineModeEnabled.value ? 'text-teal-600 dark:text-teal-400' : 'text-red-600 dark:text-red-400';
});

const onlineModeIcon = computed(() => {
  updateTrigger.value; // Force reactivity
  return onlineModeEnabled.value ? GlobeAltIcon : XCircleIcon;
});

// Current locale for release notes
const currentLocale = computed(() => {
  return document.documentElement.lang || 'en';
});

// Current release notes based on locale
const currentReleaseNotes = computed(() => {
  const locale = currentLocale.value === 'ar' ? 'ar' : 'en';
  if (locale === 'ar') {
    return {
      features: [
        'تحسين إدارة المخزون',
        'تحسين وظائف البحث',
        'تحليلات لوحة التحكم الجديدة'
      ],
      bugFixes: [
        'إصلاح التنقل في القائمة المحمولة',
        'حل مشاكل موضع التذييل',
        'تحسين دعم التخطيط من اليمين إلى اليسار'
      ]
    };
  } else {
    return {
      features: [
        'Enhanced inventory management',
        'Improved search functionality',
        'New dashboard analytics'
      ],
      bugFixes: [
        'Fixed mobile menu navigation',
        'Resolved footer positioning issues',
        'Improved RTL layout support'
      ]
    };
  }
});

// Current network status info based on locale and connection
const currentNetworkStatusInfo = computed(() => {
  const status = isOnline.value ? 'online' : 'offline';
  const locale = currentLocale.value === 'ar' ? 'ar' : 'en';

  if (locale === 'ar') {
    return status === 'online' ? {
      status: 'متصل',
      description: 'جهازك متصل بالإنترنت',
      details: ['مزامنة البيانات في الوقت الفعلي', 'النسخ الاحتياطي السحابي مفعل', 'الميزات المتصلة متاحة']
    } : {
      status: 'غير متصل',
      description: 'لم يتم اكتشاف اتصال بالإنترنت',
      details: ['العمل في الوضع غير المتصل', 'ستتم مزامنة البيانات عند الاتصال', 'ميزات محدودة متاحة']
    };
  } else {
    return status === 'online' ? {
      status: 'Connected',
      description: 'Your device is connected to the internet',
      details: ['Real-time data synchronization', 'Cloud backup enabled', 'Online features available']
    } : {
      status: 'Disconnected',
      description: 'No internet connection detected',
      details: ['Working in offline mode', 'Data will sync when reconnected', 'Limited features available']
    };
  }
});

// Current online mode info based on locale and mode
const currentOnlineModeInfo = computed(() => {
  const status = onlineModeEnabled.value ? 'enabled' : 'disabled';
  const locale = currentLocale.value === 'ar' ? 'ar' : 'en';

  if (locale === 'ar') {
    return status === 'enabled' ? {
      status: 'مفعل',
      description: 'الميزات المتصلة والخدمات السحابية نشطة',
      details: ['ميزات الذكاء الاصطناعي متاحة', 'المزامنة السحابية نشطة', 'التحديثات الفورية مفعلة']
    } : {
      status: 'معطل',
      description: 'العمل في الوضع المحلي فقط',
      details: ['تخزين البيانات محلياً فقط', 'ميزات الذكاء الاصطناعي غير متاحة', 'المزامنة اليدوية مطلوبة']
    };
  } else {
    return status === 'enabled' ? {
      status: 'Enabled',
      description: 'Online features and cloud services are active',
      details: ['AI-powered features available', 'Cloud synchronization active', 'Real-time updates enabled']
    } : {
      status: 'Disabled',
      description: 'Working in local mode only',
      details: ['Local data storage only', 'AI features unavailable', 'Manual sync required']
    };
  }
});

// Format date based on locale
const formatDate = () => {
  const now = new Date();
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  };

  // Get the current locale from the document
  const locale = document.documentElement.lang || 'en';
  currentDate.value = now.toLocaleDateString(locale, options);
};

// Format time based on locale
const formatTime = () => {
  const now = new Date();
  const options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true
  };

  // Get the current locale from the document
  const locale = document.documentElement.lang || 'en';
  currentTime.value = now.toLocaleTimeString(locale, options);
};

// Update time every second
let timeInterval: number | null = null;

// Event listeners for network and online mode changes
let networkCleanup: (() => void) | null = null;
let onlineModeCleanup: (() => void) | null = null;

onMounted(() => {
  // Initial update
  formatDate();
  formatTime();

  // Set interval to update time every second
  timeInterval = window.setInterval(() => {
    formatTime();

    // Update date at midnight
    const now = new Date();
    if (now.getHours() === 0 && now.getMinutes() === 0 && now.getSeconds() === 0) {
      formatDate();
    }
  }, 1000);

  // Initialize online mode store
  onlineModeStore.initOnlineMode();

  // Listen for network status changes
  const handleNetworkChange = () => {
    // Force reactivity update by triggering a re-render
    forceUpdate();
    console.log('Network status changed in footer:', isOnline.value);
  };

  // Listen for online mode changes
  const handleOnlineModeChange = () => {
    // Force reactivity update by triggering a re-render
    forceUpdate();
    console.log('Online mode changed in footer:', onlineModeEnabled.value);
  };

  // Add event listeners
  window.addEventListener('networkStatusChanged', handleNetworkChange);
  window.addEventListener('onlineModeChanged', handleOnlineModeChange);

  // Store cleanup functions
  networkCleanup = () => {
    window.removeEventListener('networkStatusChanged', handleNetworkChange);
  };

  onlineModeCleanup = () => {
    window.removeEventListener('onlineModeChanged', handleOnlineModeChange);
  };
});

onUnmounted(() => {
  // Clear interval when component is unmounted
  if (timeInterval !== null) {
    clearInterval(timeInterval);
  }

  // Clean up event listeners
  if (networkCleanup) {
    networkCleanup();
  }
  if (onlineModeCleanup) {
    onlineModeCleanup();
  }
});
</script>

<template>
  <footer
    class="bg-white dark:bg-neutral-850 border-t border-gray-200 dark:border-neutral-700 shadow-light dark:shadow-dark-light z-[5] fixed bottom-0 h-16 w-full left-0 right-0">
    <!-- Mobile Layout (< 640px) -->
    <div class="sm:hidden flex flex-col h-full px-3 py-1">
      <!-- Top Row: Date and Time -->
      <div class="flex items-center justify-center gap-2 flex-1">
        <div class="bg-gray-50 dark:bg-neutral-800 px-2 py-1 rounded border border-gray-200 dark:border-neutral-700">
          <div class="flex items-center gap-1">
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase">{{ t('footer.date')
            }}</span>
            <span class="text-xs font-medium text-gray-800 dark:text-neutral-100">{{ currentDate.split(',')[0] }}</span>
          </div>
        </div>
        <div class="bg-gray-50 dark:bg-neutral-800 px-2 py-1 rounded border border-gray-200 dark:border-neutral-700">
          <div class="flex items-center gap-1">
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase">{{ t('footer.time')
            }}</span>
            <span class="text-xs font-medium text-gray-800 dark:text-neutral-100 font-mono">{{ currentTime.split(' ')[0]
              }}</span>
          </div>
        </div>
      </div>

      <!-- Bottom Row: Status and Version -->
      <div class="flex items-center justify-between gap-2 flex-1">
        <div class="flex items-center gap-2">
          <div
            class="relative flex items-center gap-1 bg-gray-50 dark:bg-neutral-800 px-2 py-1 rounded border border-gray-200 dark:border-neutral-700 cursor-help"
            @mouseenter="isNetworkStatusHovered = true" @mouseleave="isNetworkStatusHovered = false">
            <!-- Network Status Popup -->
            <StatusPopup :is-visible="isNetworkStatusHovered" :icon="networkStatusIcon" :icon-color="networkStatusColor"
              :title="currentLocale === 'ar' ? 'اتصال الشبكة' : 'Network Connection'"
              :status="currentNetworkStatusInfo.status" :status-color="networkStatusColor"
              :description="currentNetworkStatusInfo.description" :details="currentNetworkStatusInfo.details"
              @mouseenter="isNetworkStatusHovered = true" @mouseleave="isNetworkStatusHovered = false" />
            <component :is="networkStatusIcon" class="w-3 h-3" :class="networkStatusColor" />
            <span class="text-xs font-semibold" :class="networkStatusColor">{{ networkStatusText }}</span>
          </div>
          <div
            class="relative flex items-center gap-1 bg-gray-50 dark:bg-neutral-800 px-2 py-1 rounded border border-gray-200 dark:border-neutral-700 cursor-help"
            @mouseenter="isOnlineModeHovered = true" @mouseleave="isOnlineModeHovered = false">
            <!-- Online Mode Popup -->
            <StatusPopup :is-visible="isOnlineModeHovered" :icon="onlineModeIcon" :icon-color="onlineModeColor"
              :title="currentLocale === 'ar' ? 'الوضع المتصل' : 'Online Mode'" :status="currentOnlineModeInfo.status"
              :status-color="onlineModeColor" :description="currentOnlineModeInfo.description"
              :details="currentOnlineModeInfo.details" @mouseenter="isOnlineModeHovered = true"
              @mouseleave="isOnlineModeHovered = false" />
            <component :is="onlineModeIcon" class="w-3 h-3" :class="onlineModeColor" />
            <span class="text-xs font-semibold" :class="onlineModeColor">{{ onlineModeText }}</span>
          </div>
        </div>
        <div
          class="relative bg-gray-50 dark:bg-neutral-800 px-2 py-1 rounded border border-gray-200 dark:border-neutral-700 cursor-help"
          @mouseenter="isVersionHovered = true" @mouseleave="isVersionHovered = false">
          <!-- Version Release Notes Popup -->
          <StatusPopup :is-visible="isVersionHovered" :icon="InformationCircleIcon"
            icon-color="text-primary-600 dark:text-primary-400"
            :title="`${currentLocale === 'ar' ? 'ملاحظات الإصدار' : 'Release Notes'} v${releaseNotes.version}`"
            :status="releaseNotes.version" status-color="text-primary-600 dark:text-primary-400"
            :description="currentLocale === 'ar' ? 'آخر التحديثات والتحسينات' : 'Latest updates and improvements'"
            :features="currentReleaseNotes.features" :bug-fixes="currentReleaseNotes.bugFixes" :show-read-more="true"
            position="left" @mouseenter="isVersionHovered = true" @mouseleave="isVersionHovered = false"
            @read-more="() => { /* TODO: Navigate to release page */ }" />
          <div class="flex items-center gap-1">
            <InformationCircleIcon class="w-3 h-3 text-primary-600 dark:text-primary-400" />
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400">{{ releaseNotes.version }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Tablet Layout (640px - 768px) -->
    <div class="hidden sm:flex md:hidden items-center justify-between h-full px-4">
      <!-- Left side - Date and Time -->
      <div class="flex items-center gap-3">
        <div class="bg-gray-50 dark:bg-neutral-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-neutral-700">
          <div class="flex items-center gap-2">
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase">{{ t('footer.date')
              }}</span>
            <span class="text-sm font-semibold text-gray-800 dark:text-neutral-100">{{ currentDate }}</span>
          </div>
        </div>
        <div class="bg-gray-50 dark:bg-neutral-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-neutral-700">
          <div class="flex items-center gap-2">
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase">{{ t('footer.time')
              }}</span>
            <span class="text-sm font-semibold text-gray-800 dark:text-neutral-100 font-mono">{{ currentTime }}</span>
          </div>
        </div>
      </div>

      <!-- Right side - Status Info and Version -->
      <div class="flex items-center gap-3">
        <div
          class="relative flex items-center gap-2 bg-gray-50 dark:bg-neutral-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-neutral-700 cursor-help"
          @mouseenter="isNetworkStatusHovered = true" @mouseleave="isNetworkStatusHovered = false">
          <!-- Network Status Popup -->
          <StatusPopup :is-visible="isNetworkStatusHovered" :icon="networkStatusIcon" :icon-color="networkStatusColor"
            :title="currentLocale === 'ar' ? 'اتصال الشبكة' : 'Network Connection'"
            :status="currentNetworkStatusInfo.status" :status-color="networkStatusColor"
            :description="currentNetworkStatusInfo.description" :details="currentNetworkStatusInfo.details"
            @mouseenter="isNetworkStatusHovered = true" @mouseleave="isNetworkStatusHovered = false" />
          <component :is="networkStatusIcon" class="w-4 h-4" :class="networkStatusColor" />
          <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase">{{ t('footer.networkStatus')
            }}</span>
          <span class="text-xs font-semibold" :class="networkStatusColor">{{ networkStatusText }}</span>
        </div>
        <div
          class="relative flex items-center gap-2 bg-gray-50 dark:bg-neutral-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-neutral-700 cursor-help"
          @mouseenter="isOnlineModeHovered = true" @mouseleave="isOnlineModeHovered = false">
          <!-- Online Mode Popup -->
          <StatusPopup :is-visible="isOnlineModeHovered" :icon="onlineModeIcon" :icon-color="onlineModeColor"
            :title="currentLocale === 'ar' ? 'الوضع المتصل' : 'Online Mode'" :status="currentOnlineModeInfo.status"
            :status-color="onlineModeColor" :description="currentOnlineModeInfo.description"
            :details="currentOnlineModeInfo.details" @mouseenter="isOnlineModeHovered = true"
            @mouseleave="isOnlineModeHovered = false" />
          <component :is="onlineModeIcon" class="w-4 h-4" :class="onlineModeColor" />
          <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase">{{ t('footer.onlineMode')
            }}</span>
          <span class="text-xs font-semibold" :class="onlineModeColor">{{ onlineModeText }}</span>
        </div>
        <div
          class="relative bg-gray-50 dark:bg-neutral-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-neutral-700 cursor-help"
          @mouseenter="isVersionHovered = true" @mouseleave="isVersionHovered = false">
          <!-- Version Release Notes Popup -->
          <StatusPopup :is-visible="isVersionHovered" :icon="InformationCircleIcon"
            icon-color="text-primary-600 dark:text-primary-400"
            :title="`${currentLocale === 'ar' ? 'ملاحظات الإصدار' : 'Release Notes'} v${releaseNotes.version}`"
            :status="releaseNotes.version" status-color="text-primary-600 dark:text-primary-400"
            :description="currentLocale === 'ar' ? 'آخر التحديثات والتحسينات' : 'Latest updates and improvements'"
            :features="currentReleaseNotes.features" :bug-fixes="currentReleaseNotes.bugFixes" :show-read-more="true"
            position="left" @mouseenter="isVersionHovered = true" @mouseleave="isVersionHovered = false"
            @read-more="() => { /* TODO: Navigate to release page */ }" />
          <div class="flex items-center gap-2">
            <InformationCircleIcon class="w-4 h-4 text-primary-600 dark:text-primary-400" />
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400">{{ releaseNotes.version }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Desktop Layout (≥ 768px) -->
    <div class="hidden md:flex items-center justify-between h-full px-6">
      <!-- Left side - Date and Time -->
      <div class="flex items-center gap-4">
        <div
          class="flex items-center gap-3 bg-gray-50 dark:bg-neutral-800 px-4 py-2 rounded-lg border border-gray-200 dark:border-neutral-700">
          <div class="flex items-center gap-2">
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase tracking-wide">{{
              t('footer.date') }}</span>
            <span class="text-sm font-semibold text-gray-800 dark:text-neutral-100">{{ currentDate }}</span>
          </div>
        </div>
        <div
          class="flex items-center gap-3 bg-gray-50 dark:bg-neutral-800 px-4 py-2 rounded-lg border border-gray-200 dark:border-neutral-700">
          <div class="flex items-center gap-2">
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase tracking-wide">{{
              t('footer.time') }}</span>
            <span class="text-sm font-semibold text-gray-800 dark:text-neutral-100 font-mono">{{ currentTime }}</span>
          </div>
        </div>
      </div>

      <!-- Right side - Network & System Info -->
      <div class="flex items-center gap-3">
        <!-- Network Status -->
        <div
          class="relative flex items-center gap-3 bg-gray-50 dark:bg-neutral-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-neutral-700 cursor-help"
          @mouseenter="isNetworkStatusHovered = true" @mouseleave="isNetworkStatusHovered = false">
          <!-- Network Status Popup -->
          <StatusPopup :is-visible="isNetworkStatusHovered" :icon="networkStatusIcon" :icon-color="networkStatusColor"
            :title="currentLocale === 'ar' ? 'اتصال الشبكة' : 'Network Connection'"
            :status="currentNetworkStatusInfo.status" :status-color="networkStatusColor"
            :description="currentNetworkStatusInfo.description" :details="currentNetworkStatusInfo.details"
            @mouseenter="isNetworkStatusHovered = true" @mouseleave="isNetworkStatusHovered = false" />
          <div class="flex items-center gap-2">
            <component :is="networkStatusIcon" class="w-4 h-4" :class="networkStatusColor" />
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase tracking-wide">{{
              t('footer.networkStatus') }}</span>
            <span class="text-xs font-semibold" :class="networkStatusColor">{{ networkStatusText }}</span>
          </div>
        </div>

        <!-- Online Mode Status -->
        <div
          class="relative flex items-center gap-3 bg-gray-50 dark:bg-neutral-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-neutral-700 cursor-help"
          @mouseenter="isOnlineModeHovered = true" @mouseleave="isOnlineModeHovered = false">
          <!-- Online Mode Popup -->
          <StatusPopup :is-visible="isOnlineModeHovered" :icon="onlineModeIcon" :icon-color="onlineModeColor"
            :title="currentLocale === 'ar' ? 'الوضع المتصل' : 'Online Mode'" :status="currentOnlineModeInfo.status"
            :status-color="onlineModeColor" :description="currentOnlineModeInfo.description"
            :details="currentOnlineModeInfo.details" @mouseenter="isOnlineModeHovered = true"
            @mouseleave="isOnlineModeHovered = false" />
          <div class="flex items-center gap-2">
            <component :is="onlineModeIcon" class="w-4 h-4" :class="onlineModeColor" />
            <span class="text-xs font-medium text-gray-500 dark:text-neutral-400 uppercase tracking-wide">{{
              t('footer.onlineMode') }}</span>
            <span class="text-xs font-semibold" :class="onlineModeColor">{{ onlineModeText }}</span>
          </div>
        </div>

        <!-- Version -->
        <div
          class="relative flex items-center gap-2 bg-gray-50 dark:bg-neutral-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-neutral-700 cursor-help"
          @mouseenter="isVersionHovered = true" @mouseleave="isVersionHovered = false">
          <!-- Version Release Notes Popup -->
          <StatusPopup :is-visible="isVersionHovered" :icon="InformationCircleIcon"
            icon-color="text-primary-600 dark:text-primary-400"
            :title="`${currentLocale === 'ar' ? 'ملاحظات الإصدار' : 'Release Notes'} v${releaseNotes.version}`"
            :status="releaseNotes.version" status-color="text-primary-600 dark:text-primary-400"
            :description="currentLocale === 'ar' ? 'آخر التحديثات والتحسينات' : 'Latest updates and improvements'"
            :features="currentReleaseNotes.features" :bug-fixes="currentReleaseNotes.bugFixes" :show-read-more="true"
            position="left" @mouseenter="isVersionHovered = true" @mouseleave="isVersionHovered = false"
            @read-more="() => { /* TODO: Navigate to release page */ }" />
          <InformationCircleIcon class="w-4 h-4 text-primary-600 dark:text-primary-400" />
          <span class="text-xs font-medium text-gray-500 dark:text-neutral-400">{{ releaseNotes.version }}</span>
        </div>
      </div>
    </div>
  </footer>
</template>
