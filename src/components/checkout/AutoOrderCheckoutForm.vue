<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  UserIcon,
  PhoneIcon,
  MapPinIcon,
  StarIcon,
  ClockIcon,
  TruckIcon,
  BuildingStorefrontIcon
} from '@heroicons/vue/24/outline';
import type { OrderPack } from '../../types/cart';

interface Props {
  orderPack: OrderPack;
  customerInfo: any;
  deliveryOption: string;
  paymentMethod: string;
  readonly?: boolean;
}

interface Emits {
  (e: 'update:deliveryOption', value: string): void;
  (e: 'update:paymentMethod', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
});

const emit = defineEmits<Emits>();
const { t } = useI18n();

// Computed properties for order pack info
const userInfo = computed(() => props.orderPack.userInfo);
const isDeliveryOrder = computed(() => userInfo.value?.serviceType === 'Delivery');
const isReservationOrder = computed(() => userInfo.value?.serviceType === 'Reserved');

// Format expiry time for reservations
const expiryTime = computed(() => {
  if (!props.orderPack.expiryTime) return '';
  const expiry = new Date(props.orderPack.expiryTime);
  const now = new Date();
  const hoursLeft = Math.max(0, (expiry.getTime() - now.getTime()) / (1000 * 60 * 60));
  return Math.floor(hoursLeft * 10) / 10; // Round to 1 decimal
});

// Handle delivery option change
const handleDeliveryOptionChange = (value: string) => {
  if (!props.readonly) {
    emit('update:deliveryOption', value);
  }
};

// Handle payment method change
const handlePaymentMethodChange = (value: string) => {
  if (!props.readonly) {
    emit('update:paymentMethod', value);
  }
};
</script>

<template>
  <div class="space-y-6">
    <!-- Order Pack Info Header -->
    <div class="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4">
      <div class="flex items-center gap-3 mb-3">
        <div class="flex items-center justify-center w-8 h-8 bg-primary-600 text-white rounded-full">
          <UserIcon class="w-4 h-4" />
        </div>
        <div>
          <h3 class="font-semibold text-primary-900 dark:text-primary-200">
            {{ t('checkout.autoAcceptedOrder') }}
          </h3>
          <p class="text-sm text-primary-700 dark:text-primary-300">
            {{ t('checkout.orderFromApp') }}
          </p>
        </div>
      </div>

      <!-- Customer Details -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-3">
          <div class="flex items-center gap-2 text-sm">
            <UserIcon class="w-4 h-4 text-primary-600 dark:text-primary-400" />
            <span class="font-medium text-gray-900 dark:text-gray-100">{{ userInfo?.userName }}</span>
          </div>
          
          <div class="flex items-center gap-2 text-sm">
            <PhoneIcon class="w-4 h-4 text-primary-600 dark:text-primary-400" />
            <span class="text-gray-700 dark:text-gray-300">{{ userInfo?.userPhone }}</span>
          </div>
          
          <div v-if="userInfo?.location?.address" class="flex items-center gap-2 text-sm">
            <MapPinIcon class="w-4 h-4 text-primary-600 dark:text-primary-400" />
            <span class="text-gray-700 dark:text-gray-300">{{ userInfo.location.address }}</span>
          </div>
        </div>

        <div class="space-y-3">
          <div v-if="userInfo?.pointsToEarn" class="flex items-center gap-2 text-sm">
            <StarIcon class="w-4 h-4 text-warning-500" />
            <span class="text-gray-700 dark:text-gray-300">
              {{ t('checkout.pointsToEarn', { points: userInfo.pointsToEarn }) }}
            </span>
          </div>
          
          <div v-if="isReservationOrder && expiryTime > 0" class="flex items-center gap-2 text-sm">
            <ClockIcon class="w-4 h-4 text-warning-500" />
            <span class="text-warning-700 dark:text-warning-300">
              {{ t('checkout.expiresInHours', { hours: expiryTime }) }}
            </span>
          </div>
          
          <div class="flex items-center gap-2 text-sm">
            <div class="w-4 h-4 flex items-center justify-center">
              <TruckIcon v-if="isDeliveryOrder" class="w-4 h-4 text-success-500" />
              <BuildingStorefrontIcon v-else class="w-4 h-4 text-info-500" />
            </div>
            <span class="text-gray-700 dark:text-gray-300">
              {{ isDeliveryOrder ? t('checkout.deliveryOrder') : t('checkout.reservationOrder') }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Pre-filled Customer Information (Read-only) -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        {{ t('checkout.customerInformation') }}
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.firstName') }}
          </label>
          <input
            type="text"
            :value="customerInfo?.firstName || ''"
            readonly
            class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-gray-100 cursor-not-allowed"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.lastName') }}
          </label>
          <input
            type="text"
            :value="customerInfo?.lastName || ''"
            readonly
            class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-gray-100 cursor-not-allowed"
          />
        </div>
        
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.phone') }}
          </label>
          <input
            type="tel"
            :value="customerInfo?.phone || ''"
            readonly
            class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-gray-100 cursor-not-allowed"
          />
        </div>

        <div v-if="customerInfo?.address" class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.address') }}
          </label>
          <input
            type="text"
            :value="customerInfo?.address || ''"
            readonly
            class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-gray-100 cursor-not-allowed"
          />
        </div>

        <div v-if="customerInfo?.city" class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.city') }}
          </label>
          <input
            type="text"
            :value="customerInfo?.city || ''"
            readonly
            class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-gray-100 cursor-not-allowed"
          />
        </div>
      </div>
    </div>

    <!-- Delivery Options -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        {{ t('checkout.deliveryOptions') }}
      </h3>
      
      <div class="space-y-3">
        <label class="flex items-center p-3 border border-gray-200 dark:border-slate-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200"
               :class="{ 'border-primary-500 bg-primary-50 dark:bg-primary-900/20': deliveryOption === 'pickup' }">
          <input
            type="radio"
            name="deliveryOption"
            value="pickup"
            :checked="deliveryOption === 'pickup'"
            @change="handleDeliveryOptionChange('pickup')"
            :disabled="readonly"
            class="w-4 h-4 text-primary-600 border-gray-300 dark:border-slate-600 focus:ring-primary-500"
          />
          <div class="ml-3 rtl:ml-0 rtl:mr-3">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ t('checkout.pickup') }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ t('checkout.pickupDescription') }}
            </div>
          </div>
        </label>
        
        <label class="flex items-center p-3 border border-gray-200 dark:border-slate-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200"
               :class="{ 'border-primary-500 bg-primary-50 dark:bg-primary-900/20': deliveryOption === 'delivery' }">
          <input
            type="radio"
            name="deliveryOption"
            value="delivery"
            :checked="deliveryOption === 'delivery'"
            @change="handleDeliveryOptionChange('delivery')"
            :disabled="readonly"
            class="w-4 h-4 text-primary-600 border-gray-300 dark:border-slate-600 focus:ring-primary-500"
          />
          <div class="ml-3 rtl:ml-0 rtl:mr-3">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ t('checkout.delivery') }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ t('checkout.deliveryDescription') }}
            </div>
          </div>
        </label>
      </div>
    </div>

    <!-- Payment Method -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        {{ t('checkout.paymentMethod') }}
      </h3>
      
      <div class="space-y-3">
        <label class="flex items-center p-3 border border-gray-200 dark:border-slate-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200"
               :class="{ 'border-primary-500 bg-primary-50 dark:bg-primary-900/20': paymentMethod === 'cash' }">
          <input
            type="radio"
            name="paymentMethod"
            value="cash"
            :checked="paymentMethod === 'cash'"
            @change="handlePaymentMethodChange('cash')"
            :disabled="readonly"
            class="w-4 h-4 text-primary-600 border-gray-300 dark:border-slate-600 focus:ring-primary-500"
          />
          <div class="ml-3 rtl:ml-0 rtl:mr-3">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ t('checkout.paymentMethods.cash') }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ t('checkout.cashDescription') }}
            </div>
          </div>
        </label>
        
        <label class="flex items-center p-3 border border-gray-200 dark:border-slate-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200"
               :class="{ 'border-primary-500 bg-primary-50 dark:bg-primary-900/20': paymentMethod === 'card' }">
          <input
            type="radio"
            name="paymentMethod"
            value="card"
            :checked="paymentMethod === 'card'"
            @change="handlePaymentMethodChange('card')"
            :disabled="readonly"
            class="w-4 h-4 text-primary-600 border-gray-300 dark:border-slate-600 focus:ring-primary-500"
          />
          <div class="ml-3 rtl:ml-0 rtl:mr-3">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ t('checkout.paymentMethods.card') }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ t('checkout.cardDescription') }}
            </div>
          </div>
        </label>
      </div>
    </div>
  </div>
</template>
