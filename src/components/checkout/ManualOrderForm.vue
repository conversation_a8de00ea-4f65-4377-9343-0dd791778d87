<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  ShoppingBagIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';
import type { OrderPack } from '../../types/cart';

interface Props {
  orderPack: OrderPack;
  customerInfo: any;
  validationErrors: any;
}

interface Emits {
  (e: 'update:customerInfo', value: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const { t } = useI18n();

// Computed properties
const localOrderInfo = computed(() => props.orderPack.localOrderInfo);

// Handle customer info updates
const updateCustomerInfo = (field: string, value: string) => {
  emit('update:customerInfo', {
    ...(props.customerInfo || {}),
    [field]: value
  });
};
</script>

<template>
  <div class="space-y-6">
    <!-- Local Order Info Header -->
    <div class="bg-info-50 dark:bg-info-900/20 border border-info-200 dark:border-info-800 rounded-lg p-4">
      <div class="flex items-center gap-3">
        <div class="flex items-center justify-center w-8 h-8 bg-info-600 text-white rounded-full">
          <ShoppingBagIcon class="w-4 h-4" />
        </div>
        <div>
          <h3 class="font-semibold text-info-900 dark:text-info-200">
            {{ t('checkout.localOrder') }}
          </h3>
          <p class="text-sm text-info-700 dark:text-info-300">
            {{ t('checkout.localOrderNumber', { number: localOrderInfo?.orderNumber }) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Customer Information Form -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        {{ t('checkout.customerInformation') }}
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- First Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.firstName') }} <span class="text-error-500">*</span>
          </label>
          <input
            type="text"
            :value="customerInfo?.firstName || ''"
            @input="updateCustomerInfo('firstName', ($event.target as HTMLInputElement).value)"
            :class="[
              'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200',
              validationErrors?.firstName?.isValid === false
                ? 'border-error-300 dark:border-error-700 bg-error-50 dark:bg-error-900/20'
                : 'border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-700'
            ]"
            :placeholder="t('checkout.firstNamePlaceholder')"
          />
          <div v-if="validationErrors?.firstName?.isValid === false" class="mt-1 flex items-center gap-1 text-sm text-error-600 dark:text-error-400">
            <ExclamationTriangleIcon class="w-4 h-4" />
            {{ validationErrors?.firstName?.message }}
          </div>
        </div>

        <!-- Last Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.lastName') }} <span class="text-error-500">*</span>
          </label>
          <input
            type="text"
            :value="customerInfo?.lastName || ''"
            @input="updateCustomerInfo('lastName', ($event.target as HTMLInputElement).value)"
            :class="[
              'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200',
              validationErrors?.lastName?.isValid === false
                ? 'border-error-300 dark:border-error-700 bg-error-50 dark:bg-error-900/20'
                : 'border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-700'
            ]"
            :placeholder="t('checkout.lastNamePlaceholder')"
          />
          <div v-if="validationErrors?.lastName?.isValid === false" class="mt-1 flex items-center gap-1 text-sm text-error-600 dark:text-error-400">
            <ExclamationTriangleIcon class="w-4 h-4" />
            {{ validationErrors?.lastName?.message }}
          </div>
        </div>

        <!-- Phone -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.phone') }} <span class="text-error-500">*</span>
          </label>
          <input
            type="tel"
            :value="customerInfo?.phone || ''"
            @input="updateCustomerInfo('phone', ($event.target as HTMLInputElement).value)"
            :class="[
              'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200',
              validationErrors?.phone?.isValid === false
                ? 'border-error-300 dark:border-error-700 bg-error-50 dark:bg-error-900/20'
                : 'border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-700'
            ]"
            :placeholder="t('checkout.phonePlaceholder')"
          />
          <div v-if="validationErrors?.phone?.isValid === false" class="mt-1 flex items-center gap-1 text-sm text-error-600 dark:text-error-400">
            <ExclamationTriangleIcon class="w-4 h-4" />
            {{ validationErrors?.phone?.message }}
          </div>
        </div>

        <!-- Email -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.email') }}
          </label>
          <input
            type="email"
            :value="customerInfo?.email || ''"
            @input="updateCustomerInfo('email', ($event.target as HTMLInputElement).value)"
            :class="[
              'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200',
              validationErrors?.email?.isValid === false
                ? 'border-error-300 dark:border-error-700 bg-error-50 dark:bg-error-900/20'
                : 'border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-700'
            ]"
            :placeholder="t('checkout.emailPlaceholder')"
          />
          <div v-if="validationErrors?.email?.isValid === false" class="mt-1 flex items-center gap-1 text-sm text-error-600 dark:text-error-400">
            <ExclamationTriangleIcon class="w-4 h-4" />
            {{ validationErrors?.email?.message }}
          </div>
        </div>

        <!-- Address -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.address') }}
          </label>
          <input
            type="text"
            :value="customerInfo?.address || ''"
            @input="updateCustomerInfo('address', ($event.target as HTMLInputElement).value)"
            class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
            :placeholder="t('checkout.addressPlaceholder')"
          />
        </div>

        <!-- City -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.city') }}
          </label>
          <input
            type="text"
            :value="customerInfo?.city || ''"
            @input="updateCustomerInfo('city', ($event.target as HTMLInputElement).value)"
            class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
            :placeholder="t('checkout.cityPlaceholder')"
          />
        </div>



        <!-- Notes -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ t('checkout.notes') }}
          </label>
          <textarea
            :value="customerInfo?.notes || ''"
            @input="updateCustomerInfo('notes', ($event.target as HTMLTextAreaElement).value)"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
            :placeholder="t('checkout.notesPlaceholder')"
          ></textarea>
        </div>
      </div>
    </div>


  </div>
</template>
