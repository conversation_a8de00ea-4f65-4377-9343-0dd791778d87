<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import QRCode from 'qrcode';
import { useTheme } from '../theme/useTheme';

const props = defineProps<{
  isDarkMode?: boolean;
  size?: number;
}>();

const emit = defineEmits<{
  roomCodeGenerated: [code: string];
}>();

const { t } = useI18n();
const { isDarkMode } = useTheme();

const qrCodeDataUrl = ref('');
const roomCode = ref('');
const isLoading = ref(true);
const expiryTime = ref<Date | null>(null);

// Generate a random room code
const generateRoomCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Generate device connection data
const generateConnectionData = () => {
  const code = generateRoomCode();
  roomCode.value = code;

  // Set expiry time to 15 minutes from now
  expiryTime.value = new Date(Date.now() + 15 * 60 * 1000);

  const connectionData = {
    roomCode: code,
    deviceId: 'desktop_' + Math.random().toString(36).substring(2, 10),
    timestamp: new Date().toISOString(),
    expiresAt: expiryTime.value.toISOString(),
    type: 'device_connection'
  };

  emit('roomCodeGenerated', code);
  return JSON.stringify(connectionData);
};

// QR code colors based on theme
const qrColors = computed(() => {
  const darkMode = props.isDarkMode ?? isDarkMode.value;
  return {
    dark: darkMode ? '#FFFFFF' : '#1F2937', // White in dark mode, dark gray in light mode
    light: darkMode ? '#1F2937' : '#FFFFFF' // Dark gray in dark mode, white in light mode
  };
});

// Generate QR code
const generateQRCode = async () => {
  try {
    isLoading.value = true;

    const qrData = generateConnectionData();

    qrCodeDataUrl.value = await QRCode.toDataURL(qrData, {
      width: props.size || 280,
      margin: 2,
      color: qrColors.value,
      errorCorrectionLevel: 'M'
    });
  } catch (error) {
    console.error('Error generating QR code:', error);
  } finally {
    isLoading.value = false;
  }
};

// Regenerate QR code when theme changes
watch(() => qrColors.value, generateQRCode, { deep: true });

// Initial generation
onMounted(generateQRCode);

// Expose refresh function
const refreshCode = () => {
  generateQRCode();
};

defineExpose({
  refreshCode,
  roomCode: computed(() => roomCode.value),
  expiryTime: computed(() => expiryTime.value)
});
</script>

<template>
  <div class="flex flex-col items-center justify-center">
    <!-- QR Code Container -->
    <div
      class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-slate-700 transition-colors duration-200">
      <div v-if="isLoading" class="flex items-center justify-center w-[280px] h-[280px]">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 dark:border-primary-400"></div>
      </div>
      <img v-else :src="qrCodeDataUrl" alt="QR Code" class="w-[280px] h-[280px] rounded-lg" />
    </div>
  </div>
</template>
