<script setup lang="ts">
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import Modal from './Modal.vue';
import OrderStatusBadge from './orders/OrderStatusBadge.vue';
import LiveTrackingMap from './LiveTrackingMap.vue';
import { formatCurrency, formatDate, formatTime } from '../utils/localeUtils';
import {
  CalendarDaysIcon,
  PrinterIcon,
  XCircleIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  EnvelopeIcon,
  CreditCardIcon,
  TruckIcon,
  ClockIcon,
  UserIcon,
  BuildingStorefrontIcon
} from '@heroicons/vue/24/outline';
import type { Order, DeliveryOrder } from '../types/orders';

interface Props {
  show: boolean;
  order: Order | DeliveryOrder | null;
  type?: 'order' | 'delivery';
}

const props = withDefaults(defineProps<Props>(), {
  order: null,
  type: 'order'
});

const emit = defineEmits([
  'close',
  'print-order',
  'cancel-order',
  'reorder',
  'download-pdf',
  'send-email'
]);

const { t } = useI18n();

// Note: Using imported locale-aware formatting functions from utils/localeUtils

// Calculate total items count for regular orders
const totalItemsCount = computed(() => {
  if (props.type === 'delivery') {
    return (props.order as DeliveryOrder)?.itemsCount || 0;
  }
  if (!(props.order as Order)?.items) return 0;
  return (props.order as Order).items!.reduce((total, item) => total + item.quantity, 0);
});

// Calculate subtotal if items are available
const subtotal = computed(() => {
  if (props.type === 'delivery') return 0;
  if (!(props.order as Order)?.items) return (props.order as Order)?.totalAmount || 0;
  return (props.order as Order).items!.reduce((total, item) => total + item.totalPrice, 0);
});

// Get modal title based on type
const modalTitle = computed(() => {
  return props.type === 'delivery' ? t('orders.deliveryOrderDetails') : t('orders.orderDetails');
});

// Mock data for the map (in a real app, this would come from the order data)
const pharmacyLocation = ref<[number, number]>([30.0444, 31.2357]); // Cairo coordinates
const customerLocation = ref<[number, number]>([30.0571, 31.2272]); // Nearby location
const driverLocation = ref<[number, number]>([30.0500, 31.2300]); // Between pharmacy and customer

// Computed property to determine if we should show the tracking map
const showTrackingMap = computed(() => {
  return props.type === 'delivery' && (props.order as DeliveryOrder)?.status === 'out_for_delivery';
});

// Check if order can be cancelled
const canCancelOrder = computed(() => {
  if (!props.order) return false;
  const cancelableStatuses = ['pending', 'processed'];
  return cancelableStatuses.includes(props.order.status);
});

// Action handlers
const handlePrint = () => {
  emit('print-order', props.order);
};

const handleCancel = () => {
  if (confirm(t('orders.confirmCancel'))) {
    emit('cancel-order', props.order);
  }
};

const handleReorder = () => {
  emit('reorder', props.order);
};

const handleDownloadPDF = () => {
  emit('download-pdf', props.order);
};

const handleSendEmail = () => {
  emit('send-email', props.order);
};

// Get payment method icon
const getPaymentMethodIcon = (method: string) => {
  const icons: Record<string, any> = {
    credit_card: CreditCardIcon,
    cash: CreditCardIcon,
    bank_transfer: CreditCardIcon,
    wallet: CreditCardIcon
  };
  return icons[method] || CreditCardIcon;
};

// Format payment method display using translations
const formatPaymentMethod = (method: string) => {
  return t(`orders.paymentMethods.${method}`, method.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
};
</script>

<template>
  <Modal :show="show" :title="modalTitle" size="lg" @close="emit('close')">
    <div v-if="order" class="flex flex-col gap-6 text-start rtl:text-right">
      <!-- Order Header -->
      <div
        class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-2xl p-6 shadow-sm relative overflow-hidden">
        <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-600 to-primary-400"></div>
        <div class="flex flex-col gap-4">
          <div class="flex justify-between items-center flex-wrap gap-4 ">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white m-0 tracking-wide">
              {{ type === 'delivery' ? t('orders.orderId') : t('orders.orderId') }}:
              {{ type === 'delivery' ? (order as DeliveryOrder).orderId : order.id }}
            </h3>
            <OrderStatusBadge :status="order.status" :type="type === 'delivery' ? 'delivery' : 'order'" />
          </div>
          <div class="flex items-center gap-2 ">
            <CalendarDaysIcon class="w-4 h-4 text-gray-500 dark:text-gray-400" />
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ type === 'delivery' ? formatDate((order as DeliveryOrder).deliveryTime) : formatDate((order as
                Order).date) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Order Details Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Customer Info -->
        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <div class="flex items-center gap-3 mb-3 ">
            <UserIcon class="w-5 h-5 text-primary-600" />
            <h3 class="font-semibold text-gray-900 dark:text-white">{{ t('orders.customer') }}</h3>
          </div>
          <p class="text-gray-700 dark:text-gray-300">{{ order.customerName }}</p>
        </div>

        <!-- Date Placed -->
        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <div class="flex items-center gap-3 mb-3 ">
            <CalendarDaysIcon class="w-5 h-5 text-primary-600" />
            <h3 class="font-semibold text-gray-900 dark:text-white">{{ t('orders.datePlaced') }}</h3>
          </div>
          <p class="text-gray-700 dark:text-gray-300">{{ formatDate(order.date) }}</p>
        </div>

        <!-- Pharmacy Info -->
        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <div class="flex items-center gap-3 mb-3 ">
            <BuildingStorefrontIcon class="w-5 h-5 text-primary-600" />
            <h3 class="font-semibold text-gray-900 dark:text-white">{{ t('orders.pharmacyName') }}</h3>
          </div>
          <p class="text-gray-700 dark:text-gray-300">{{ order.pharmacyName || t('orders.defaultPharmacyName') }}</p>
        </div>

        <!-- Payment Status -->
        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <div class="flex items-center gap-3 mb-3 ">
            <component :is="getPaymentMethodIcon(order.paymentMethod || 'cash')" class="w-5 h-5 text-primary-600" />
            <h3 class="font-semibold text-gray-900 dark:text-white">{{ t('orders.paymentStatus') }}</h3>
          </div>
          <div class="flex items-center gap-2 ">
            <span class="px-3 py-1 rounded-full text-xs font-medium"
              :class="order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'">
              {{ t(`orders.paymentStatuses.${order.paymentStatus || 'pending'}`) }}
            </span>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              ({{ formatPaymentMethod(order.paymentMethod || 'cash') }})
            </span>
          </div>
        </div>

        <!-- Shipping Method (for delivery orders) -->
        <div v-if="type === 'delivery'"
          class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <div class="flex items-center gap-3 mb-3 ">
            <TruckIcon class="w-5 h-5 text-primary-600" />
            <h3 class="font-semibold text-gray-900 dark:text-white">{{ t('orders.shippingMethod') }}</h3>
          </div>
          <p class="text-gray-700 dark:text-gray-300">{{ t(`orders.shippingMethods.${(order as
            DeliveryOrder).shippingMethod || 'standard'}`) }}</p>
        </div>

        <!-- Estimated Delivery (for delivery orders) -->
        <div v-if="type === 'delivery'"
          class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-gray-200 dark:border-slate-600">
          <div class="flex items-center gap-3 mb-3 ">
            <ClockIcon class="w-5 h-5 text-primary-600" />
            <h3 class="font-semibold text-gray-900 dark:text-white">{{ t('orders.estimatedDelivery') }}</h3>
          </div>
          <p class="text-gray-700 dark:text-gray-300">{{ formatDate((order as DeliveryOrder).deliveryTime) }}</p>
        </div>
      </div>

      <!-- Total Amount / Items Count -->
      <div
        class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-2xl p-5 shadow-sm relative overflow-hidden group hover:-translate-y-0.5 hover:shadow-md transition-all duration-300">
        <div
          class="absolute top-0 left-0 w-1 h-full bg-primary-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        </div>
        <div class="flex-1 w-full">
          <h4 class="text-xs font-bold text-gray-600 dark:text-gray-400 mb-2 uppercase tracking-wider">{{ type ===
            'order' ? t('orders.totalAmount') : t('orders.itemsCount') }}</h4>
          <p v-if="type === 'order'" class="text-xl font-bold text-primary-600 dark:text-primary-400 m-0 leading-tight">
            {{ formatCurrency((order as Order).totalAmount) }}</p>
          <p v-else class="text-lg font-semibold text-gray-900 dark:text-white m-0 leading-tight">{{ totalItemsCount }}
            {{ t('orders.items') }}</p>
        </div>
      </div>

      <!-- Delivery Address (for delivery orders) -->
      <div v-if="type === 'delivery'"
        class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-2xl p-5 shadow-sm relative overflow-hidden group hover:-translate-y-0.5 hover:shadow-md transition-all duration-300">
        <div
          class="absolute top-0 left-0 w-1 h-full bg-primary-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        </div>
        <div class="flex-1 w-full">
          <h4 class="text-xs font-bold text-gray-600 dark:text-gray-400 mb-2 uppercase tracking-wider">{{
            t('orders.address') }}</h4>
          <p class="text-lg font-semibold text-gray-900 dark:text-white m-0 leading-tight">{{ (order as
            DeliveryOrder).address }}</p>
        </div>
      </div>

      <!-- Phone Number (for delivery orders) -->
      <div v-if="type === 'delivery' && (order as DeliveryOrder).contactNumber"
        class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-2xl p-5 shadow-sm relative overflow-hidden group hover:-translate-y-0.5 hover:shadow-md transition-all duration-300">
        <div
          class="absolute top-0 left-0 w-1 h-full bg-primary-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        </div>
        <div class="flex-1 w-full">
          <h4 class="text-xs font-bold text-gray-600 dark:text-gray-400 mb-2 uppercase tracking-wider">{{
            t('orders.phoneNumber') }}</h4>
          <p class="text-lg font-semibold text-gray-900 dark:text-white m-0 leading-tight">{{ (order as
            DeliveryOrder).contactNumber }}</p>
        </div>
      </div>

      <!-- Delivery Time (for delivery orders) -->
      <div v-if="type === 'delivery'"
        class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-2xl p-5 shadow-sm relative overflow-hidden group hover:-translate-y-0.5 hover:shadow-md transition-all duration-300">
        <div
          class="absolute top-0 left-0 w-1 h-full bg-primary-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        </div>
        <div class="flex-1 w-full">
          <h4 class="text-xs font-bold text-gray-600 dark:text-gray-400 mb-2 uppercase tracking-wider">{{
            t('orders.deliveryTime') }}</h4>
          <p class="text-lg font-semibold text-gray-900 dark:text-white m-0 leading-tight">{{ formatTime((order as
            DeliveryOrder).deliveryTime) }}</p>
        </div>
      </div>

      <!-- Delivery Partner (for delivery orders) -->
      <div v-if="type === 'delivery'"
        class="bg-gradient-to-br mb-6 from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-2xl p-5 shadow-sm relative overflow-hidden group hover:-translate-y-0.5 hover:shadow-md transition-all duration-300">
        <div
          class="absolute top-0 left-0 w-1 h-full bg-primary-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        </div>
        <div class="flex-1 w-full">
          <h4 class="text-xs font-bold text-gray-600 dark:text-gray-400 mb-2 uppercase tracking-wider">{{
            t('orders.deliveryPartner') }}</h4>
          <p class="text-lg font-semibold text-gray-900 dark:text-white m-0 leading-tight">{{
            t(`orders.partners.${(order as DeliveryOrder).partner}`) }}</p>
        </div>
      </div>

      <!-- Order Items (for regular orders) -->
      <div v-if="type === 'order' && (order as Order).items && (order as Order).items!.length > 0"
        class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-2xl p-5 shadow-sm relative overflow-hidden group hover:-translate-y-0.5 hover:shadow-md transition-all duration-300 col-span-full">
        <div
          class="absolute top-0 left-0 w-1 h-full bg-primary-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        </div>
        <div class="flex-1 w-full">
          <h4 class="text-xs font-bold text-gray-600 dark:text-gray-400 mb-2 uppercase tracking-wider">{{
            t('orders.orderedItems') }}</h4>
          <div class="flex flex-col gap-3">
            <div v-for="item in (order as Order).items" :key="item.id"
              class="flex justify-between items-center p-4 bg-gradient-to-r from-white to-gray-50 dark:from-slate-700 dark:to-slate-600 border border-gray-200 dark:border-slate-500 rounded-xl transition-all duration-300 relative overflow-hidden group/item hover:translate-x-0.5 hover:shadow-sm ">
              <div
                class="absolute top-0 left-0 rtl:left-auto rtl:right-0 w-0.5 h-full bg-primary-600 opacity-0 group-hover/item:opacity-100 transition-opacity duration-300">
              </div>
              <div class="flex flex-col gap-1 flex-1">
                <span class="font-semibold text-gray-900 dark:text-white text-base">{{ item.medicationName }}</span>
                <span class="text-sm text-gray-600 dark:text-gray-400 font-medium">x{{ item.quantity }}</span>
              </div>
              <div class="flex flex-col items-end rtl:items-start gap-1">
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ formatCurrency(item.unitPrice) }} {{
                  t('orders.each') }}</span>
                <span class="font-semibold text-primary-600 dark:text-primary-400 text-base">{{
                  formatCurrency(item.totalPrice) }}</span>
              </div>
            </div>

            <!-- Order Summary -->
            <div
              class="mt-6 p-5 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-slate-700 dark:to-slate-600 rounded-xl border border-gray-200 dark:border-slate-500 shadow-sm relative">
              <div
                class="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-600 to-primary-400 rounded-t-xl">
              </div>
              <div class="flex justify-between items-center py-2">
                <span class="text-gray-600 dark:text-gray-400 font-medium">{{ t('orders.subtotal') }}</span>
                <span class="text-gray-900 dark:text-white font-semibold">{{ formatCurrency(subtotal) }}</span>
              </div>
              <div
                class="flex justify-between items-center pt-4 mt-2 border-t border-gray-300 dark:border-slate-500 font-semibold text-lg">
                <span class="text-primary-600 dark:text-primary-400 font-bold">{{ t('orders.total') }}</span>
                <span class="text-primary-600 dark:text-primary-400 font-bold">{{ formatCurrency((order as
                  Order).totalAmount) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes (if available) -->
      <div v-if="type === 'delivery' && (order as DeliveryOrder).notes"
        class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-2xl p-5 shadow-sm relative overflow-hidden group hover:-translate-y-0.5 hover:shadow-md transition-all duration-300 col-span-full">
        <div
          class="absolute top-0 left-0 w-1 h-full bg-primary-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        </div>
        <div class="flex-1 w-full">
          <h4 class="text-xs font-bold text-gray-600 dark:text-gray-400 mb-2 uppercase tracking-wider">{{
            t('orders.notes') }}</h4>
          <p class="text-lg font-semibold text-gray-900 dark:text-white m-0 leading-tight">{{ (order as
            DeliveryOrder).notes }}</p>
        </div>
      </div>
    </div>

    <!-- Live Tracking Map Section (for delivery orders that are out for delivery) -->
    <div v-if="showTrackingMap"
      class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-2xl overflow-hidden shadow-md relative">
      <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-600 to-primary-400"></div>
      <div
        class="flex justify-between items-center p-5 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-slate-700 dark:to-slate-600 border-b border-gray-200 dark:border-slate-500 relative z-10">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white m-0 tracking-wide">{{ t('orders.liveTracking') }}
        </h3>
        <div class="flex items-center gap-2 text-sm font-medium text-primary-600 dark:text-primary-400">
          <div class="w-2 h-2 bg-primary-600 dark:bg-primary-400 rounded-full animate-pulse"></div>
          <span>{{ t('orders.trackingActive') }}</span>
        </div>
      </div>
      <div class="h-96 w-full">
        <LiveTrackingMap :pharmacy-location="pharmacyLocation" :customer-location="customerLocation"
          :driver-location="driverLocation" :simulate-driver-movement="true" />
      </div>
    </div>

    <template #footer>
      <div class="flex flex-wrap gap-3 justify-between">
        <div class="flex gap-2">
          <button @click="handlePrint"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800">
            <PrinterIcon class="w-4 h-4" />
            {{ t('orders.print') }}
          </button>
          <button v-if="canCancelOrder" @click="handleCancel"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-red-600">
            <XCircleIcon class="w-4 h-4" />
            {{ t('orders.cancel') }}
          </button>
          <button @click="handleReorder"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800">
            <ArrowPathIcon class="w-4 h-4" />
            {{ t('orders.reorder') }}
          </button>
          <button @click="handleDownloadPDF"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800">
            <DocumentArrowDownIcon class="w-4 h-4" />
            {{ t('orders.download') }}
          </button>
          <button @click="handleSendEmail"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800">
            <EnvelopeIcon class="w-4 h-4" />
            {{ t('orders.sendEmail') }}
          </button>
        </div>
        <div class="flex gap-2">
          <button @click="emit('close')"
            class="inline-flex items-center gap-2 px-4 py-2.5 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary-600">
            {{ t('common.close') }}
          </button>
        </div>
      </div>
    </template>
  </Modal>
</template>
