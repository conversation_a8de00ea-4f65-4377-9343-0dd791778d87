<script setup lang="ts">
import { computed } from 'vue';
import type { Component } from 'vue';

interface Props {
  modelValue: boolean;
  disabled?: boolean;
  label?: string;
  description?: string;
  icon?: Component;
  iconEnabled?: Component;
  iconDisabled?: Component;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  size: 'md',
  showIcon: true
});

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// Computed properties for styling
const switchClasses = computed(() => {
  const baseClasses = 'relative rounded-full cursor-pointer transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2';
  const sizeClasses = {
    sm: 'w-9 h-5',
    md: 'w-12 h-6',
    lg: 'w-14 h-7'
  };
  const stateClasses = props.modelValue
    ? 'bg-primary-600'
    : 'bg-gray-300 dark:bg-neutral-600';
  const disabledClasses = props.disabled
    ? 'opacity-50 cursor-not-allowed'
    : '';

  return `${baseClasses} ${sizeClasses[props.size]} ${stateClasses} ${disabledClasses}`;
});

const sliderClasses = computed(() => {
  const baseClasses = 'absolute bg-white rounded-full transition-transform duration-200 pointer-events-none shadow-sm';
  const sizeClasses = {
    sm: 'top-0.5 left-0.5 rtl:left-auto rtl:right-0.5 w-4 h-4',
    md: 'top-0.5 left-0.5 rtl:left-auto rtl:right-0.5 w-5 h-5',
    lg: 'top-0.5 left-0.5 rtl:left-auto rtl:right-0.5 w-6 h-6'
  };
  const translateClasses = {
    sm: props.modelValue ? 'translate-x-4 rtl:-translate-x-4' : '',
    md: props.modelValue ? 'translate-x-6 rtl:-translate-x-6' : '',
    lg: props.modelValue ? 'translate-x-7 rtl:-translate-x-7' : ''
  };

  return `${baseClasses} ${sizeClasses[props.size]} ${translateClasses[props.size]}`;
});

const iconContainerClasses = computed(() => {
  const baseClasses = 'rounded-lg flex items-center justify-center shadow-sm border';
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  let stateClasses = '';
  if (props.iconEnabled && props.iconDisabled) {
    // Different icons for enabled/disabled states
    stateClasses = props.modelValue
      ? 'bg-gray-50 border-gray-200 text-primary-600 dark:bg-neutral-800 dark:border-neutral-700 dark:text-primary-400'
      : 'bg-gray-100 border-gray-300 text-gray-400 dark:bg-neutral-700 dark:border-neutral-600 dark:text-neutral-500';
  } else {
    // Single icon or theme-based icons
    stateClasses = props.modelValue
      ? 'bg-neutral-800 border-neutral-700 text-primary-400'
      : 'bg-gray-50 border-gray-200 text-primary-600';
  }

  return `${baseClasses} ${sizeClasses[props.size]} ${stateClasses}`;
});

const iconClasses = computed(() => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };
  return sizeClasses[props.size];
});

// Get the appropriate icon to display
const currentIcon = computed(() => {
  if (props.iconEnabled && props.iconDisabled) {
    return props.modelValue ? props.iconEnabled : props.iconDisabled;
  }
  return props.icon;
});

// Handle toggle
const handleToggle = () => {
  if (!props.disabled) {
    emit('update:modelValue', !props.modelValue);
  }
};

// Handle keyboard events
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === ' ' || event.key === 'Enter') {
    event.preventDefault();
    handleToggle();
  }
};
</script>

<template>
  <div class="flex justify-between items-center">
    <!-- Label and Description -->
    <div v-if="label || description || showIcon" class="flex items-center gap-4">
      <!-- Icon -->
      <div v-if="showIcon && currentIcon" :class="iconContainerClasses">
        <component :is="currentIcon" :class="iconClasses" />
      </div>

      <!-- Text Content -->
      <div v-if="label || description" class="text-start">
        <h3 v-if="label" class="text-sm font-medium text-gray-900 dark:text-white">{{ label }}</h3>
        <p v-if="description" class="text-xs text-gray-500 dark:text-gray-400">{{ description }}</p>
      </div>
    </div>

    <!-- Toggle Switch -->
    <button :class="switchClasses" :disabled="disabled" :aria-checked="modelValue"
      :aria-label="label || 'Toggle switch'" role="switch" tabindex="0" @click="handleToggle" @keydown="handleKeydown">
      <span :class="sliderClasses"></span>
    </button>
  </div>
</template>
