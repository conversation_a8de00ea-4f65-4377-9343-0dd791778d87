<script setup lang="ts">
import { computed, type Component } from 'vue';

interface Props {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'ghost' | 'outline';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  icon?: Component;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  rounded?: boolean;
  elevated?: boolean;
  type?: 'button' | 'submit' | 'reset';
  ariaLabel?: string;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  iconPosition: 'left',
  fullWidth: false,
  rounded: false,
  elevated: false,
  type: 'button'
});

const emit = defineEmits(['click']);

// Base button classes
const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none';

// Size classes
const sizeClasses = computed(() => {
  const sizes = {
    xs: 'px-2.5 py-1.5 text-xs gap-1',
    sm: 'px-3 py-2 text-sm gap-1.5',
    md: 'px-4 py-2.5 text-sm gap-2',
    lg: 'px-5 py-3 text-base gap-2.5',
    xl: 'px-6 py-3.5 text-lg gap-3'
  };
  return sizes[props.size];
});

// Variant classes
const variantClasses = computed(() => {
  const variants = {
    primary: 'bg-primary-600 hover:bg-primary-700 text-white border border-transparent focus:ring-primary-500 shadow-sm',
    secondary: 'bg-white dark:bg-neutral-850 hover:bg-gray-50 dark:hover:bg-neutral-800 text-gray-700 dark:text-neutral-200 border border-gray-300 dark:border-neutral-700 focus:ring-gray-500 shadow-sm',
    success: 'bg-success-600 hover:bg-success-700 text-white border border-transparent focus:ring-success-500 shadow-sm',
    warning: 'bg-warning-600 hover:bg-warning-700 text-white border border-transparent focus:ring-warning-500 shadow-sm',
    error: 'bg-error-600 hover:bg-error-700 text-white border border-transparent focus:ring-error-500 shadow-sm',
    info: 'bg-info-600 hover:bg-info-700 text-white border border-transparent focus:ring-info-500 shadow-sm',
    ghost: 'bg-transparent hover:bg-gray-100 dark:hover:bg-neutral-800 text-gray-700 dark:text-neutral-200 border border-transparent focus:ring-gray-500',
    outline: 'bg-transparent hover:bg-primary-50 dark:hover:bg-primary-900/20 text-primary-600 dark:text-primary-400 border border-primary-300 dark:border-primary-700 focus:ring-primary-500'
  };
  return variants[props.variant];
});

// Shape classes
const shapeClasses = computed(() => {
  if (props.rounded) {
    return 'rounded-full';
  }
  return 'rounded-lg';
});

// Width classes
const widthClasses = computed(() => {
  return props.fullWidth ? 'w-full' : '';
});

// Elevation classes
const elevationClasses = computed(() => {
  if (props.elevated) {
    return 'hover:-translate-y-0.5 hover:shadow-md';
  }
  return '';
});

// Icon size based on button size
const iconSize = computed(() => {
  const sizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6'
  };
  return sizes[props.size];
});

// Combined classes
const buttonClasses = computed(() => {
  return [
    baseClasses,
    sizeClasses.value,
    variantClasses.value,
    shapeClasses.value,
    widthClasses.value,
    elevationClasses.value
  ].join(' ');
});

// Handle click
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event);
  }
};
</script>

<template>
  <button :type="type" :class="buttonClasses" :disabled="disabled || loading" :aria-label="ariaLabel"
    @click="handleClick">
    <!-- Loading spinner -->
    <div v-if="loading" :class="iconSize" class="animate-spin">
      <svg class="w-full h-full text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
        </path>
      </svg>
    </div>

    <!-- Left icon -->
    <component v-else-if="icon && iconPosition === 'left'" :is="icon" :class="iconSize" aria-hidden="true" />

    <!-- Button text -->
    <span v-if="$slots.default">
      <slot />
    </span>

    <!-- Right icon -->
    <component v-if="icon && iconPosition === 'right' && !loading" :is="icon" :class="iconSize" aria-hidden="true" />
  </button>
</template>
