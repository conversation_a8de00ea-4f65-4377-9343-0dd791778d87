<script setup lang="ts">
import { computed, type Component } from 'vue';

interface Props {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  rounded?: boolean;
  outlined?: boolean;
  icon?: Component;
  iconPosition?: 'left' | 'right';
  dot?: boolean;
  removable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'sm',
  rounded: false,
  outlined: false,
  iconPosition: 'left',
  dot: false,
  removable: false
});

const emit = defineEmits(['remove']);

// Base badge classes
const baseClasses = 'inline-flex items-center font-medium transition-colors duration-200';

// Size classes
const sizeClasses = computed(() => {
  const sizes = {
    xs: 'px-2 py-0.5 text-xs gap-1',
    sm: 'px-2.5 py-0.5 text-xs gap-1',
    md: 'px-3 py-1 text-sm gap-1.5',
    lg: 'px-4 py-1.5 text-sm gap-2'
  };
  return sizes[props.size];
});

// Variant classes
const variantClasses = computed(() => {
  if (props.outlined) {
    const outlinedVariants = {
      default: 'bg-transparent text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600',
      primary: 'bg-transparent text-primary-700 dark:text-primary-300 border border-primary-300 dark:border-primary-600',
      secondary: 'bg-transparent text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600',
      success: 'bg-transparent text-success-700 dark:text-success-300 border border-success-300 dark:border-success-600',
      warning: 'bg-transparent text-warning-700 dark:text-warning-300 border border-warning-300 dark:border-warning-600',
      error: 'bg-transparent text-error-700 dark:text-error-300 border border-error-300 dark:border-error-600',
      info: 'bg-transparent text-info-700 dark:text-info-300 border border-info-300 dark:border-info-600'
    };
    return outlinedVariants[props.variant];
  }

  const filledVariants = {
    default: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    primary: 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-200',
    secondary: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    success: 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-200',
    warning: 'bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-200',
    error: 'bg-error-100 text-error-800 dark:bg-error-900/20 dark:text-error-200',
    info: 'bg-info-100 text-info-800 dark:bg-info-900/20 dark:text-info-200'
  };
  return filledVariants[props.variant];
});

// Shape classes
const shapeClasses = computed(() => {
  if (props.rounded) {
    return 'rounded-full';
  }
  return 'rounded-md';
});

// Icon size based on badge size
const iconSize = computed(() => {
  const sizes = {
    xs: 'w-3 h-3',
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-4 h-4'
  };
  return sizes[props.size];
});

// Dot size based on badge size
const dotSize = computed(() => {
  const sizes = {
    xs: 'w-1.5 h-1.5',
    sm: 'w-2 h-2',
    md: 'w-2 h-2',
    lg: 'w-2.5 h-2.5'
  };
  return sizes[props.size];
});

// Dot color based on variant
const dotColor = computed(() => {
  const colors = {
    default: 'bg-gray-400 dark:bg-gray-500',
    primary: 'bg-primary-500',
    secondary: 'bg-gray-400 dark:bg-gray-500',
    success: 'bg-success-500',
    warning: 'bg-warning-500',
    error: 'bg-error-500',
    info: 'bg-info-500'
  };
  return colors[props.variant];
});

// Combined classes
const badgeClasses = computed(() => {
  return [
    baseClasses,
    sizeClasses.value,
    variantClasses.value,
    shapeClasses.value
  ].join(' ');
});

// Handle remove
const handleRemove = (event: MouseEvent) => {
  event.stopPropagation();
  emit('remove');
};
</script>

<template>
  <span :class="badgeClasses">
    <!-- Dot indicator -->
    <span
      v-if="dot"
      :class="[dotSize, dotColor, 'rounded-full flex-shrink-0']"
      aria-hidden="true"
    ></span>

    <!-- Left icon -->
    <component
      v-else-if="icon && iconPosition === 'left'"
      :is="icon"
      :class="iconSize"
      aria-hidden="true"
    />

    <!-- Badge text -->
    <span v-if="$slots.default">
      <slot />
    </span>

    <!-- Right icon -->
    <component
      v-if="icon && iconPosition === 'right'"
      :is="icon"
      :class="iconSize"
      aria-hidden="true"
    />

    <!-- Remove button -->
    <button
      v-if="removable"
      type="button"
      class="flex-shrink-0 ml-1 hover:opacity-70 transition-opacity"
      @click="handleRemove"
      :aria-label="$t?.('common.remove') || 'Remove'"
    >
      <svg :class="iconSize" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </span>
</template>
