<script setup lang="ts">
import { ref, computed } from 'vue';

defineOptions({
  inheritAttrs: false
});

interface Props {
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  disabled?: boolean;
  maxWidth?: string;
}

const props = withDefaults(defineProps<Props>(), {
  position: 'top',
  delay: 500,
  disabled: false,
  maxWidth: '200px'
});

const isVisible = ref(false);
let showTimeout: number | null = null;
let hideTimeout: number | null = null;

const tooltipClasses = computed(() => {
  const baseClasses = 'absolute z-[10000] px-3 py-2 text-sm text-white bg-gray-900 dark:bg-gray-700 rounded-lg shadow-lg pointer-events-none transition-opacity duration-200';

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  };

  return `${baseClasses} ${positionClasses[props.position]}`;
});

const arrowClasses = computed(() => {
  const baseClasses = 'absolute w-2 h-2 bg-gray-900 dark:bg-gray-700 transform rotate-45';

  const positionClasses = {
    top: 'top-full left-1/2 transform -translate-x-1/2 -mt-1',
    bottom: 'bottom-full left-1/2 transform -translate-x-1/2 -mb-1',
    left: 'left-full top-1/2 transform -translate-y-1/2 -ml-1',
    right: 'right-full top-1/2 transform -translate-y-1/2 -mr-1'
  };

  return `${baseClasses} ${positionClasses[props.position]}`;
});

const showTooltip = () => {
  if (props.disabled || !props.content) return;

  if (hideTimeout) {
    clearTimeout(hideTimeout);
    hideTimeout = null;
  }

  showTimeout = window.setTimeout(() => {
    isVisible.value = true;
  }, props.delay);
};

const hideTooltip = () => {
  if (showTimeout) {
    clearTimeout(showTimeout);
    showTimeout = null;
  }

  hideTimeout = window.setTimeout(() => {
    isVisible.value = false;
  }, 100);
};
</script>

<template>
  <div :class="['relative inline-block', $attrs.class]" @mouseenter="showTooltip" @mouseleave="hideTooltip">
    <!-- Trigger element -->
    <slot />

    <!-- Tooltip -->
    <div v-if="isVisible && content" :class="tooltipClasses" :style="{ maxWidth: maxWidth }" role="tooltip">
      <!-- Tooltip content with text-start alignment -->
      <div class="text-start whitespace-normal break-words">
        {{ content }}
      </div>

      <!-- Arrow -->
      <div :class="arrowClasses"></div>
    </div>
  </div>
</template>
