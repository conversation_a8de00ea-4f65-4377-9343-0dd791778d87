<!--
  Reusable Pagination Component

  Usage Examples:

  1. Basic Usage:
  <Pagination
    :current-page="currentPage"
    :total-items="totalItems"
    :items-per-page="itemsPerPage"
    @update:current-page="currentPage = $event"
    @update:items-per-page="itemsPerPage = $event"
  />

  2. With Custom Options:
  <Pagination
    :current-page="currentPage"
    :total-items="totalItems"
    :items-per-page="itemsPerPage"
    :items-per-page-options="[5, 10, 20, 50]"
    :show-page-numbers="true"
    :max-visible-pages="7"
    size="lg"
    @update:current-page="currentPage = $event"
    @update:items-per-page="itemsPerPage = $event"
  />

  3. Minimal Version:
  <Pagination
    :current-page="currentPage"
    :total-items="totalItems"
    :items-per-page="itemsPerPage"
    :show-items-per-page="false"
    :show-info="false"
    :show-page-numbers="false"
    size="sm"
    @update:current-page="currentPage = $event"
  />
-->

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/outline';
import { useRTL } from '../../composables/useRTL';

interface Props {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  itemsPerPageOptions?: number[];
  showItemsPerPage?: boolean;
  showInfo?: boolean;
  showPageNumbers?: boolean;
  maxVisiblePages?: number;
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  itemsPerPageOptions: () => [10, 25, 50, 100],
  showItemsPerPage: true,
  showInfo: true,
  showPageNumbers: true,
  maxVisiblePages: 5,
  size: 'md',
  loading: false,
  disabled: false
});

const emit = defineEmits<{
  'update:currentPage': [page: number];
  'update:itemsPerPage': [itemsPerPage: number];
  'page-change': [page: number];
  'items-per-page-change': [itemsPerPage: number];
}>();

const { t } = useI18n();
const { getNavigationArrow } = useRTL();

// Computed properties
const totalPages = computed(() => Math.ceil(props.totalItems / props.itemsPerPage));

const pageRange = computed(() => {
  const start = (props.currentPage - 1) * props.itemsPerPage + 1;
  const end = Math.min(props.currentPage * props.itemsPerPage, props.totalItems);
  return { start, end };
});

const visiblePages = computed(() => {
  const total = totalPages.value;
  const current = props.currentPage;
  const max = props.maxVisiblePages;

  if (total <= max) {
    return Array.from({ length: total }, (_, i) => i + 1);
  }

  const half = Math.floor(max / 2);
  let start = Math.max(1, current - half);
  let end = Math.min(total, start + max - 1);

  if (end - start + 1 < max) {
    start = Math.max(1, end - max + 1);
  }

  return Array.from({ length: end - start + 1 }, (_, i) => start + i);
});

const sizeClasses = computed(() => {
  const sizes = {
    sm: {
      button: 'px-2 py-1 text-xs',
      select: 'px-2 py-1 text-xs',
      text: 'text-xs',
      icon: 'w-3 h-3'
    },
    md: {
      button: 'px-3 py-2 text-sm',
      select: 'px-3 py-2 text-sm',
      text: 'text-sm',
      icon: 'w-4 h-4'
    },
    lg: {
      button: 'px-4 py-2.5 text-base',
      select: 'px-4 py-2.5 text-base',
      text: 'text-base',
      icon: 'w-5 h-5'
    }
  };
  return sizes[props.size];
});

// Methods
const changePage = (page: number) => {
  if (page < 1 || page > totalPages.value || props.disabled || props.loading) return;

  emit('update:currentPage', page);
  emit('page-change', page);
};

const changeItemsPerPage = (newItemsPerPage: number) => {
  if (props.disabled || props.loading) return;

  emit('update:itemsPerPage', newItemsPerPage);
  emit('items-per-page-change', newItemsPerPage);

  // Reset to first page when changing items per page
  if (props.currentPage > 1) {
    changePage(1);
  }
};

const canGoPrevious = computed(() => props.currentPage > 1 && !props.disabled && !props.loading);
const canGoNext = computed(() => props.currentPage < totalPages.value && !props.disabled && !props.loading);

// Get RTL-aware arrow icons
const previousArrowIcon = computed(() => {
  const iconName = getNavigationArrow('previous');
  return iconName === 'ChevronLeftIcon' ? ChevronLeftIcon : ChevronRightIcon;
});

const nextArrowIcon = computed(() => {
  const iconName = getNavigationArrow('next');
  return iconName === 'ChevronLeftIcon' ? ChevronLeftIcon : ChevronRightIcon;
});
</script>

<template>
  <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 text-start"
    :class="{ 'opacity-50 pointer-events-none': loading }">

    <!-- Pagination Info -->
    <div v-if="showInfo" :class="['text-gray-700 dark:text-gray-300', sizeClasses.text]">
      {{ t('pagination.showing', 'Showing') }} {{ pageRange.start }} {{ t('pagination.to', 'to') }} {{ pageRange.end }}
      {{ t('pagination.of', 'of') }} {{ totalItems }} {{ t('pagination.results', 'results') }}
    </div>

    <!-- Pagination Controls -->
    <div class="flex items-center gap-2">
      <!-- Previous Button -->
      <button @click="changePage(currentPage - 1)" :disabled="!canGoPrevious" :class="[
        'flex items-center gap-1 bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded-md text-gray-700 dark:text-gray-300 font-medium transition-colors duration-200',
        sizeClasses.button,
        canGoPrevious
          ? 'hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer'
          : 'opacity-50 cursor-not-allowed'
      ]" :aria-label="t('pagination.previous', 'Previous page')">
        <component :is="previousArrowIcon" :class="sizeClasses.icon" aria-hidden="true" />
        <span>{{ t('pagination.previous', 'Previous') }}</span>
      </button>

      <!-- Page Numbers (if enabled) -->
      <div v-if="showPageNumbers" class="flex items-center gap-1">
        <button v-for="page in visiblePages" :key="page" @click="changePage(page)" :class="[
          'font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1',
          sizeClasses.button,
          page === currentPage
            ? 'bg-primary-600 hover:bg-primary-700 text-white border border-primary-600 shadow-sm transform scale-105'
            : 'bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700 hover:border-primary-300 dark:hover:border-primary-500'
        ]" :disabled="disabled || loading" :aria-current="page === currentPage ? 'page' : undefined">
          {{ page }}
        </button>
      </div>

      <!-- Simple Page Indicator (if page numbers disabled) -->
      <div v-else
        :class="['flex items-center gap-1 px-3 py-2 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-md text-gray-700 dark:text-gray-300', sizeClasses.text]">
        <span class="font-medium">{{ currentPage }}</span>
        <span>{{ t('pagination.of', 'of') }}</span>
        <span class="font-medium">{{ totalPages }}</span>
      </div>

      <!-- Next Button -->
      <button @click="changePage(currentPage + 1)" :disabled="!canGoNext" :class="[
        'flex items-center gap-1 bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded-md text-gray-700 dark:text-gray-300 font-medium transition-colors duration-200',
        sizeClasses.button,
        canGoNext
          ? 'hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer'
          : 'opacity-50 cursor-not-allowed'
      ]" :aria-label="t('pagination.next', 'Next page')">
        <span>{{ t('pagination.next', 'Next') }}</span>
        <component :is="nextArrowIcon" :class="sizeClasses.icon" aria-hidden="true" />
      </button>
    </div>

    <!-- Items Per Page -->
    <div v-if="showItemsPerPage" class="flex items-center gap-2">
      <label :for="`items-per-page-${$attrs.id || 'default'}`"
        :class="['font-medium text-gray-700 dark:text-gray-300', sizeClasses.text]">
        {{ t('pagination.itemsPerPage', 'Items per page') }}
      </label>
      <select :id="`items-per-page-${$attrs.id || 'default'}`" :value="itemsPerPage"
        @change="changeItemsPerPage(Number(($event.target as HTMLSelectElement).value))" :disabled="disabled || loading"
        :class="[
          'bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded-md text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-1 focus:ring-primary-500/50 focus:border-primary-400 transition-colors duration-200',
          sizeClasses.select
        ]">
        <option v-for="option in itemsPerPageOptions" :key="option" :value="option">
          {{ option }}
        </option>
      </select>
    </div>
  </div>
</template>
