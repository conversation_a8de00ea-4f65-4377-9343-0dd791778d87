<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { ChevronDownIcon, MagnifyingGlassIcon } from '@heroicons/vue/24/outline';
import { currencies, getCurrencyByCode } from '../../data/countries';
import type { Currency } from '../../data/countries';

interface Props {
  modelValue: string;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', currency: Currency): void;
}

const props = withDefaults(defineProps<Props>(), {
  label: '',
  placeholder: 'Select currency...',
  disabled: false
});

const emit = defineEmits<Emits>();

const { t, locale } = useI18n();

// Local state
const isOpen = ref(false);
const searchQuery = ref('');
const dropdownRef = ref<HTMLElement>();

// Available currencies as array
const currencyList = computed(() => {
  return Object.values(currencies);
});

// Selected currency
const selectedCurrency = computed(() => {
  return getCurrencyByCode(props.modelValue);
});

// Filtered currencies based on search
const filteredCurrencies = computed(() => {
  if (!searchQuery.value) return currencyList.value;
  
  const query = searchQuery.value.toLowerCase();
  return currencyList.value.filter(currency => {
    const name = locale.value === 'ar' ? currency.name.ar : currency.name.en;
    return name.toLowerCase().includes(query) || 
           currency.code.toLowerCase().includes(query) ||
           currency.symbol.includes(query);
  });
});

// Handle currency selection
const selectCurrency = (currency: Currency) => {
  emit('update:modelValue', currency.code);
  emit('change', currency);
  isOpen.value = false;
  searchQuery.value = '';
};

// Toggle dropdown
const toggleDropdown = () => {
  if (!props.disabled) {
    isOpen.value = !isOpen.value;
    if (isOpen.value) {
      searchQuery.value = '';
    }
  }
};

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false;
    searchQuery.value = '';
  }
};

// Watch for outside clicks
watch(isOpen, (newValue) => {
  if (newValue) {
    document.addEventListener('click', handleClickOutside);
  } else {
    document.removeEventListener('click', handleClickOutside);
  }
});

// Get currency display name
const getCurrencyName = (currency: Currency) => {
  return locale.value === 'ar' ? currency.name.ar : currency.name.en;
};
</script>

<template>
  <div class="relative" ref="dropdownRef">
    <!-- Label -->
    <label v-if="label" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
      {{ label }}
    </label>

    <!-- Dropdown Button -->
    <button
      @click="toggleDropdown"
      :disabled="disabled"
      class="w-full flex items-center justify-between px-4 py-3 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-900 dark:text-white text-sm transition-all duration-200 hover:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
      :class="{ 'border-primary-500 ring-2 ring-primary-500': isOpen }"
    >
      <div class="flex items-center gap-3">
        <span v-if="selectedCurrency" class="font-mono text-primary-600 dark:text-primary-400 font-semibold">
          {{ selectedCurrency.symbol }}
        </span>
        <div v-if="selectedCurrency" class="flex flex-col items-start">
          <span class="font-medium">{{ selectedCurrency.code }}</span>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ getCurrencyName(selectedCurrency) }}
          </span>
        </div>
        <span v-else class="text-gray-500 dark:text-gray-400">
          {{ placeholder }}
        </span>
      </div>
      <ChevronDownIcon 
        class="w-5 h-5 text-gray-400 transition-transform duration-200"
        :class="{ 'rotate-180': isOpen }"
      />
    </button>

    <!-- Dropdown Menu -->
    <div
      v-if="isOpen"
      class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden"
    >
      <!-- Search Input -->
      <div class="p-3 border-b border-gray-200 dark:border-slate-600">
        <div class="relative">
          <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="t('common.search')"
            class="w-full pl-10 pr-4 py-2 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>

      <!-- Currencies List -->
      <div class="max-h-60 overflow-y-auto">
        <button
          v-for="currency in filteredCurrencies"
          :key="currency.code"
          @click="selectCurrency(currency)"
          class="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200"
          :class="{ 'bg-primary-50 dark:bg-primary-900/20': selectedCurrency?.code === currency.code }"
        >
          <span class="font-mono text-primary-600 dark:text-primary-400 font-semibold w-8">
            {{ currency.symbol }}
          </span>
          <div class="flex flex-col items-start flex-1">
            <span class="text-sm font-medium text-gray-900 dark:text-white">
              {{ currency.code }}
            </span>
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ getCurrencyName(currency) }}
            </span>
          </div>
        </button>

        <!-- No results -->
        <div v-if="filteredCurrencies.length === 0" class="px-4 py-6 text-center text-gray-500 dark:text-gray-400 text-sm">
          {{ t('common.noResults') }}
        </div>
      </div>
    </div>
  </div>
</template>
