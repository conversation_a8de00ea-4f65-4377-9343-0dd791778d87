<script setup lang="ts">
import { computed, ref, type Component } from 'vue';
import { EyeIcon, EyeSlashIcon } from '@heroicons/vue/24/outline';

interface Props {
  modelValue?: string | number;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  placeholder?: string;
  label?: string;
  hint?: string;
  error?: string;
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  icon?: Component;
  iconPosition?: 'left' | 'right';
  clearable?: boolean;
  loading?: boolean;
  maxlength?: number;
  minlength?: number;
  min?: number;
  max?: number;
  step?: number;
  autocomplete?: string;
  ariaLabel?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'md',
  variant: 'default',
  iconPosition: 'left',
  clearable: false,
  loading: false,
  disabled: false,
  readonly: false,
  required: false
});

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'clear']);

// Local state
const isFocused = ref(false);
const showPassword = ref(false);

// Input ref
const inputRef = ref<HTMLInputElement | null>(null);

// Computed input type (for password toggle)
const inputType = computed(() => {
  if (props.type === 'password' && showPassword.value) {
    return 'text';
  }
  return props.type;
});

// Base input classes
const baseClasses = 'block w-full transition-colors duration-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed';

// Size classes
const sizeClasses = computed(() => {
  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-sm',
    lg: 'px-5 py-3 text-base'
  };
  return sizes[props.size];
});

// Variant classes
const variantClasses = computed(() => {
  const hasError = !!props.error;
  const variants = {
    default: hasError 
      ? 'bg-white dark:bg-neutral-850 border border-error-300 dark:border-error-700 focus:border-error-500 focus:ring-1 focus:ring-error-500'
      : 'bg-white dark:bg-neutral-850 border border-gray-300 dark:border-neutral-700 focus:border-primary-500 focus:ring-1 focus:ring-primary-500',
    filled: hasError
      ? 'bg-gray-50 dark:bg-neutral-800 border border-error-300 dark:border-error-700 focus:border-error-500 focus:ring-1 focus:ring-error-500'
      : 'bg-gray-50 dark:bg-neutral-800 border border-transparent focus:bg-white dark:focus:bg-neutral-850 focus:border-primary-500 focus:ring-1 focus:ring-primary-500',
    outlined: hasError
      ? 'bg-transparent border-2 border-error-300 dark:border-error-700 focus:border-error-500'
      : 'bg-transparent border-2 border-gray-300 dark:border-neutral-700 focus:border-primary-500'
  };
  return variants[props.variant];
});

// Rounded classes
const roundedClasses = 'rounded-lg';

// Text color classes
const textClasses = 'text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500';

// Icon size based on input size
const iconSize = computed(() => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-5 h-5'
  };
  return sizes[props.size];
});

// Padding adjustments for icons
const paddingClasses = computed(() => {
  const hasLeftIcon = props.icon && props.iconPosition === 'left';
  const hasRightIcon = props.icon && props.iconPosition === 'right';
  const hasPasswordToggle = props.type === 'password';
  const hasClearButton = props.clearable && props.modelValue;

  let leftPadding = '';
  let rightPadding = '';

  if (props.size === 'sm') {
    leftPadding = hasLeftIcon ? 'pl-10' : 'pl-3';
    rightPadding = (hasRightIcon || hasPasswordToggle || hasClearButton) ? 'pr-10' : 'pr-3';
  } else if (props.size === 'lg') {
    leftPadding = hasLeftIcon ? 'pl-12' : 'pl-5';
    rightPadding = (hasRightIcon || hasPasswordToggle || hasClearButton) ? 'pr-12' : 'pr-5';
  } else {
    leftPadding = hasLeftIcon ? 'pl-11' : 'pl-4';
    rightPadding = (hasRightIcon || hasPasswordToggle || hasClearButton) ? 'pr-11' : 'pr-4';
  }

  return `${leftPadding} ${rightPadding}`;
});

// Combined input classes
const inputClasses = computed(() => {
  return [
    baseClasses,
    sizeClasses.value,
    variantClasses.value,
    roundedClasses,
    textClasses,
    paddingClasses.value
  ].join(' ');
});

// Icon position classes
const iconPositionClasses = computed(() => {
  const base = 'absolute top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500';
  
  if (props.iconPosition === 'left') {
    return props.size === 'sm' ? `${base} left-3` : props.size === 'lg' ? `${base} left-4` : `${base} left-3.5`;
  } else {
    return props.size === 'sm' ? `${base} right-3` : props.size === 'lg' ? `${base} right-4` : `${base} right-3.5`;
  }
});

// Handle input
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  emit('update:modelValue', target.value);
};

// Handle focus
const handleFocus = (event: FocusEvent) => {
  isFocused.value = true;
  emit('focus', event);
};

// Handle blur
const handleBlur = (event: FocusEvent) => {
  isFocused.value = false;
  emit('blur', event);
};

// Handle clear
const handleClear = () => {
  emit('update:modelValue', '');
  emit('clear');
  inputRef.value?.focus();
};

// Toggle password visibility
const togglePassword = () => {
  showPassword.value = !showPassword.value;
};

// Focus input
const focus = () => {
  inputRef.value?.focus();
};

// Expose focus method
defineExpose({ focus });
</script>

<template>
  <div class="w-full">
    <!-- Label -->
    <label v-if="label" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
      {{ label }}
      <span v-if="required" class="text-error-500 ml-1">*</span>
    </label>

    <!-- Input container -->
    <div class="relative">
      <!-- Left icon -->
      <div v-if="icon && iconPosition === 'left'" :class="iconPositionClasses">
        <component :is="icon" :class="iconSize" />
      </div>

      <!-- Input field -->
      <input
        ref="inputRef"
        :type="inputType"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled || loading"
        :readonly="readonly"
        :required="required"
        :maxlength="maxlength"
        :minlength="minlength"
        :min="min"
        :max="max"
        :step="step"
        :autocomplete="autocomplete"
        :aria-label="ariaLabel || label"
        :class="inputClasses"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />

      <!-- Right side icons/buttons -->
      <div class="absolute top-1/2 transform -translate-y-1/2 right-3 flex items-center gap-1">
        <!-- Loading spinner -->
        <div v-if="loading" :class="iconSize" class="animate-spin text-gray-400">
          <svg class="w-full h-full" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>

        <!-- Clear button -->
        <button
          v-else-if="clearable && modelValue && !readonly"
          type="button"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          @click="handleClear"
        >
          <svg :class="iconSize" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <!-- Password toggle -->
        <button
          v-else-if="type === 'password'"
          type="button"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          @click="togglePassword"
        >
          <EyeIcon v-if="!showPassword" :class="iconSize" />
          <EyeSlashIcon v-else :class="iconSize" />
        </button>

        <!-- Right icon -->
        <div v-else-if="icon && iconPosition === 'right'" class="text-gray-400 dark:text-gray-500">
          <component :is="icon" :class="iconSize" />
        </div>
      </div>
    </div>

    <!-- Hint text -->
    <p v-if="hint && !error" class="mt-2 text-sm text-gray-500 dark:text-gray-400">
      {{ hint }}
    </p>

    <!-- Error message -->
    <p v-if="error" class="mt-2 text-sm text-error-600 dark:text-error-400">
      {{ error }}
    </p>
  </div>
</template>
