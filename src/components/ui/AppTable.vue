<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/vue/24/outline';
import Pagination from './Pagination.vue';
import SkeletonLoader from './SkeletonLoader.vue';
import { useRTL } from '../../composables/useRTL';
import type { AppTableColumn, AppTablePagination } from '../../types/table';

const { t } = useI18n();
const { getTableAlignment } = useRTL();

interface Props {
  columns: AppTableColumn[];
  data: any[];
  loading?: boolean;
  pagination?: AppTablePagination;
  // Legacy pagination props for backward compatibility
  currentPage?: number;
  totalItems?: number;
  itemsPerPage?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  showPagination?: boolean;
  showItemsPerPage?: boolean;
  emptyMessage?: string;
  loadingRows?: number;
  hoverable?: boolean;
  striped?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  pagination: () => ({ page: 1, pageSize: 10, totalItems: 0 }),
  currentPage: 1,
  totalItems: 0,
  itemsPerPage: 10,
  sortBy: '',
  sortOrder: 'asc',
  showPagination: true,
  showItemsPerPage: true,
  emptyMessage: 'No data available',
  loadingRows: 8,
  hoverable: true,
  striped: false
});

const emit = defineEmits<{
  'update:pagination': [pagination: AppTablePagination];
  'update:current-page': [page: number];
  'update:items-per-page': [itemsPerPage: number];
  'sort': [column: string, order: 'asc' | 'desc'];
  'row-click': [row: any, index: number];
}>();

// Computed properties for pagination compatibility
const currentPage = computed(() => props.pagination?.page || props.currentPage || 1);
const totalItems = computed(() => props.pagination?.totalItems || props.totalItems || 0);
const itemsPerPage = computed(() => props.pagination?.pageSize || props.itemsPerPage || 10);

// Get responsive classes for columns
const getColumnClasses = (column: AppTableColumn) => {
  const tableAlignment = getTableAlignment();
  const classes = ['px-5 py-4 border-b border-gray-200 dark:border-slate-600'];

  // Add RTL-aware text alignment
  Object.keys(tableAlignment).forEach(key => {
    if (tableAlignment[key]) {
      classes.push(key);
    }
  });

  if (column.responsive === 'md') {
    classes.push('hidden md:table-cell');
  } else if (column.responsive === 'lg') {
    classes.push('hidden lg:table-cell');
  } else if (column.responsive === 'xl') {
    classes.push('hidden xl:table-cell');
  }

  return classes.join(' ');
};

// Get header classes
const getHeaderClasses = (column: AppTableColumn) => {
  const tableAlignment = getTableAlignment();
  const classes = [
    'px-5 py-4 border-b border-gray-200 dark:border-slate-600',
    'bg-gray-50 dark:bg-slate-700 font-semibold text-gray-800 dark:text-gray-100',
    'whitespace-nowrap text-sm tracking-wide first:rounded-tl-lg last:rounded-tr-lg rtl:first:rounded-tl-none rtl:first:rounded-tr-lg rtl:last:rounded-tr-none rtl:last:rounded-tl-lg'
  ];

  // Add RTL-aware text alignment
  Object.keys(tableAlignment).forEach(key => {
    if (tableAlignment[key]) {
      classes.push(key);
    }
  });

  if (column.responsive === 'md') {
    classes.push('hidden md:table-cell');
  } else if (column.responsive === 'lg') {
    classes.push('hidden lg:table-cell');
  } else if (column.responsive === 'xl') {
    classes.push('hidden xl:table-cell');
  }

  if (column.sortable) {
    classes.push('cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors duration-200 select-none');
  }

  return classes.join(' ');
};

// Get row classes
const getRowClasses = (index: number) => {
  const classes = ['bg-white dark:bg-slate-800'];

  if (props.hoverable) {
    classes.push('transition-all duration-200 hover:bg-gray-50 dark:hover:bg-slate-700 hover:-translate-y-0.5 hover:shadow-sm cursor-pointer');
  }

  if (props.striped && index % 2 === 1) {
    classes.push('bg-gray-25 dark:bg-slate-750');
  }

  return classes.join(' ');
};

// Handle sort
const handleSort = (column: AppTableColumn) => {
  if (!column.sortable) return;

  const newOrder = props.sortBy === column.key && props.sortOrder === 'asc' ? 'desc' : 'asc';
  emit('sort', column.key, newOrder);
};

// Handle row click
const handleRowClick = (row: any, index: number) => {
  emit('row-click', row, index);
};

// Get cell value
const getCellValue = (row: any, column: AppTableColumn) => {
  if (column.render) {
    return column.render(row[column.key], row);
  }
  return row[column.key];
};

// Handle pagination updates
const handlePageChange = (page: number) => {
  if (props.pagination) {
    emit('update:pagination', {
      ...props.pagination,
      page
    });
  } else {
    emit('update:current-page', page);
  }
};

const handlePageSizeChange = (pageSize: number) => {
  if (props.pagination) {
    emit('update:pagination', {
      ...props.pagination,
      pageSize,
      page: 1 // Reset to first page when changing page size
    });
  } else {
    emit('update:items-per-page', pageSize);
  }
};

// Generate loading skeleton rows
const loadingSkeletonRows = computed(() => {
  return Array.from({ length: props.loadingRows }, (_, index) => index);
});
</script>

<template>
  <div class="w-full">
    <!-- Table Container -->
    <div class="overflow-x-auto border border-gray-200 dark:border-slate-700 rounded-xl shadow-sm">
      <table class="w-full border-collapse border-spacing-0" :class="getTableAlignment()">
        <!-- Table Header -->
        <thead>
          <tr>
            <th v-for="column in columns" :key="column.key" :class="getHeaderClasses(column)"
              :style="column.width ? { width: column.width } : {}" @click="handleSort(column)">
              <div class="flex items-center gap-2 justify-start rtl:justify-start">
                <span>{{ column.label }}</span>
                <div v-if="column.sortable" class="flex items-center">
                  <ArrowUpIcon v-if="sortBy === column.key && sortOrder === 'asc'"
                    class="w-4 h-4 text-primary-600 dark:text-primary-400" aria-hidden="true" />
                  <ArrowDownIcon v-else-if="sortBy === column.key && sortOrder === 'desc'"
                    class="w-4 h-4 text-primary-600 dark:text-primary-400" aria-hidden="true" />
                  <div v-else class="w-4 h-4 flex flex-col justify-center items-center">
                    <ArrowUpIcon class="w-3 h-3 text-gray-400 -mb-1" aria-hidden="true" />
                    <ArrowDownIcon class="w-3 h-3 text-gray-400" aria-hidden="true" />
                  </div>
                </div>
              </div>
            </th>
          </tr>
        </thead>

        <!-- Table Body -->
        <tbody>
          <!-- Loading State -->
          <template v-if="loading">
            <tr v-for="index in loadingSkeletonRows" :key="`loading-${index}`" class="bg-white dark:bg-slate-800">
              <td v-for="column in columns" :key="`loading-${index}-${column.key}`" :class="getColumnClasses(column)">
                <SkeletonLoader type="text" width="w-full" height="h-4" />
              </td>
            </tr>
          </template>

          <!-- Data Rows -->
          <template v-else-if="data && data.length > 0">
            <tr v-for="(row, index) in data" :key="row.id || index" :class="getRowClasses(index)"
              @click="handleRowClick(row, index)">
              <td v-for="column in columns" :key="`${row.id || index}-${column.key}`" :class="getColumnClasses(column)">
                <slot :name="`cell-${column.key}`" :value="row[column.key]" :row="row" :index="index">
                  <span v-html="getCellValue(row, column)"></span>
                </slot>
              </td>
            </tr>
          </template>

          <!-- Empty State -->
          <tr v-else class="bg-white dark:bg-slate-800">
            <td :colspan="columns.length" class="px-5 py-12 text-center">
              <div class="flex flex-col items-center justify-center">
                <div class="w-16 h-16 bg-gray-100 dark:bg-slate-700 rounded-full flex items-center justify-center mb-4">
                  <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ emptyMessage }}</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">{{ t('common.noDataDescription') }}</p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div v-if="showPagination && !loading && data && data.length > 0" class="mt-4">
      <Pagination :current-page="currentPage" :total-items="totalItems" :items-per-page="itemsPerPage"
        :show-items-per-page="showItemsPerPage" :loading="loading" @update:current-page="handlePageChange"
        @update:items-per-page="handlePageSizeChange" />
    </div>
  </div>
</template>
