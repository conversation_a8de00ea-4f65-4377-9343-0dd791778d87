<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { XMarkIcon, WifiIcon } from '@heroicons/vue/24/outline';
import onlineModeStore from '../../store/onlineModeStore';

interface Props {
  showOnlyOnHomePage?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showOnlyOnHomePage: false
});

const { t } = useI18n();

// Local dismissal state
const isDismissed = ref(false);

// Show alert when offline (either network offline OR online mode disabled) and not dismissed
const showAlert = computed(() => {
  // If showOnlyOnHomePage is true, don't show the alert (it will only be shown on DashboardView)
  if (props.showOnlyOnHomePage) {
    return false;
  }

  // Show when online mode is disabled OR when online mode is enabled but network is offline
  const shouldShow = !onlineModeStore.state.onlineModeEnabled.value ||
    (onlineModeStore.state.onlineModeEnabled.value && !onlineModeStore.state.isOnline.value);
  return shouldShow && !isDismissed.value;
});

// Dismiss the alert
const dismissAlert = () => {
  isDismissed.value = true;
};

// Reset dismissal when coming back online
const resetDismissal = () => {
  isDismissed.value = false;
};

// Determine if the offline state is due to disabled online mode or network issue
const isOnlineModeDisabled = computed(() => {
  return !onlineModeStore.state.onlineModeEnabled.value;
});

// Determine alert style based on the reason for being offline
const alertClasses = computed(() => {
  // Always use warning (yellow) style for consistency with AlertMessage
  return 'mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 flex items-center justify-between';
});

const iconClasses = computed(() => {
  // Always use warning (yellow) style for consistency
  return 'w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0';
});

const textClasses = computed(() => {
  // Always use warning (yellow) style for consistency
  return 'text-sm font-medium text-yellow-800 dark:text-yellow-200';
});

const buttonClasses = computed(() => {
  // Always use warning (yellow) style for consistency
  return 'ml-4 rtl:ml-0 rtl:mr-4 text-yellow-400 hover:text-yellow-600 dark:text-yellow-500 dark:hover:text-yellow-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 rounded-md p-1';
});

// Event listeners
let networkCleanup: (() => void) | null = null;
let onlineModeCleanup: (() => void) | null = null;

onMounted(() => {
  // Listen for network status changes
  const handleNetworkChange = () => {
    // Reset dismissed state when network comes back online (and online mode is enabled)
    if (onlineModeStore.state.isOnline.value && onlineModeStore.state.onlineModeEnabled.value) {
      isDismissed.value = false;
    }
  };

  // Listen for online mode changes
  const handleOnlineModeChange = () => {
    // Reset dismissed state when online mode changes (either enabled or disabled)
    isDismissed.value = false;
  };

  // Add event listeners
  window.addEventListener('networkStatusChanged', handleNetworkChange);
  window.addEventListener('onlineModeChanged', handleOnlineModeChange);

  // Store cleanup functions
  networkCleanup = () => {
    window.removeEventListener('networkStatusChanged', handleNetworkChange);
  };

  onlineModeCleanup = () => {
    window.removeEventListener('onlineModeChanged', handleOnlineModeChange);
  };
});

onUnmounted(() => {
  if (networkCleanup) {
    networkCleanup();
  }
  if (onlineModeCleanup) {
    onlineModeCleanup();
  }
});
</script>

<template>
  <div v-if="showAlert" :class="alertClasses" role="alert" aria-live="polite">
    <div class="flex items-center gap-3">
      <WifiIcon :class="iconClasses" aria-hidden="true" />
      <p :class="textClasses">
        {{ t('network.offline') }}
      </p>
    </div>

    <button @click="dismissAlert" :class="buttonClasses" :aria-label="t('common.close')">
      <XMarkIcon class="w-4 h-4" aria-hidden="true" />
    </button>
  </div>
</template>
