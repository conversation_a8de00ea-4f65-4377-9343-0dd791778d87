<script setup lang="ts">
import { computed, type Component } from 'vue';

interface Props {
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  clickable?: boolean;
  loading?: boolean;
  title?: string;
  subtitle?: string;
  icon?: Component;
  iconVariant?: 'default' | 'success' | 'warning' | 'error' | 'info';
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  padding: 'md',
  rounded: 'lg',
  shadow: 'sm',
  hover: false,
  clickable: false,
  loading: false,
  iconVariant: 'default'
});

const emit = defineEmits(['click']);

// Base card classes
const baseClasses = 'bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 transition-all duration-200';

// Variant classes
const variantClasses = computed(() => {
  const variants = {
    default: '',
    elevated: 'shadow-lg hover:shadow-xl',
    outlined: 'border-2',
    filled: 'bg-gray-50 dark:bg-neutral-850'
  };
  return variants[props.variant];
});

// Padding classes
const paddingClasses = computed(() => {
  const paddings = {
    none: '',
    sm: 'p-3',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8',
    xl: 'p-8 sm:p-10'
  };
  return paddings[props.padding];
});

// Rounded classes
const roundedClasses = computed(() => {
  const rounded = {
    none: '',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    full: 'rounded-full'
  };
  return rounded[props.rounded];
});

// Shadow classes
const shadowClasses = computed(() => {
  const shadows = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl'
  };
  return shadows[props.shadow];
});

// Hover classes
const hoverClasses = computed(() => {
  if (props.hover || props.clickable) {
    return 'hover:shadow-md hover:-translate-y-0.5';
  }
  return '';
});

// Clickable classes
const clickableClasses = computed(() => {
  if (props.clickable) {
    return 'cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2';
  }
  return '';
});

// Icon variant classes
const iconVariantClasses = computed(() => {
  const variants = {
    default: 'bg-gray-100 dark:bg-neutral-800 text-primary-600 dark:text-primary-400',
    success: 'bg-success-100 dark:bg-success-900/20 text-success-600 dark:text-success-400',
    warning: 'bg-warning-100 dark:bg-warning-900/20 text-warning-600 dark:text-warning-400',
    error: 'bg-error-100 dark:bg-error-900/20 text-error-600 dark:text-error-400',
    info: 'bg-info-100 dark:bg-info-900/20 text-info-600 dark:text-info-400'
  };
  return variants[props.iconVariant];
});

// Combined classes
const cardClasses = computed(() => {
  return [
    baseClasses,
    variantClasses.value,
    paddingClasses.value,
    roundedClasses.value,
    shadowClasses.value,
    hoverClasses.value,
    clickableClasses.value
  ].join(' ');
});

// Handle click
const handleClick = (event: MouseEvent) => {
  if (props.clickable && !props.loading) {
    emit('click', event);
  }
};
</script>

<template>
  <div :class="cardClasses" @click="handleClick" :tabindex="clickable ? 0 : -1" @keydown.enter="handleClick"
    @keydown.space="handleClick">
    <!-- Loading overlay -->
    <div v-if="loading"
      class="absolute inset-0 bg-white/50 dark:bg-slate-800/50 rounded-lg flex items-center justify-center">
      <div class="animate-spin w-6 h-6">
        <svg class="w-full h-full text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </div>
    </div>

    <!-- Header with icon, title, and subtitle -->
    <div v-if="icon || title || subtitle || $slots.header" class="mb-4">
      <div v-if="$slots.header">
        <slot name="header" />
      </div>
      <div v-else class="flex items-start gap-3">
        <!-- Icon -->
        <div v-if="icon" class="flex-shrink-0">
          <div class="w-10 h-10 rounded-lg flex items-center justify-center" :class="iconVariantClasses">
            <component :is="icon" class="w-5 h-5" />
          </div>
        </div>

        <!-- Title and subtitle -->
        <div v-if="title || subtitle" class="flex-1 min-w-0">
          <h3 v-if="title" class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
            {{ title }}
          </h3>
          <p v-if="subtitle" class="text-sm text-gray-500 dark:text-gray-400">
            {{ subtitle }}
          </p>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <div v-if="$slots.default" class="flex-1">
      <slot />
    </div>

    <!-- Footer -->
    <div v-if="$slots.footer" class="mt-4 pt-4 border-t border-gray-200 dark:border-slate-700">
      <slot name="footer" />
    </div>
  </div>
</template>
