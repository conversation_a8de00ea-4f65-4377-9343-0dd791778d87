<!--
  Reusable SearchFilter Component

  Usage Examples:

  1. Basic Usage (Search only):
  <SearchFilter
    v-model:search="searchQuery"
    :placeholder="t('search.placeholder')"
  />

  2. With Filters:
  <SearchFilter
    v-model:search="searchQuery"
    v-model:filters="filterState"
    :filter-config="filterConfig"
    :placeholder="t('search.placeholder')"
  />

  3. Advanced Configuration:
  <SearchFilter
    v-model:search="searchQuery"
    v-model:filters="filterState"
    :filter-config="filterConfig"
    :placeholder="t('search.placeholder')"
    :search-debounce="1500"
    :show-reset="true"
    size="lg"
    @search="handleSearch"
    @filter-change="handleFilterChange"
    @reset="handleReset"
  />

  4. Custom Filter Groups:
  const filterConfig = {
    status: {
      label: 'Status',
      options: [
        { value: 'all', label: 'All Items' },
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ],
      multiSelect: true
    },
    category: {
      label: 'Category',
      options: [
        { value: 'all', label: 'All Categories' },
        { value: 'electronics', label: 'Electronics' },
        { value: 'books', label: 'Books' }
      ],
      multiSelect: false
    }
  }
-->

<script setup lang="ts">
import { ref, computed, onUnmounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import debounce from 'debounce';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  ArrowPathIcon
} from '@heroicons/vue/24/outline';

interface FilterOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface FilterGroup {
  label: string;
  options: FilterOption[];
  multiSelect?: boolean;
}

interface FilterConfig {
  [key: string]: FilterGroup;
}

interface Props {
  search?: string;
  filters?: Record<string, string | string[]>;
  filterConfig?: FilterConfig;
  placeholder?: string;
  searchDebounce?: number;
  showReset?: boolean;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  search: '',
  filters: () => ({}),
  filterConfig: () => ({}),
  placeholder: '',
  searchDebounce: 500,
  showReset: true,
  size: 'md',
  disabled: false,
  loading: false
});

const emit = defineEmits<{
  'update:search': [search: string];
  'update:filters': [filters: Record<string, string | string[]>];
  'search': [search: string];
  'filter-change': [filterType: string, value: string | string[]];
  'reset': [];
}>();

const { t } = useI18n();

// Local state
const localSearch = ref(props.search);
const localFilters = ref({ ...props.filters });
const showFilters = ref(false);

// Watch for prop changes to sync local state
watch(() => props.search, (newSearch) => {
  localSearch.value = newSearch;
});

watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...newFilters };
}, { deep: true });

// Size configurations
const sizeClasses = computed(() => {
  const sizes = {
    sm: {
      input: 'px-3 py-2 text-sm',
      button: 'px-2.5 py-2 text-sm',
      icon: 'w-4 h-4',
      filterButton: 'px-2 py-1 text-xs',
      dropdown: 'w-72'
    },
    md: {
      input: 'px-4 py-2.5 text-sm',
      button: 'px-3 py-2.5 text-sm',
      icon: 'w-5 h-5',
      filterButton: 'px-2.5 py-1.5 text-xs',
      dropdown: 'w-80'
    },
    lg: {
      input: 'px-5 py-3 text-base',
      button: 'px-4 py-3 text-base',
      icon: 'w-6 h-6',
      filterButton: 'px-3 py-2 text-sm',
      dropdown: 'w-96'
    }
  };
  return sizes[props.size];
});

// Debounced search function
const debouncedSearch = debounce((searchValue: string) => {
  emit('update:search', searchValue);
  emit('search', searchValue);
}, props.searchDebounce);

// Handle search input
const handleSearchInput = () => {
  emit('update:search', localSearch.value);
  debouncedSearch(localSearch.value);
};

// Handle filter change
const handleFilterChange = (filterType: string, value: string) => {
  const filterGroup = props.filterConfig[filterType];
  if (!filterGroup) return;

  let newValue: string | string[];

  if (filterGroup.multiSelect) {
    // Multi-select logic
    const currentArray = Array.isArray(localFilters.value[filterType])
      ? localFilters.value[filterType] as string[]
      : [localFilters.value[filterType] as string || 'all'];

    if (value === 'all') {
      newValue = ['all'];
    } else {
      const allIndex = currentArray.indexOf('all');
      if (allIndex > -1) {
        currentArray.splice(allIndex, 1);
      }

      const valueIndex = currentArray.indexOf(value);
      if (valueIndex > -1) {
        currentArray.splice(valueIndex, 1);
        if (currentArray.length === 0) {
          currentArray.push('all');
        }
      } else {
        currentArray.push(value);
      }
      newValue = [...currentArray];
    }
  } else {
    // Single select logic
    newValue = value;
  }

  localFilters.value[filterType] = newValue;
  emit('update:filters', { ...localFilters.value });
  emit('filter-change', filterType, newValue);
};

// Check if filter option is active
const isFilterActive = (filterType: string, value: string): boolean => {
  const currentValue = localFilters.value[filterType];
  if (Array.isArray(currentValue)) {
    return currentValue.includes(value);
  }
  return currentValue === value;
};

// Reset all filters and search
const resetAll = () => {
  localSearch.value = '';
  const resetFilters: Record<string, string | string[]> = {};

  Object.keys(props.filterConfig).forEach(key => {
    const filterGroup = props.filterConfig[key];
    resetFilters[key] = filterGroup.multiSelect ? ['all'] : 'all';
  });

  localFilters.value = resetFilters;

  // Cancel pending search
  debouncedSearch.clear();

  emit('update:search', '');
  emit('update:filters', resetFilters);
  emit('reset');
};

// Check if any filters are active (not 'all')
const hasActiveFilters = computed(() => {
  return Object.values(localFilters.value).some(value => {
    if (Array.isArray(value)) {
      return !value.includes('all') || value.length > 1;
    }
    return value !== 'all';
  });
});

// Count the total number of active filters
const activeFilterCount = computed(() => {
  let count = 0;

  Object.values(localFilters.value).forEach(value => {
    if (Array.isArray(value)) {
      // For multi-select, count all selected items except 'all'
      const activeItems = value.filter(item => item !== 'all');
      count += activeItems.length;
    } else {
      // For single select, count if not 'all'
      if (value !== 'all') {
        count += 1;
      }
    }
  });

  return count;
});

// Handle click outside to close filter dropdown
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const filterDropdown = document.getElementById('search-filter-dropdown');
  const filterButton = target.closest('[aria-controls="search-filter-dropdown"]');

  if (showFilters.value && filterDropdown && !filterDropdown.contains(target) && !filterButton) {
    showFilters.value = false;
  }
};

// Cleanup
onUnmounted(() => {
  debouncedSearch.clear();
  document.removeEventListener('click', handleClickOutside);
});

// Add click outside listener when dropdown is shown
const toggleFilters = () => {
  showFilters.value = !showFilters.value;

  // Use nextTick to ensure DOM is updated before adding listener
  if (showFilters.value) {
    // Add listener on next tick to avoid immediate closure
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 0);
  } else {
    document.removeEventListener('click', handleClickOutside);
  }
};
</script>

<template>
  <div class="flex flex-col md:flex-row gap-4" :class="{ 'opacity-50 pointer-events-none': loading }">
    <!-- Search Input -->
    <div class="relative flex-1 min-w-0">
      <MagnifyingGlassIcon :class="[
        'absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 rtl:left-auto rtl:right-3',
        sizeClasses.icon
      ]" aria-hidden="true" />
      <input type="text" v-model="localSearch" @input="handleSearchInput"
        :placeholder="placeholder || t('common.search')" :disabled="disabled || loading" :class="[
          'w-full pl-10 pr-4 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg text-gray-800 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500/50 focus:border-primary-400 transition-all duration-200 rtl:pl-4 rtl:pr-10',
          sizeClasses.input
        ]" :aria-label="placeholder" />
    </div>

    <!-- Filter Button (if filters are configured) -->
    <div v-if="Object.keys(filterConfig).length > 0" class="relative">
      <button @click="toggleFilters" :disabled="disabled || loading" :class="[
        'flex items-center gap-2 bg-white dark:bg-neutral-850 border border-gray-300 dark:border-neutral-700 rounded-lg text-gray-700 dark:text-neutral-300 font-medium hover:bg-gray-50 dark:hover:bg-neutral-800 hover:border-gray-400 dark:hover:border-neutral-600 transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-primary-500/50 shadow-sm',
        sizeClasses.button,
        {
          'ring-1 ring-primary-500/50 border-primary-400': showFilters,
          'bg-primary-50 dark:bg-primary-900/20 border-primary-300 dark:border-primary-600': hasActiveFilters
        }
      ]" :aria-expanded="showFilters" aria-controls="search-filter-dropdown">
        <FunnelIcon :class="sizeClasses.icon" aria-hidden="true" />
        <span>{{ t('common.filter') }}</span>
        <span v-if="hasActiveFilters"
          class="ml-1 rtl:ml-0 rtl:mr-1 px-1.5 py-0.5 bg-primary-600 text-white text-xs rounded-full">
          {{ activeFilterCount }}
        </span>
      </button>

      <!-- Filter Dropdown -->
      <div v-if="showFilters" id="search-filter-dropdown" :class="[
        'absolute top-full mt-2 right-0 rtl:right-auto rtl:left-0 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-medium dark:shadow-dark-medium p-4 z-10 text-start rtl:text-right',
        sizeClasses.dropdown
      ]" @click.stop>
        <!-- Filter Groups -->
        <div v-for="(filterGroup, filterType) in filterConfig" :key="filterType" class="mb-4 last:mb-0">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ filterGroup.label }}
          </label>
          <div class="flex flex-wrap gap-1.5">
            <button v-for="option in filterGroup.options" :key="option.value"
              @click.stop="handleFilterChange(filterType.toString(), option.value)" :disabled="option.disabled" :class="[
                'border rounded transition-all duration-200 font-medium',
                sizeClasses.filterButton,
                isFilterActive(filterType.toString(), option.value)
                  ? 'bg-primary-600 text-white border-primary-600 shadow-sm'
                  : 'bg-gray-50 dark:bg-neutral-800 text-gray-600 dark:text-neutral-400 border-gray-200 dark:border-neutral-700 hover:bg-gray-100 dark:hover:bg-neutral-700 hover:text-gray-800 dark:hover:text-neutral-200',
                option.disabled && 'opacity-50 cursor-not-allowed'
              ]">
              {{ option.label }}
            </button>
          </div>
        </div>

        <!-- Action Buttons -->
        <div v-if="showReset" class="flex gap-2 pt-4 border-t border-gray-200 dark:border-slate-700">
          <button @click.stop="resetAll"
            class="flex items-center justify-center gap-1.5 flex-1 px-3 py-2 bg-gray-100 dark:bg-slate-700 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-slate-600 rounded text-sm font-medium hover:bg-gray-200 dark:hover:bg-slate-600 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200">
            <ArrowPathIcon class="w-3.5 h-3.5" aria-hidden="true" />
            <span>{{ t('common.reset') }}</span>
          </button>
          <button @click="showFilters = false"
            class="flex items-center justify-center gap-1.5 px-3 py-2 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-700 rounded text-sm font-medium hover:bg-primary-200 dark:hover:bg-primary-800 transition-all duration-200">
            <XMarkIcon class="w-3.5 h-3.5" aria-hidden="true" />
            <span>{{ t('common.close') }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
