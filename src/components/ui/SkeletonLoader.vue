<script setup lang="ts">
interface Props {
  type?: 'text' | 'circle' | 'rectangle' | 'card' | 'table' | 'button' | 'avatar' | 'list';
  width?: string;
  height?: string;
  rows?: number;
  animated?: boolean;
  className?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  width: 'w-full',
  height: 'h-4',
  rows: 1,
  animated: true,
  className: ''
});

const getSkeletonClasses = () => {
  const baseClasses = 'bg-gray-200 dark:bg-neutral-700 rounded';
  const animationClass = props.animated ? 'animate-pulse' : '';

  switch (props.type) {
    case 'circle':
      return `${baseClasses} rounded-full ${props.width} ${props.height} ${animationClass}`;
    case 'rectangle':
      return `${baseClasses} ${props.width} ${props.height} ${animationClass}`;
    case 'button':
      return `${baseClasses} rounded-lg h-10 w-24 ${animationClass}`;
    case 'avatar':
      return `${baseClasses} rounded-full w-10 h-10 ${animationClass}`;
    case 'text':
    default:
      return `${baseClasses} ${props.width} ${props.height} ${animationClass}`;
  }
};
</script>

<template>
  <!-- Card Skeleton -->
  <div v-if="type === 'card'" class="bg-gray-50 dark:bg-neutral-800 rounded-lg shadow-sm p-4 sm:p-6"
    :class="[animated && 'animate-pulse', className]">
    <div class="flex items-center gap-3 mb-4">
      <div class="w-10 h-10 bg-gray-200 dark:bg-neutral-700 rounded-lg"></div>
      <div class="flex-1">
        <div class="w-32 h-5 bg-gray-200 dark:bg-neutral-700 rounded mb-2"></div>
        <div class="w-48 h-3 bg-gray-200 dark:bg-neutral-700 rounded"></div>
      </div>
    </div>
    <div class="space-y-3">
      <div class="w-full h-4 bg-gray-200 dark:bg-neutral-700 rounded"></div>
      <div class="w-3/4 h-4 bg-gray-200 dark:bg-neutral-700 rounded"></div>
      <div class="w-1/2 h-4 bg-gray-200 dark:bg-neutral-700 rounded"></div>
    </div>
  </div>

  <!-- Table Skeleton -->
  <div v-else-if="type === 'table'" class="space-y-3" :class="[animated && 'animate-pulse', className]">
    <div v-for="i in rows" :key="i" class="flex items-center gap-4 p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg">
      <div class="w-8 h-8 bg-gray-200 dark:bg-neutral-700 rounded"></div>
      <div class="flex-1">
        <div class="w-full h-4 bg-gray-200 dark:bg-neutral-700 rounded mb-2"></div>
        <div class="w-2/3 h-3 bg-gray-200 dark:bg-neutral-700 rounded"></div>
      </div>
      <div class="w-16 h-6 bg-gray-200 dark:bg-neutral-700 rounded"></div>
    </div>
  </div>

  <!-- List Skeleton -->
  <div v-else-if="type === 'list'" class="space-y-4" :class="[animated && 'animate-pulse', className]">
    <div v-for="i in rows" :key="i" class="flex items-center gap-3">
      <div class="w-10 h-10 bg-gray-200 dark:bg-neutral-700 rounded-lg"></div>
      <div class="flex-1">
        <div class="w-full h-4 bg-gray-200 dark:bg-neutral-700 rounded mb-2"></div>
        <div class="w-3/4 h-3 bg-gray-200 dark:bg-neutral-700 rounded"></div>
      </div>
    </div>
  </div>

  <!-- Text Skeleton (Multiple Rows) -->
  <div v-else-if="rows > 1" class="space-y-2" :class="[animated && 'animate-pulse', className]">
    <div v-for="i in rows" :key="i" :class="getSkeletonClasses()"></div>
  </div>

  <!-- Single Element Skeleton -->
  <div v-else :class="[getSkeletonClasses(), className]"></div>
</template>
