<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  count: number;
  variant?: 'primary' | 'error' | 'warning' | 'success';
  size?: 'sm' | 'md' | 'lg';
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'error',
  size: 'sm',
  position: 'top-right'
});

// Computed classes for styling
const badgeClasses = computed(() => {
  const baseClasses = 'absolute text-white text-xs rounded-full flex items-center justify-center font-bold';
  
  // Variant classes
  const variantClasses = {
    primary: 'bg-primary-500',
    error: 'bg-error-500',
    warning: 'bg-warning-500',
    success: 'bg-success-500'
  };
  
  // Size classes
  const sizeClasses = {
    sm: 'w-5 h-5 text-xs',
    md: 'w-6 h-6 text-sm',
    lg: 'w-7 h-7 text-base'
  };
  
  // Position classes - moved further away from icon
  const positionClasses = {
    'top-right': '-top-2 -right-2',
    'top-left': '-top-2 -left-2',
    'bottom-right': '-bottom-2 -right-2',
    'bottom-left': '-bottom-2 -left-2'
  };
  
  return [
    baseClasses,
    variantClasses[props.variant],
    sizeClasses[props.size],
    positionClasses[props.position]
  ].join(' ');
});
</script>

<template>
  <span 
    v-if="count > 0" 
    :class="badgeClasses"
  >
    {{ count > 99 ? '99+' : count }}
  </span>
</template>
