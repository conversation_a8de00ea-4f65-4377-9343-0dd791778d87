<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ExclamationTriangleIcon, XMarkIcon } from '@heroicons/vue/24/outline';
import onlineModeStore from '../../store/onlineModeStore';

const { t } = useI18n();

// Local state
const isVisible = ref(false);
const isDismissed = ref(false);

// Props
interface Props {
  autoHide?: boolean;
  dismissible?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoHide: false,
  dismissible: true
});

// Check if banner should be shown and determine the reason
const updateVisibility = () => {
  if (isDismissed.value) {
    isVisible.value = false;
    return;
  }

  isVisible.value = onlineModeStore.state.shouldShowOfflineBanner.value;
};

// Determine if the offline state is due to disabled online mode or network issue
const isOnlineModeDisabled = computed(() => {
  return !onlineModeStore.state.onlineModeEnabled.value;
});

// Determine banner style based on the reason for being offline
const bannerClasses = computed(() => {
  if (isOnlineModeDisabled.value) {
    // Red for intentionally disabled online mode
    return 'bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800';
  } else {
    // Yellow for network connectivity issues
    return 'bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800';
  }
});

const iconClasses = computed(() => {
  if (isOnlineModeDisabled.value) {
    return 'text-red-600 dark:text-red-400';
  } else {
    return 'text-yellow-600 dark:text-yellow-400';
  }
});

const textClasses = computed(() => {
  if (isOnlineModeDisabled.value) {
    return 'text-red-800 dark:text-red-200';
  } else {
    return 'text-yellow-800 dark:text-yellow-200';
  }
});

const buttonClasses = computed(() => {
  if (isOnlineModeDisabled.value) {
    return 'text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 hover:bg-red-100 dark:hover:bg-red-800/30 focus:ring-red-500 focus:ring-offset-red-50 dark:focus:ring-offset-red-900/20';
  } else {
    return 'text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200 hover:bg-yellow-100 dark:hover:bg-yellow-800/30 focus:ring-yellow-500 focus:ring-offset-yellow-50 dark:focus:ring-offset-yellow-900/20';
  }
});

// Handle dismiss
const dismiss = () => {
  isDismissed.value = true;
  isVisible.value = false;
};

// Event listeners
let networkCleanup: (() => void) | null = null;
let onlineModeCleanup: (() => void) | null = null;

onMounted(() => {
  // Initial visibility check
  updateVisibility();

  // Listen for network status changes
  const handleNetworkChange = () => {
    // Reset dismissed state when network comes back online
    if (onlineModeStore.state.isOnline.value) {
      isDismissed.value = false;
    }
    updateVisibility();
  };

  // Listen for online mode changes
  const handleOnlineModeChange = () => {
    // Reset dismissed state when online mode is disabled
    if (!onlineModeStore.state.onlineModeEnabled.value) {
      isDismissed.value = false;
    }
    updateVisibility();
  };

  // Add event listeners
  window.addEventListener('networkStatusChanged', handleNetworkChange);
  window.addEventListener('onlineModeChanged', handleOnlineModeChange);

  // Store cleanup functions
  networkCleanup = () => {
    window.removeEventListener('networkStatusChanged', handleNetworkChange);
  };

  onlineModeCleanup = () => {
    window.removeEventListener('onlineModeChanged', handleOnlineModeChange);
  };

  // Auto-hide functionality
  if (props.autoHide && isVisible.value) {
    setTimeout(() => {
      if (isVisible.value && !isDismissed.value) {
        dismiss();
      }
    }, 10000); // Auto-hide after 10 seconds
  }
});

onUnmounted(() => {
  if (networkCleanup) {
    networkCleanup();
  }
  if (onlineModeCleanup) {
    onlineModeCleanup();
  }
});
</script>

<template>
  <Transition enter-active-class="transition-all duration-300 ease-out"
    enter-from-class="transform -translate-y-full opacity-0" enter-to-class="transform translate-y-0 opacity-100"
    leave-active-class="transition-all duration-300 ease-in" leave-from-class="transform translate-y-0 opacity-100"
    leave-to-class="transform -translate-y-full opacity-0">
    <div v-if="isVisible" class="fixed top-16 left-0 right-0 z-50 px-4 py-3 shadow-sm backdrop-blur-sm"
      :class="bannerClasses" role="alert" aria-live="polite">
      <div class="flex items-center justify-between max-w-7xl mx-auto">
        <!-- Icon and Message -->
        <div class="flex items-center gap-3">
          <ExclamationTriangleIcon class="w-5 h-5 flex-shrink-0" :class="iconClasses" aria-hidden="true" />
          <p class="text-sm font-medium" :class="textClasses">
            {{ t('network.offline') }}
          </p>
        </div>

        <!-- Dismiss Button -->
        <button v-if="dismissible" @click="dismiss"
          class="ml-4 rtl:ml-0 rtl:mr-4 p-1 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
          :class="buttonClasses" :aria-label="t('common.close')">
          <XMarkIcon class="w-4 h-4" aria-hidden="true" />
        </button>
      </div>
    </div>
  </Transition>
</template>
