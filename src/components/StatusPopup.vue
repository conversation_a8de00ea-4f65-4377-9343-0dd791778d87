<template>
  <Transition enter-active-class="transition-all duration-300 ease-out"
    enter-from-class="opacity-0 transform translate-y-2 scale-95"
    enter-to-class="opacity-100 transform translate-y-0 scale-100"
    leave-active-class="transition-all duration-200 ease-in"
    leave-from-class="opacity-100 transform translate-y-0 scale-100"
    leave-to-class="opacity-0 transform translate-y-2 scale-95">
    <div v-if="isVisible"
      class="absolute bottom-full mb-2 z-[50] bg-white dark:bg-neutral-850 border border-gray-200 dark:border-neutral-700 rounded-lg shadow-xl p-4 w-80 max-w-sm"
      :class="positionClasses" @mouseenter="$emit('mouseenter')" @mouseleave="$emit('mouseleave')">

      <!-- Header -->
      <div class="flex items-center gap-2 mb-3 pb-2 border-b border-gray-200 dark:border-slate-700">
        <component :is="icon" class="w-5 h-5" :class="iconColor" />
        <h3 class="text-sm font-semibold text-gray-800 dark:text-gray-100">
          {{ title }}
        </h3>
      </div>

      <!-- Status -->
      <div class="mb-3">
        <div class="flex items-center gap-2 mb-2">
          <span class="text-xs font-medium uppercase tracking-wide" :class="statusColor">
            {{ status }}
          </span>
        </div>
        <p class="text-xs text-gray-600 dark:text-gray-300 mb-3">
          {{ description }}
        </p>
      </div>

      <!-- Details -->
      <div v-if="details && details.length > 0" class="mb-3">
        <h4 class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2 uppercase tracking-wide">
          {{ currentLocale === 'ar' ? 'التفاصيل' : 'Details' }}
        </h4>
        <ul class="space-y-1">
          <li v-for="detail in details" :key="detail"
            class="text-xs text-gray-600 dark:text-gray-300 flex items-start gap-2">
            <span :class="statusColor" class="mt-0.5">•</span>
            <span>{{ detail }}</span>
          </li>
        </ul>
      </div>

      <!-- Features Section (for version popup) -->
      <div v-if="features && features.length > 0" class="mb-3">
        <h4 class="text-xs font-medium text-primary-600 dark:text-primary-400 mb-2 uppercase tracking-wide">
          {{ currentLocale === 'ar' ? 'ميزات جديدة' : 'New Features' }}
        </h4>
        <ul class="space-y-1">
          <li v-for="feature in features" :key="feature"
            class="text-xs text-gray-600 dark:text-gray-300 flex items-start gap-2">
            <span class="text-green-500 mt-0.5">•</span>
            <span>{{ feature }}</span>
          </li>
        </ul>
      </div>

      <!-- Bug Fixes Section (for version popup) -->
      <div v-if="bugFixes && bugFixes.length > 0" class="mb-3">
        <h4 class="text-xs font-medium text-orange-600 dark:text-orange-400 mb-2 uppercase tracking-wide">
          {{ t('common.bugFixes') }}
        </h4>
        <ul class="space-y-1">
          <li v-for="fix in bugFixes" :key="fix"
            class="text-xs text-gray-600 dark:text-gray-300 flex items-start gap-2">
            <span class="text-orange-500 mt-0.5">•</span>
            <span>{{ fix }}</span>
          </li>
        </ul>
      </div>

      <!-- Read More Link (for version popup) -->
      <div v-if="showReadMore" class="pt-2 border-t border-gray-200 dark:border-slate-700">
        <button
          class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors duration-200"
          @click="$emit('readMore')">
          {{ t('common.readMore', 'Read More') }} →
        </button>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

// Props
interface Props {
  isVisible: boolean;
  icon: any;
  iconColor: string;
  title: string;
  status: string;
  statusColor: string;
  description: string;
  details?: string[];
  features?: string[];
  bugFixes?: string[];
  showReadMore?: boolean;
  position?: 'center' | 'left' | 'right';
}

const props = withDefaults(defineProps<Props>(), {
  details: () => [],
  features: () => [],
  bugFixes: () => [],
  showReadMore: false,
  position: 'center'
});

// Emits
defineEmits<{
  mouseenter: [];
  mouseleave: [];
  readMore: [];
}>();

const { t, locale } = useI18n();

// Current locale
const currentLocale = computed(() => {
  return locale.value;
});

// Position classes based on position prop and locale
const positionClasses = computed(() => {
  const isRTL = currentLocale.value === 'ar';

  switch (props.position) {
    case 'left':
      // Position popup to align with right edge of button to prevent clipping
      // In English: align right edge, In Arabic: align left edge
      return isRTL ? 'left-0' : 'right-0';
    case 'right':
      // Position popup to align with left edge of button to prevent clipping
      // In English: align left edge, In Arabic: align right edge
      return isRTL ? 'right-0' : 'left-0';
    case 'center':
    default:
      return 'left-1/2 transform -translate-x-1/2';
  }
});
</script>
