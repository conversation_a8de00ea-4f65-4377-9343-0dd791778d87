<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  CheckIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline';
import type { OrderRequest } from '../../types/orderRequests';

interface Props {
  orderRequest: OrderRequest | null;
  canAccept?: boolean;
  hasLocked?: boolean;
  processing?: boolean;
  actionCompleted?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  canAccept: false,
  hasLocked: false,
  processing: false,
  actionCompleted: false
});

const emit = defineEmits(['accept', 'reject']);

const { t } = useI18n();

// Action handlers
const handleAcceptClick = () => {
  if (props.processing || props.actionCompleted) return;
  emit('accept');
};

const handleRejectClick = () => {
  if (props.processing || props.actionCompleted) return;
  emit('reject');
};
</script>

<template>
  <div class="flex flex-col sm:flex-row gap-3 justify-center">
    <!-- Reject Button -->
    <button @click="handleRejectClick" :disabled="processing || actionCompleted"
      class="px-8 py-3 bg-error-600 hover:bg-error-700 disabled:bg-error-400 text-white font-medium rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
      :class="{ 'opacity-50 cursor-not-allowed': processing || actionCompleted }">
      <XMarkIcon class="w-5 h-5" />
      {{ processing ? t('orderRequests.processing') : actionCompleted ? t('orderRequests.actionCompleted') :
        t('orderRequests.rejectOrder') }}
    </button>

    <!-- Accept Button -->
    <button @click="handleAcceptClick" :disabled="processing || !canAccept || actionCompleted"
      class="px-8 py-3 bg-success-600 hover:bg-success-700 disabled:bg-success-400 text-white font-medium rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
      :class="{ 'opacity-50 cursor-not-allowed': processing || !canAccept || actionCompleted }">
      <CheckIcon class="w-5 h-5" />
      {{ processing ? t('orderRequests.processing') : actionCompleted ? t('orderRequests.actionCompleted') :
        t('orderRequests.acceptOrder') }}
    </button>
  </div>
</template>
