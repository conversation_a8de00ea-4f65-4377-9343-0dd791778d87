<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import Modal from '../Modal.vue';
import OrderRequestUserDetails from './OrderRequestUserDetails.vue';
import OrderRequestMedicationCard from './OrderRequestMedicationCard.vue';
import OrderRequestActions from './OrderRequestActions.vue';
import Notification from '../Notification.vue';
import {
  ClockIcon,
  MapPinIcon,
  StarIcon,
  ExclamationTriangleIcon,
  SpeakerXMarkIcon
} from '@heroicons/vue/24/outline';
import type { OrderRequest } from '../../types/orderRequests';
import { acceptOrderRequest, rejectOrderRequest, releaseLocked } from '../../services/orderRequestService';
import orderPackStore from '../../store/orderPackStore';

interface Props {
  show: boolean;
  orderRequest: OrderRequest | null;
}

const props = withDefaults(defineProps<Props>(), {
  orderRequest: null
});

const emit = defineEmits(['close', 'action-completed']);

const { t } = useI18n();

// Local state
const isProcessing = ref(false);
const actionCompleted = ref(false);
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationType = ref<'success' | 'error' | 'info'>('success');
const elapsedTime = ref('');
const updateInterval = ref<number | null>(null);
const notificationSoundInterval = ref<number | null>(null);
const audioContext = ref<AudioContext | null>(null);
const audioEnabled = ref(false);

// Computed properties
const modalTitle = computed(() => {
  if (!props.orderRequest) return '';
  return `${t('orderRequests.newOrderRequest')} - ${t(`orderRequests.serviceTypes.${props.orderRequest.serviceType}`)}`;
});

const hasLockedMedications = computed(() => {
  if (!props.orderRequest) return false;
  return props.orderRequest.medications.some(med =>
    med.status === 'locked' && (med.lockDurationHours || 0) > 6
  );
});

const canAcceptOrder = computed(() => {
  if (!props.orderRequest) return false;
  return props.orderRequest.medications.some(med =>
    med.status === 'available' || med.status === 'low_stock'
  );
});

const orderStatusClass = computed(() => {
  if (!props.orderRequest) return '';

  switch (props.orderRequest.status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'accepted':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'expired':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    default:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
  }
});

// Helper functions
const formatElapsedTime = () => {
  if (!props.orderRequest) return '';

  const now = new Date();
  const orderTime = new Date(props.orderRequest.orderTime);
  const diffMs = now.getTime() - orderTime.getTime();

  const minutes = Math.floor(diffMs / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    elapsedTime.value = `${days} ${t('orderRequests.days')} ${t('orderRequests.ago')}`;
  } else if (hours > 0) {
    elapsedTime.value = `${hours} ${t('orderRequests.hours')} ${t('orderRequests.ago')}`;
  } else {
    elapsedTime.value = `${minutes} ${t('orderRequests.minutes')} ${t('orderRequests.ago')}`;
  }
};

const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
  notificationMessage.value = message;
  notificationType.value = type;
  showNotification.value = true;
};

const handlePhoneCopied = (data: { success: boolean; phone?: string; error?: string }) => {
  if (data.success) {
    showToast(t('orderRequests.phoneCopiedSuccess'), 'success');
  } else {
    showToast(data.error || t('orderRequests.phoneCopyError'), 'error');
  }
};

// Enable audio on user interaction
const enableAudio = () => {
  if (!audioEnabled.value) {
    console.log('User clicked - enabling audio and starting sound loop');
    audioEnabled.value = true;
    startNotificationSound(); // Start the full sound loop
  }
};

// Notification sound functions
const createNotificationSound = () => {
  console.log('createNotificationSound called');
  try {
    // Initialize audio context if needed
    if (!audioContext.value) {
      console.log('Creating new audio context');
      audioContext.value = new (window.AudioContext || (window as any).webkitAudioContext)();
    }

    console.log('Audio context state:', audioContext.value.state);

    // Resume audio context if suspended
    if (audioContext.value.state === 'suspended') {
      console.log('Resuming suspended audio context');
      audioContext.value.resume().then(() => {
        playSound();
      }).catch(error => {
        console.warn('Failed to resume audio context:', error);
      });
    } else {
      playSound();
    }

    function playSound() {
      try {
        console.log('Playing sound...');

        // Ensure audio context is still running
        if (audioContext.value!.state === 'suspended') {
          console.log('Audio context suspended, resuming...');
          audioContext.value!.resume();
        }

        const oscillator = audioContext.value!.createOscillator();
        const gainNode = audioContext.value!.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.value!.destination);

        // Create a pleasant notification sound (two-tone chime)
        oscillator.frequency.setValueAtTime(800, audioContext.value!.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.value!.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.3, audioContext.value!.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.value!.currentTime + 0.3);

        oscillator.start(audioContext.value!.currentTime);
        oscillator.stop(audioContext.value!.currentTime + 0.3);

        console.log('Sound played successfully');

        // Mark audio as enabled after first successful play
        audioEnabled.value = true;
      } catch (error) {
        console.warn('Failed to play sound:', error);
      }
    }

  } catch (error) {
    console.warn('Failed to create notification sound:', error);
  }
};

const startNotificationSound = () => {
  // Always stop any existing interval first
  stopNotificationSound();

  console.log('Starting notification sound...');

  // Play sound immediately
  createNotificationSound();

  // Create a simple function to repeat the sound
  const repeatSound = () => {
    console.log('Playing repeated notification sound');
    createNotificationSound();
  };

  // Set up the interval - repeat every 3 seconds
  notificationSoundInterval.value = setInterval(repeatSound, 3000);

  console.log('Sound interval started with ID:', notificationSoundInterval.value);

  // Test the interval is working by logging every second for 10 seconds
  let testCount = 0;
  const testInterval = setInterval(() => {
    testCount++;
    console.log(`Test interval working: ${testCount}`);
    if (testCount >= 10) {
      clearInterval(testInterval);
      console.log('Test interval completed');
    }
  }, 1000);

  // Also create a simple test interval that just logs every 3 seconds
  const simpleTestInterval = setInterval(() => {
    console.log('🔔 Simple 3-second test interval fired!');
  }, 3000);

  // Store it so we can clean it up
  setTimeout(() => {
    clearInterval(simpleTestInterval);
    console.log('Simple test interval cleared');
  }, 15000);
};

const stopNotificationSound = () => {
  try {
    console.log('stopNotificationSound called');
    if (notificationSoundInterval.value) {
      console.log('Stopping notification sound with ID:', notificationSoundInterval.value);
      clearInterval(notificationSoundInterval.value);
      notificationSoundInterval.value = null;
      console.log('Notification sound stopped');
    } else {
      console.log('No notification sound to stop');
    }
  } catch (error) {
    console.warn('Error stopping notification sound:', error);
    // Ensure the interval is cleared even if there's an error
    if (notificationSoundInterval.value) {
      try {
        clearInterval(notificationSoundInterval.value);
      } catch (clearError) {
        console.warn('Error clearing interval:', clearError);
      }
      notificationSoundInterval.value = null;
    }
  }
};

// Action handlers
const handleAcceptOrder = async () => {
  if (!props.orderRequest) return;

  // Stop notification sound when action is taken
  try {
    stopNotificationSound();
  } catch (error) {
    console.warn('Error stopping sound in handleAcceptOrder:', error);
  }
  isProcessing.value = true;

  try {
    const result = await acceptOrderRequest(props.orderRequest.id);

    if (result.success) {
      // Add the order to cart as a new pack with the requester's name
      try {
        const packId = orderPackStore.addAutoAcceptedOrder(props.orderRequest);
        if (packId) {
          console.log('Order added to cart as pack:', packId);
          showToast(t('orderRequests.orderAcceptedAndAddedToCart', { userName: props.orderRequest.userName }), 'success');
        } else {
          console.warn('Failed to add order to cart - pack creation failed');
          showToast(t('orderRequests.orderAccepted'), 'success');
        }
      } catch (cartError) {
        console.error('Error adding order to cart:', cartError);
        showToast(t('orderRequests.orderAccepted'), 'success');
      }

      actionCompleted.value = true;
      emit('action-completed', { action: 'accept', orderRequestId: props.orderRequest.id });

      // Close modal after a delay
      setTimeout(() => {
        emit('close');
      }, 2000);
    } else {
      showToast(result.error || t('orderRequests.acceptError'), 'error');
    }
  } catch (error) {
    showToast(t('orderRequests.acceptError'), 'error');
  } finally {
    isProcessing.value = false;
  }
};

const handleRejectOrder = async () => {
  if (!props.orderRequest) return;

  // Stop notification sound when action is taken
  try {
    stopNotificationSound();
  } catch (error) {
    console.warn('Error stopping sound in handleRejectOrder:', error);
  }
  isProcessing.value = true;

  try {
    const result = await rejectOrderRequest(props.orderRequest.id);

    if (result.success) {
      showToast(t('orderRequests.orderRejected'), 'success');
      actionCompleted.value = true;
      emit('action-completed', { action: 'reject', orderRequestId: props.orderRequest.id });

      // Close modal after a delay
      setTimeout(() => {
        emit('close');
      }, 2000);
    } else {
      showToast(result.error || t('orderRequests.rejectError'), 'error');
    }
  } catch (error) {
    showToast(t('orderRequests.rejectError'), 'error');
  } finally {
    isProcessing.value = false;
  }
};

const handleReleaseLocked = async (medicationId: number) => {
  if (!props.orderRequest) return;

  isProcessing.value = true;

  try {
    const result = await releaseLocked(props.orderRequest.id, medicationId);

    if (result.success) {
      showToast(t('orderRequests.medicationReleased'), 'success');
      emit('action-completed', { action: 'release', orderRequestId: props.orderRequest.id, medicationId });
    } else {
      showToast(result.error || t('orderRequests.releaseError'), 'error');
    }
  } catch (error) {
    showToast(t('orderRequests.releaseError'), 'error');
  } finally {
    isProcessing.value = false;
  }
};

// Lifecycle
onMounted(() => {
  if (props.show && props.orderRequest) {
    formatElapsedTime();
    updateInterval.value = window.setInterval(formatElapsedTime, 60000); // Update every minute

    // Only start sound if audio is already enabled (user has interacted)
    if (audioEnabled.value) {
      setTimeout(() => {
        startNotificationSound();
      }, 100);
    } else {
      console.log('Audio not enabled yet - waiting for user interaction');
    }
  }
});

onUnmounted(() => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value);
    updateInterval.value = null;
  }
  // Stop sound when component unmounts
  try {
    stopNotificationSound();
  } catch (error) {
    console.warn('Error stopping sound in onUnmounted:', error);
  }
});

// Watch for modal show/hide
const handleModalShow = () => {
  if (props.show && props.orderRequest) {
    formatElapsedTime();
    if (!updateInterval.value) {
      updateInterval.value = window.setInterval(formatElapsedTime, 60000);
    }
    // Only start sound if audio is enabled
    if (audioEnabled.value) {
      setTimeout(() => {
        startNotificationSound();
      }, 100);
    }
  } else {
    if (updateInterval.value) {
      clearInterval(updateInterval.value);
      updateInterval.value = null;
    }
    // Stop sound when modal hides
    try {
      stopNotificationSound();
    } catch (error) {
      console.warn('Error stopping sound in handleModalShow:', error);
    }
  }
};

// Watch for prop changes
const handleClose = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value);
    updateInterval.value = null;
  }
  // Stop sound when closing
  try {
    stopNotificationSound();
  } catch (error) {
    console.warn('Error stopping sound in handleClose:', error);
  }
  emit('close');
};
</script>

<template>
  <Modal :show="show" :title="modalTitle" size="lg" :close-on-click-outside="false" :close-on-esc="false"
    :show-close-button="false" @update:show="handleModalShow">
    <div v-if="orderRequest" class="flex flex-col gap-6 text-start rtl:text-right" @click="enableAudio">
      <!-- Audio Status Indicator -->
      <div v-if="!audioEnabled"
        class="bg-info-50 dark:bg-info-900/20 border border-info-200 dark:border-info-800 rounded-lg p-3">
        <div class="flex items-center gap-2">
          <SpeakerXMarkIcon class="w-4 h-4 text-info-600 dark:text-info-400" />
          <p class="text-sm text-info-800 dark:text-info-200">
            {{ t('orderRequests.clickToEnableSound') }}
          </p>
        </div>
      </div>

      <!-- No Available Medications Warning - Top Priority -->
      <div v-if="!canAcceptOrder"
        class="bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 rounded-lg p-4">
        <div class="flex items-start gap-3">
          <ExclamationTriangleIcon class="w-5 h-5 text-warning-600 dark:text-warning-400 flex-shrink-0 mt-0.5" />
          <div>
            <h5 class="font-medium text-warning-900 dark:text-warning-200 mb-1">
              {{ t('orderRequests.noAvailableMedications') }}
            </h5>
            <p class="text-sm text-warning-800 dark:text-warning-300">
              {{ t('orderRequests.cannotAcceptOrderExplanation') }}
            </p>
          </div>
        </div>
      </div>

      <!-- Order Status Header -->
      <div
        class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-slate-800 dark:to-slate-700 border border-gray-200 dark:border-slate-600 rounded-xl p-6 shadow-sm relative overflow-hidden">
        <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-600 to-primary-400"></div>

        <div class="flex justify-between items-start flex-wrap gap-4">
          <div class="flex flex-col gap-2">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white m-0 tracking-wide">
              {{ t('orderRequests.orderRequestDetails') }}
            </h3>
            <div class="flex items-center gap-2">
              <span class="px-3 py-1 rounded-full text-sm font-medium" :class="orderStatusClass">
                {{ t(`orderRequests.serviceTypes.${orderRequest.serviceType}`) }}
              </span>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                ID: {{ orderRequest.id }}
              </span>
            </div>
          </div>

          <div class="flex flex-col items-end gap-2">
            <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <ClockIcon class="w-4 h-4" />
              <span>{{ elapsedTime }}</span>
            </div>
            <div class="flex items-center gap-2 text-sm font-medium text-primary-600 dark:text-primary-400">
              <StarIcon class="w-4 h-4" />
              <span>{{ orderRequest.pointsToEarn }} {{ t('orderRequests.pointsToEarn') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- User Details Section -->
      <OrderRequestUserDetails :order-request="orderRequest" @phone-copied="handlePhoneCopied" />

      <!-- Medications Section -->
      <div class="space-y-4">
        <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
          {{ t('orderRequests.requestedMedications') }}
        </h4>



        <div class="grid gap-4">
          <OrderRequestMedicationCard v-for="medication in orderRequest.medications" :key="medication.id"
            :medication="medication"
            :can-release="medication.status === 'locked' && (medication.lockDurationHours || 0) > 6"
            :processing="isProcessing" @release="handleReleaseLocked" />
        </div>
      </div>

      <!-- Order Summary -->
      <div v-if="orderRequest.totalAmount"
        class="bg-gray-50 dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg p-4">
        <div class="flex justify-between items-center">
          <span class="font-medium text-gray-900 dark:text-white">
            {{ t('orderRequests.totalAmount') }}
          </span>
          <span class="text-lg font-bold text-primary-600 dark:text-primary-400">
            {{ orderRequest.totalAmount }} {{ t('common.currency') }}
          </span>
        </div>
      </div>

      <!-- Notes Section -->
      <div v-if="orderRequest.notes"
        class="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4">
        <h5 class="font-medium text-primary-900 dark:text-primary-200 mb-2">
          {{ t('orderRequests.notes') }}
        </h5>
        <p class="text-sm text-primary-800 dark:text-primary-300">
          {{ orderRequest.notes }}
        </p>
      </div>


    </div>

    <!-- Modal Footer with Actions -->
    <template #footer>
      <OrderRequestActions :order-request="orderRequest" :can-accept="canAcceptOrder" :has-locked="hasLockedMedications"
        :processing="isProcessing" :action-completed="actionCompleted" @accept="handleAcceptOrder"
        @reject="handleRejectOrder" />
    </template>
  </Modal>

  <!-- Notification -->
  <Notification :show="showNotification" :message="notificationMessage" :type="notificationType" :duration="3000"
    @close="showNotification = false" />
</template>
