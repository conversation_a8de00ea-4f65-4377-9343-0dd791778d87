<script setup lang="ts">
import {
  ShoppingBagIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  TruckIcon,
  LockClosedIcon
} from '@heroicons/vue/24/outline';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

interface Props {
  activeTab: string;
}

interface Emits {
  (e: 'update:activeTab', value: string): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const tabs = [
  {
    id: 'orders',
    label: t('orders.allOrders'),
    icon: ShoppingBagIcon
  },
  {
    id: 'invoices',
    label: t('orders.invoices'),
    icon: DocumentTextIcon
  },
  {
    id: 'revenue',
    label: t('orders.revenueSummary'),
    icon: CurrencyDollarIcon
  },
  {
    id: 'top-selling',
    label: t('orders.topSelling'),
    icon: ChartBarIcon
  },
  {
    id: 'delivery',
    label: t('orders.deliveryOrders'),
    icon: TruckIcon
  },
  {
    id: 'reserved',
    label: t('orders.reservedMedications'),
    icon: LockClosedIcon
  }
];

const setActiveTab = (tabId: string) => {
  emit('update:activeTab', tabId);
};
</script>

<template>
  <div class="mb-8 relative">
    <div class="flex border-b border-gray-200 dark:border-slate-700 overflow-x-auto scrollbar-hide relative">
      <button v-for="tab in tabs" :key="tab.id"
        class="flex items-center px-6 py-4 bg-transparent border-none text-gray-600 dark:text-gray-400 font-semibold cursor-pointer transition-all duration-300 whitespace-nowrap relative text-lg hover:text-gray-800 dark:hover:text-gray-100 hover:bg-teal-50 dark:hover:bg-neutral-800 after:content-[''] after:absolute after:bottom-0 after:left-6 after:right-6 after:h-0.5 after:bg-transparent after:scale-x-0 after:origin-center after:transition-all after:duration-300"
        :class="{
          'text-primary-600 dark:text-primary-400 bg-teal-50 dark:bg-neutral-800 after:bg-primary-600 dark:after:bg-primary-400 after:scale-x-100': activeTab === tab.id
        }" @click="setActiveTab(tab.id)">
        <component :is="tab.icon" class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3" />
        <span>{{ tab.label }}</span>
      </button>
    </div>
  </div>
</template>
