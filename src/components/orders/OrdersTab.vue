<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { ArrowPathIcon, InformationCircleIcon } from '@heroicons/vue/24/outline';
import DashboardWidget from '../dashboard/DashboardWidget.vue';
import SearchFilter from '../ui/SearchFilter.vue';
import AppTable from '../ui/AppTable.vue';
import OrderStatusBadge from './OrderStatusBadge.vue';

const { t } = useI18n();

interface Order {
  id: string;
  customerName: string;
  totalAmount: number;
  date: string;
  status: string;
}

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'refresh'): void;
  (e: 'view-order', order: Order): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// Search and filter state
const searchQuery = ref('');
const filters = ref({
  status: 'all',
  period: 'this-month'
});

// Pagination state
const pagination = ref({
  currentPage: 1,
  itemsPerPage: 10,
  totalItems: 0
});

// Filter configuration for SearchFilter component
const filterConfig = {
  status: {
    label: 'Status',
    options: [
      { value: 'all', label: 'All Statuses' },
      { value: 'delivered', label: 'Delivered' },
      { value: 'pending', label: 'Pending' },
      { value: 'canceled', label: 'Canceled' }
    ],
    multiSelect: false
  },
  period: {
    label: 'Time Period',
    options: [
      { value: 'this-month', label: 'This Month' },
      { value: 'last-6-months', label: 'Last 6 Months' },
      { value: 'last-year', label: 'Last Year' },
      { value: 'last-5-years', label: 'Last 5 Years' }
    ],
    multiSelect: false
  }
};

// Mock data - replace with actual data from props or API
const orders = ref<Order[]>([
  {
    id: 'ORD-001',
    customerName: 'Ahmed Hassan',
    totalAmount: 250.50,
    date: '2024-01-15',
    status: 'delivered',
    items: [
      {
        id: 1,
        medicationId: 101,
        medicationName: 'Paracetamol 500mg',
        quantity: 2,
        unitPrice: 25.00,
        totalPrice: 50.00
      },
      {
        id: 2,
        medicationId: 102,
        medicationName: 'Vitamin C 1000mg',
        quantity: 1,
        unitPrice: 45.50,
        totalPrice: 45.50
      },
      {
        id: 3,
        medicationId: 103,
        medicationName: 'Amoxicillin 250mg',
        quantity: 3,
        unitPrice: 51.67,
        totalPrice: 155.00
      }
    ]
  },
  {
    id: 'ORD-002',
    customerName: 'Fatima Ali',
    totalAmount: 180.25,
    date: '2024-01-14',
    status: 'pending',
    items: [
      {
        id: 4,
        medicationId: 104,
        medicationName: 'Ibuprofen 400mg',
        quantity: 2,
        unitPrice: 30.00,
        totalPrice: 60.00
      },
      {
        id: 5,
        medicationId: 105,
        medicationName: 'Omega-3 Fish Oil',
        quantity: 1,
        unitPrice: 120.25,
        totalPrice: 120.25
      }
    ]
  },
  {
    id: 'ORD-003',
    customerName: 'Mohamed Ali',
    totalAmount: 320.75,
    date: '2024-01-13',
    status: 'delivered',
    items: [
      {
        id: 6,
        medicationId: 106,
        medicationName: 'Aspirin 100mg',
        quantity: 1,
        unitPrice: 15.75,
        totalPrice: 15.75
      },
      {
        id: 7,
        medicationId: 107,
        medicationName: 'Multivitamin Complex',
        quantity: 2,
        unitPrice: 152.50,
        totalPrice: 305.00
      }
    ]
  },
  {
    id: 'ORD-004',
    customerName: 'Sara Ahmed',
    totalAmount: 150.00,
    date: '2024-01-12',
    status: 'canceled'
  }
]);

const filteredOrders = computed(() => {
  let filtered = orders.value;

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(order =>
      order.id.toLowerCase().includes(query) ||
      order.customerName.toLowerCase().includes(query) ||
      order.totalAmount.toString().includes(query)
    );
  }

  // Apply status filter
  if (filters.value.status !== 'all') {
    filtered = filtered.filter(order => order.status === filters.value.status);
  }

  // Apply period filter (you can implement date filtering logic here)
  // For now, we'll just return the filtered results

  // Update total items for pagination
  pagination.value.totalItems = filtered.length;

  return filtered;
});

const paginatedOrders = computed(() => {
  const filtered = filteredOrders.value;
  if (!filtered || filtered.length === 0) {
    return [];
  }

  const start = (pagination.value.currentPage - 1) * pagination.value.itemsPerPage;
  const end = start + pagination.value.itemsPerPage;
  return filtered.slice(start, end);
});

// Event handlers
const handleSearch = (search: string) => {
  console.log('Search:', search);
};

const handleFilterChange = (filterType: string, value: string | string[]) => {
  console.log('Filter change:', filterType, value);
};

const handleReset = () => {
  console.log('Reset filters');
};

const refreshData = () => {
  emit('refresh');
};

const viewOrderDetails = (order: Order) => {
  emit('view-order', order);
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-EG', {
    style: 'currency',
    currency: 'EGP'
  }).format(amount);
};

const formatDate = (date: string | undefined | null) => {
  if (!date) return '-';
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '-';
    return dateObj.toLocaleDateString();
  } catch (error) {
    console.warn('Invalid date format:', date);
    return '-';
  }
};

// DataTable configuration
const tableColumns = computed(() => [
  {
    key: 'id',
    label: t('orders.orderId'),
    sortable: true,
    width: '120px',
    responsive: 'always'
  },
  {
    key: 'customerName',
    label: t('orders.customer'),
    sortable: true,
    responsive: 'always'
  },
  {
    key: 'totalAmount',
    label: t('orders.totalAmount'),
    sortable: true,
    responsive: 'always'
  },
  {
    key: 'date',
    label: t('orders.date'),
    sortable: true,
    responsive: 'md'
  },
  {
    key: 'status',
    label: t('orders.status'),
    sortable: true,
    responsive: 'always'
  }
]);

// Handle table events
const handleTableSort = (column: string, order: 'asc' | 'desc') => {
  console.log('Sort:', column, order);
  // Implement sorting logic here
};

const handleTableRowClick = (row: any) => {
  viewOrderDetails(row);
};
</script>

<template>
  <div class="w-full">
    <!-- Search and Filter Controls -->
    <div class="flex items-center gap-4 mb-6">
      <div class="flex-1">
        <SearchFilter v-model:search="searchQuery" v-model:filters="filters" :filter-config="filterConfig"
          :placeholder="t('orders.searchOrdersPlaceholder')" :loading="loading" @search="handleSearch"
          @filter-change="handleFilterChange" @reset="handleReset" />
      </div>
      <button
        class="flex items-center px-5 py-2.5 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-800 dark:text-gray-100 font-semibold cursor-pointer transition-all duration-300 hover:bg-gray-100 dark:hover:bg-slate-600 hover:-translate-y-0.5 hover:shadow-md whitespace-nowrap"
        @click="refreshData">
        <ArrowPathIcon class="w-4 h-4 me-2 text-primary-600 dark:text-primary-400" />
        {{ t('common.refresh') }}
      </button>
    </div>

    <!-- Orders table -->
    <DashboardWidget title="All Orders" id="orders-table" :full-width="true">
      <!-- Tooltip banner -->
      <div
        class="flex items-center gap-2 bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-400 px-4 py-3 rounded-lg mb-4 text-sm">
        <InformationCircleIcon class="w-4 h-4" />
        <span>{{ t('orders.clickToViewOrderDetails') }}</span>
      </div>

      <AppTable :columns="tableColumns" :data="paginatedOrders" :loading="loading"
        :current-page="pagination.currentPage" :total-items="pagination.totalItems"
        :items-per-page="pagination.itemsPerPage" :show-pagination="true" :empty-message="t('orders.noOrdersFound')"
        @sort="handleTableSort" @row-click="handleTableRowClick" @update:current-page="pagination.currentPage = $event"
        @update:items-per-page="pagination.itemsPerPage = $event">
        <!-- Custom cell templates -->
        <template #cell-id="{ value }">
          <span class="font-mono font-semibold text-primary-600 dark:text-primary-400 text-sm">{{ value }}</span>
        </template>

        <template #cell-totalAmount="{ value }">
          <span class="font-semibold text-gray-900 dark:text-white text-sm">{{ formatCurrency(value) }}</span>
        </template>

        <template #cell-date="{ value }">
          <span class="text-sm text-gray-600 dark:text-gray-400">{{ formatDate(value) }}</span>
        </template>

        <template #cell-status="{ value }">
          <OrderStatusBadge :status="value" type="order" />
        </template>
      </AppTable>
    </DashboardWidget>
  </div>
</template>
