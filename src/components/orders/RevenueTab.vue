<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ArrowPathIcon, CurrencyDollarIcon, ShoppingBagIcon, ChartBarIcon } from '@heroicons/vue/24/outline';
import DashboardWidget from '../dashboard/DashboardWidget.vue';
import SearchFilter from '../ui/SearchFilter.vue';
import RevenueChart from '../dashboard/RevenueChart.vue';

const { t } = useI18n();

interface RevenueSummary {
  totalRevenue: number;
  numberOfOrders: number;
  averageOrderValue: number;
}

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// Search and filter state
const searchQuery = ref('');
const filters = ref({
  period: 'this-month',
  metric: 'all'
});

// Filter configuration
const filterConfig = {
  period: {
    label: t('orders.filterBy'),
    options: [
      { value: 'today', label: t('orders.today') },
      { value: 'this-month', label: t('orders.thisMonth') },
      { value: 'this-year', label: t('orders.thisYear') },
      { value: 'custom', label: t('orders.custom') }
    ],
    multiSelect: false
  },
  metric: {
    label: t('orders.metricType'),
    options: [
      { value: 'all', label: t('orders.allMetrics') },
      { value: 'revenue', label: t('orders.revenueOnly') },
      { value: 'orders', label: t('orders.ordersOnly') },
      { value: 'average', label: t('orders.averageValue') }
    ],
    multiSelect: false
  }
};

// Mock data
const revenueSummary = ref<RevenueSummary>({
  totalRevenue: 125450.00,
  numberOfOrders: 342,
  averageOrderValue: 366.81
});

const revenueChartData = ref({
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  datasets: [{
    label: 'Revenue',
    data: [12000, 19000, 15000, 25000, 22000, 30000]
  }]
});

// Filtered chart data based on period
const filteredChartData = computed(() => {
  const baseData = revenueChartData.value;

  switch (filters.value.period) {
    case 'today':
      return {
        labels: ['6 AM', '9 AM', '12 PM', '3 PM', '6 PM', '9 PM'],
        datasets: [{
          label: 'Revenue',
          data: [800, 1200, 2100, 1800, 2500, 1600] // Hourly data for today
        }]
      };
    case 'this-year':
      return {
        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
        datasets: [{
          label: 'Revenue',
          data: [180000, 220000, 195000, 285000] // Quarterly data
        }]
      };
    case 'custom':
      return {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [{
          label: 'Revenue',
          data: [28000, 32000, 29000, 36000] // Weekly data for custom range
        }]
      };
    case 'this-month':
    default:
      return baseData; // Monthly data
  }
});

const filteredRevenueSummary = computed(() => {
  let summary = { ...revenueSummary.value };

  // Apply period filter - adjust values based on selected time period
  switch (filters.value.period) {
    case 'today':
      // Simulate today's data (roughly 1/30th of monthly data)
      summary.totalRevenue = Math.round(summary.totalRevenue / 30);
      summary.numberOfOrders = Math.round(summary.numberOfOrders / 30);
      break;
    case 'this-year':
      // Simulate yearly data (12x monthly data)
      summary.totalRevenue = summary.totalRevenue * 12;
      summary.numberOfOrders = summary.numberOfOrders * 12;
      break;
    case 'custom':
      // For custom range, use current data as placeholder
      break;
    case 'this-month':
    default:
      // Use current monthly data as is
      break;
  }

  // Recalculate average order value
  summary.averageOrderValue = summary.numberOfOrders > 0
    ? summary.totalRevenue / summary.numberOfOrders
    : 0;

  return summary;
});

// Computed property to determine which metrics to show based on metric filter
const visibleMetrics = computed(() => {
  const metric = filters.value.metric;
  return {
    showRevenue: metric === 'all' || metric === 'revenue',
    showOrders: metric === 'all' || metric === 'orders',
    showAverage: metric === 'all' || metric === 'average'
  };
});

// Event handlers
const handleSearch = (search: string) => {
  searchQuery.value = search;
};

const handleFilterChange = (filterType: string, value: string | string[]) => {
  if (filterType === 'period') {
    filters.value.period = value as string;
  } else if (filterType === 'metric') {
    filters.value.metric = value as string;
  }
};

const handleReset = () => {
  searchQuery.value = '';
  filters.value.period = 'this-month';
  filters.value.metric = 'all';
};

const refreshData = () => {
  emit('refresh');
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-EG', {
    style: 'currency',
    currency: 'EGP'
  }).format(amount);
};
</script>

<template>
  <div class="w-full">
    <!-- Search and Filter Controls -->
    <div class="flex items-center gap-4 mb-6">
      <div class="flex-1">
        <SearchFilter v-model:search="searchQuery" v-model:filters="filters" :filter-config="filterConfig"
          :placeholder="t('orders.searchRevenuePlaceholder')" :loading="loading" @search="handleSearch"
          @filter-change="handleFilterChange" @reset="handleReset" />
      </div>
      <button
        class="flex items-center px-5 py-2.5 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-800 dark:text-gray-100 font-semibold cursor-pointer transition-all duration-300 hover:bg-gray-100 dark:hover:bg-slate-600 hover:-translate-y-0.5 hover:shadow-md whitespace-nowrap"
        @click="refreshData">
        <ArrowPathIcon class="w-4 h-4 me-2 text-primary-600 dark:text-primary-400" />
        {{ t('common.refresh') }}
      </button>
    </div>

    <!-- Revenue summary cards -->
    <div v-if="loading" class="animate-pulse grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
      <div v-for="i in 3" :key="i" class="bg-gray-50 dark:bg-neutral-800 rounded-xl p-7 flex items-center gap-5">
        <div class="w-14 h-14 bg-gray-200 dark:bg-neutral-700 rounded-xl"></div>
        <div class="flex-1">
          <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded mb-2 w-24"></div>
          <div class="h-8 bg-gray-200 dark:bg-neutral-700 rounded w-32"></div>
        </div>
      </div>
    </div>

    <div v-else class="grid gap-6 mb-6" :class="{
      'grid-cols-1': (visibleMetrics.showRevenue ? 1 : 0) + (visibleMetrics.showOrders ? 1 : 0) + (visibleMetrics.showAverage ? 1 : 0) === 1,
      'grid-cols-1 md:grid-cols-2': (visibleMetrics.showRevenue ? 1 : 0) + (visibleMetrics.showOrders ? 1 : 0) + (visibleMetrics.showAverage ? 1 : 0) === 2,
      'grid-cols-1 md:grid-cols-2 lg:grid-cols-3': (visibleMetrics.showRevenue ? 1 : 0) + (visibleMetrics.showOrders ? 1 : 0) + (visibleMetrics.showAverage ? 1 : 0) === 3
    }">
      <!-- Total Revenue Card -->
      <div v-if="visibleMetrics.showRevenue"
        class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl p-7 flex items-center gap-5 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
        <div
          class="w-14 h-14 flex items-center justify-center bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 rounded-xl transition-all duration-300">
          <CurrencyDollarIcon class="w-7 h-7" />
        </div>
        <div class="flex-1">
          <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1 text-start">{{ t('orders.totalRevenue')
          }}</h3>
          <p class="text-3xl font-bold text-gray-800 dark:text-gray-100 tracking-tight text-start">{{
            formatCurrency(filteredRevenueSummary.totalRevenue) }}</p>
        </div>
      </div>

      <!-- Number of Orders Card -->
      <div v-if="visibleMetrics.showOrders"
        class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl p-7 flex items-center gap-5 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
        <div
          class="w-14 h-14 flex items-center justify-center bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-xl transition-all duration-300">
          <ShoppingBagIcon class="w-7 h-7" />
        </div>
        <div class="flex-1">
          <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1 text-start">{{ t('orders.numberOfOrders')
          }}</h3>
          <p class="text-3xl font-bold text-gray-800 dark:text-gray-100 tracking-tight text-start">{{
            filteredRevenueSummary.numberOfOrders }}</p>
        </div>
      </div>

      <!-- Average Order Value Card -->
      <div v-if="visibleMetrics.showAverage"
        class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl p-7 flex items-center gap-5 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
        <div
          class="w-14 h-14 flex items-center justify-center bg-amber-100 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400 rounded-xl transition-all duration-300">
          <ChartBarIcon class="w-7 h-7" />
        </div>
        <div class="flex-1">
          <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1 text-start">{{
            t('orders.averageOrderValue') }}</h3>
          <p class="text-3xl font-bold text-gray-800 dark:text-gray-100 tracking-tight text-start">{{
            formatCurrency(filteredRevenueSummary.averageOrderValue) }}</p>
        </div>
      </div>
    </div>

    <!-- Revenue chart -->
    <RevenueChart :data="filteredChartData" :loading="loading" :title="t('orders.revenueTrends')" />
  </div>
</template>
