<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  UserIcon,
  PhoneIcon,
  MapPinIcon,
  ClockIcon,
  TruckIcon,
  ClipboardDocumentIcon
} from '@heroicons/vue/24/outline';
import type { OrderRequest } from '../../types/orderRequests';

interface Props {
  orderRequest: OrderRequest;
}

const props = defineProps<Props>();
const emit = defineEmits(['phone-copied']);
const { t } = useI18n();

// Computed properties
const formattedOrderTime = computed(() => {
  const date = new Date(props.orderRequest.orderTime);
  return date.toLocaleString();
});

const formattedEstimatedDelivery = computed(() => {
  if (!props.orderRequest.estimatedDeliveryTime) return null;
  const date = new Date(props.orderRequest.estimatedDeliveryTime);
  return date.toLocaleString();
});

const formattedReservationExpiry = computed(() => {
  if (!props.orderRequest.reservationExpiryTime) return null;
  const date = new Date(props.orderRequest.reservationExpiryTime);
  return date.toLocaleString();
});

const copyPhoneNumber = async () => {
  try {
    await navigator.clipboard.writeText(props.orderRequest.userPhone);
    emit('phone-copied', {
      success: true,
      phone: props.orderRequest.userPhone
    });
  } catch (error) {
    console.error('Failed to copy phone number:', error);
    emit('phone-copied', {
      success: false,
      error: 'Failed to copy phone number'
    });
  }
};
</script>

<template>
  <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl p-6 shadow-sm">
    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
      {{ t('orderRequests.userInformation') }}
    </h4>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- User Name -->
      <div class="flex items-center gap-3">
        <div class="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-neutral-800 rounded-lg">
          <UserIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
            {{ t('orderRequests.userName') }}
          </p>
          <p class="text-base font-semibold text-gray-900 dark:text-white truncate">
            {{ orderRequest.userName }}
          </p>
        </div>
      </div>

      <!-- Phone Number -->
      <div class="flex items-center gap-3">
        <div class="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-neutral-800 rounded-lg">
          <PhoneIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
            {{ t('orderRequests.phoneNumber') }}
          </p>
          <div class="flex items-center gap-2">
            <p class="text-base font-semibold text-gray-900 dark:text-white">
              {{ orderRequest.userPhone }}
            </p>
            <button @click="copyPhoneNumber"
              class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 rounded hover:bg-gray-100 dark:hover:bg-neutral-700"
              :title="t('orders.copyNumber')">
              <ClipboardDocumentIcon class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <!-- Location -->
      <div class="flex items-start gap-3 md:col-span-2">
        <div
          class="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-neutral-800 rounded-lg flex-shrink-0">
          <MapPinIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
            {{ t('orderRequests.location') }}
          </p>
          <p class="text-base font-semibold text-gray-900 dark:text-white">
            {{ orderRequest.location.address }}
          </p>
        </div>
      </div>

      <!-- Order Time -->
      <div class="flex items-center gap-3">
        <div class="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-neutral-800 rounded-lg">
          <ClockIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
            {{ t('orderRequests.orderTime') }}
          </p>
          <p class="text-base font-semibold text-gray-900 dark:text-white">
            {{ formattedOrderTime }}
          </p>
        </div>
      </div>

      <!-- Estimated Delivery Time (for delivery orders) -->
      <div v-if="orderRequest.serviceType === 'Delivery' && formattedEstimatedDelivery" class="flex items-center gap-3">
        <div class="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-neutral-800 rounded-lg">
          <TruckIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
            {{ t('orderRequests.estimatedDelivery') }}
          </p>
          <p class="text-base font-semibold text-gray-900 dark:text-white">
            {{ formattedEstimatedDelivery }}
          </p>
        </div>
      </div>

      <!-- Reservation Expiry Time (for reserved orders) -->
      <div v-if="orderRequest.serviceType === 'Reserved' && formattedReservationExpiry" class="flex items-center gap-3">
        <div class="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-neutral-800 rounded-lg">
          <ClockIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
            {{ t('orderRequests.reservationExpiry') }}
          </p>
          <p class="text-base font-semibold text-gray-900 dark:text-white">
            {{ formattedReservationExpiry }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
