<script setup lang="ts">
import { computed } from 'vue';
import {
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  TruckIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

interface Props {
  status: string;
  type?: 'order' | 'delivery' | 'reservation';
}

const props = withDefaults(defineProps<Props>(), {
  type: 'order'
});

const statusConfig = computed(() => {
  const configs = {
    order: {
      delivered: {
        icon: CheckCircleIcon,
        class: 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/20'
      },
      pending: {
        icon: ClockIcon,
        class: 'text-yellow-700 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20'
      },
      canceled: {
        icon: XCircleIcon,
        class: 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/20'
      }
    },
    delivery: {
      delivered: {
        icon: CheckCircleIcon,
        class: 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/20'
      },
      pending: {
        icon: ClockIcon,
        class: 'text-yellow-700 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20'
      },
      out_for_delivery: {
        icon: TruckIcon,
        class: 'text-teal-700 bg-teal-100 dark:text-teal-400 dark:bg-teal-900/20'
      },
      canceled: {
        icon: XCircleIcon,
        class: 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/20'
      }
    },
    reservation: {
      active: {
        icon: CheckCircleIcon,
        class: 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/20'
      },
      expired: {
        icon: ExclamationTriangleIcon,
        class: 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/20'
      }
    }
  };

  return configs[props.type]?.[props.status] || {
    icon: ClockIcon,
    class: 'text-gray-700 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20'
  };
});

const statusLabel = computed(() => {
  return t(`orders.statuses.${props.status}`);
});
</script>

<template>
  <div class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full font-medium text-xs w-fit"
    :class="statusConfig.class">
    <component :is="statusConfig.icon" class="w-3.5 h-3.5" />
    <span>{{ statusLabel }}</span>
  </div>
</template>
