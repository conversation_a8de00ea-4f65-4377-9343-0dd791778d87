<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  BeakerIcon as PillIcon,
  CheckCircleIcon,
  XCircleIcon,
  LockClosedIcon,
  ExclamationTriangleIcon,
  ClockIcon
} from '@heroicons/vue/24/outline';
import type { OrderRequestMedication } from '../../types/orderRequests';

interface Props {
  medication: OrderRequestMedication;
  canRelease?: boolean;
  processing?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  canRelease: false,
  processing: false
});

const emit = defineEmits(['release']);

const { t } = useI18n();

// Computed properties
const statusIcon = computed(() => {
  switch (props.medication.status) {
    case 'available':
      return CheckCircleIcon;
    case 'out_of_stock':
      return XCircleIcon;
    case 'locked':
      return LockClosedIcon;
    case 'low_stock':
      return ExclamationTriangleIcon;
    default:
      return PillIcon;
  }
});

const statusClass = computed(() => {
  switch (props.medication.status) {
    case 'available':
      return 'text-success-600 dark:text-success-400 bg-success-100 dark:bg-success-900/20';
    case 'out_of_stock':
      return 'text-error-600 dark:text-error-400 bg-error-100 dark:bg-error-900/20';
    case 'locked':
      return 'text-warning-600 dark:text-warning-400 bg-warning-100 dark:bg-warning-900/20';
    case 'low_stock':
      return 'text-warning-600 dark:text-warning-400 bg-warning-100 dark:bg-warning-900/20';
    default:
      return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/20';
  }
});

const cardBorderClass = computed(() => {
  switch (props.medication.status) {
    case 'available':
      return 'border-success-200 dark:border-success-800';
    case 'out_of_stock':
      return 'border-error-200 dark:border-error-800';
    case 'locked':
      return 'border-warning-200 dark:border-warning-800';
    case 'low_stock':
      return 'border-warning-200 dark:border-warning-800';
    default:
      return 'border-gray-200 dark:border-gray-700';
  }
});

const formattedTotalPrice = computed(() => {
  if (!props.medication.totalPrice) return null;
  return props.medication.totalPrice.toFixed(2);
});

const formattedUnitPrice = computed(() => {
  if (!props.medication.unitPrice) return null;
  return props.medication.unitPrice.toFixed(2);
});

const lockDurationText = computed(() => {
  if (props.medication.status !== 'locked' || !props.medication.lockDurationHours) return null;

  const hours = props.medication.lockDurationHours;
  if (hours > 6) {
    return t('orderRequests.canRelease');
  } else {
    return t('orderRequests.lockedFor', { duration: hours });
  }
});

const handleRelease = () => {
  if (props.canRelease && !props.processing) {
    emit('release', props.medication.medicationId);
  }
};
</script>

<template>
  <div class="bg-white dark:bg-slate-800 border rounded-lg p-4 transition-all duration-200"
    :class="[cardBorderClass, { 'shadow-sm hover:shadow-md': true }]">
    <div class="flex items-start gap-4">
      <!-- Medication Icon -->
      <div class="flex items-center justify-center w-12 h-12 rounded-lg flex-shrink-0" :class="statusClass">
        <component :is="statusIcon" class="w-6 h-6" />
      </div>

      <!-- Medication Details -->
      <div class="flex-1 min-w-0">
        <div class="flex items-start justify-between gap-4 mb-3">
          <div class="flex-1 min-w-0">
            <h5 class="text-lg font-semibold text-gray-900 dark:text-white mb-1 truncate">
              {{ medication.name }}
            </h5>
            <div class="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
              <span>
                {{ t('orderRequests.quantity') }}:
                <span class="font-medium text-gray-900 dark:text-white">{{ medication.quantity }}</span>
              </span>
              <span v-if="formattedUnitPrice">
                {{ t('orderRequests.unitPrice') }}:
                <span class="font-medium text-gray-900 dark:text-white">{{ formattedUnitPrice }} {{ t('common.currency')
                  }}</span>
              </span>
            </div>
          </div>

          <!-- Total Price -->
          <div v-if="formattedTotalPrice" class="text-right flex-shrink-0">
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ t('orderRequests.totalPrice') }}</p>
            <p class="text-lg font-bold text-primary-600 dark:text-primary-400">
              {{ formattedTotalPrice }} {{ t('common.currency') }}
            </p>
          </div>
        </div>

        <!-- Status and Actions Row -->
        <div class="flex items-center justify-between gap-4">
          <!-- Status Badge -->
          <div class="flex items-center gap-2">
            <span class="px-3 py-1 rounded-full text-sm font-medium" :class="statusClass">
              {{ t(`orderRequests.medicationStatuses.${medication.status}`) }}
            </span>

            <!-- Lock Duration Info -->
            <span v-if="lockDurationText" class="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <ClockIcon class="w-3 h-3" />
              {{ lockDurationText }}
            </span>
          </div>

          <!-- Release Button -->
          <button v-if="canRelease" @click="handleRelease" :disabled="processing"
            class="px-2 py-1 bg-warning-100 hover:bg-warning-200 dark:bg-warning-900/20 dark:hover:bg-warning-900/30 text-warning-700 dark:text-warning-300 text-xs font-medium rounded border border-warning-300 dark:border-warning-700 transition-colors duration-200 flex items-center gap-1"
            :class="{ 'opacity-50 cursor-not-allowed': processing }">
            <LockClosedIcon class="w-3 h-3" />
            {{ processing ? t('orderRequests.processing') : t('orderRequests.releaseLocked') }}
          </button>
        </div>

        <!-- Locked Item Warning -->
        <div v-if="medication.status === 'locked' && (medication.lockDurationHours || 0) > 6"
          class="mt-3 p-3 bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 rounded-lg">
          <div class="flex items-start gap-2">
            <ExclamationTriangleIcon class="w-4 h-4 text-warning-600 dark:text-warning-400 flex-shrink-0 mt-0.5" />
            <p class="text-sm text-warning-800 dark:text-warning-200">
              {{ t('orderRequests.lockedItemMessage') }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
