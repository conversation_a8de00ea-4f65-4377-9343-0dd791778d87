<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ArrowPathIcon } from '@heroicons/vue/24/outline';
import DashboardWidget from '../dashboard/DashboardWidget.vue';
import SearchFilter from '../ui/SearchFilter.vue';
import AppTable from '../ui/AppTable.vue';
import type { AppTableColumn } from '../../types/table';

const { t } = useI18n();

interface TopSellingMedication {
  id: string;
  name: string;
  category: string;
  unitsSold: number;
  totalSales: number;
}

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// Search and filter state
const searchQuery = ref('');
const filters = ref({
  sortBy: 'most-sold',
  category: 'all'
});

// Filter configuration
const filterConfig = {
  sortBy: {
    label: t('orders.sortBy'),
    options: [
      { value: 'most-sold', label: t('orders.mostSold') },
      { value: 'highest-revenue', label: t('orders.highestRevenue') }
    ],
    multiSelect: false
  },
  category: {
    label: t('orders.category'),
    options: [
      { value: 'all', label: t('orders.all') },
      { value: 'Pain Relief', label: t('orders.categories.painRelief') },
      { value: 'Antibiotics', label: t('orders.categories.antibiotics') },
      { value: 'Vitamins', label: t('orders.categories.vitamins') },
      { value: 'Digestive', label: t('orders.categories.digestive') }
    ],
    multiSelect: false
  }
};

// Mock data
const topSellingMedications = ref<TopSellingMedication[]>([
  {
    id: '1',
    name: 'Paracetamol 500mg',
    category: 'Pain Relief',
    unitsSold: 450,
    totalSales: 2250.00
  },
  {
    id: '2',
    name: 'Amoxicillin 250mg',
    category: 'Antibiotics',
    unitsSold: 320,
    totalSales: 4800.00
  },
  {
    id: '3',
    name: 'Vitamin D3',
    category: 'Vitamins',
    unitsSold: 280,
    totalSales: 1680.00
  },
  {
    id: '4',
    name: 'Ibuprofen 400mg',
    category: 'Pain Relief',
    unitsSold: 250,
    totalSales: 1875.00
  },
  {
    id: '5',
    name: 'Omeprazole 20mg',
    category: 'Digestive',
    unitsSold: 200,
    totalSales: 3000.00
  }
]);

const sortedTopSellingMedications = computed(() => {
  let medications = [...topSellingMedications.value];

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    medications = medications.filter(medication =>
      medication.name.toLowerCase().includes(query) ||
      medication.category.toLowerCase().includes(query)
    );
  }

  // Apply category filter
  if (filters.value.category !== 'all') {
    medications = medications.filter(medication => medication.category === filters.value.category);
  }

  // Apply sorting
  if (filters.value.sortBy === 'most-sold') {
    return medications.sort((a, b) => b.unitsSold - a.unitsSold);
  } else if (filters.value.sortBy === 'highest-revenue') {
    return medications.sort((a, b) => b.totalSales - a.totalSales);
  }

  return medications;
});

// Event handlers
const handleSearch = (search: string) => {
  console.log('Search:', search);
};

const handleFilterChange = (filterType: string, value: string | string[]) => {
  console.log('Filter change:', filterType, value);
};

const handleReset = () => {
  console.log('Reset filters');
};

const refreshData = () => {
  emit('refresh');
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-EG', {
    style: 'currency',
    currency: 'EGP'
  }).format(amount);
};

// Table columns configuration
const tableColumns = computed<AppTableColumn[]>(() => [
  {
    key: 'name',
    label: t('orders.medicationName'),
    sortable: false,
    width: '40%'
  },
  {
    key: 'category',
    label: t('orders.category'),
    sortable: false,
    width: '20%'
  },
  {
    key: 'unitsSold',
    label: t('orders.unitsSold'),
    sortable: false,
    width: '20%'
  },
  {
    key: 'totalSales',
    label: t('orders.totalSales'),
    sortable: false,
    width: '20%',
    render: (value: number) => formatCurrency(value)
  }
]);
</script>

<template>
  <div class="w-full">
    <!-- Search and Filter Controls -->
    <div class="flex items-center gap-4 mb-6">
      <div class="flex-1">
        <SearchFilter v-model:search="searchQuery" v-model:filters="filters" :filter-config="filterConfig"
          :placeholder="t('orders.searchMedicationsPlaceholder')" :loading="loading" @search="handleSearch"
          @filter-change="handleFilterChange" @reset="handleReset" />
      </div>
      <button
        class="flex items-center px-5 py-2.5 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-800 dark:text-gray-100 font-semibold cursor-pointer transition-all duration-300 hover:bg-gray-100 dark:hover:bg-slate-600 hover:-translate-y-0.5 hover:shadow-md whitespace-nowrap"
        @click="refreshData">
        <ArrowPathIcon class="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2 text-primary-600 dark:text-primary-400" />
        {{ t('common.refresh') }}
      </button>
    </div>

    <!-- Top selling medications table -->
    <DashboardWidget :title="t('orders.topSellingMedications')" id="top-selling-table" :full-width="true">
      <AppTable :columns="tableColumns" :data="sortedTopSellingMedications" :loading="loading" :show-pagination="false"
        :empty-message="t('orders.noMedicationsFound')">
        <!-- Custom cell template for medication name with ranking -->
        <template #cell-name="{ value, index }">
          <div class="flex items-center gap-3">
            <div
              class="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center font-bold text-sm">
              {{ index + 1 }}
            </div>
            <span class="font-medium">{{ value }}</span>
          </div>
        </template>

        <!-- Custom cell template for category -->
        <template #cell-category="{ value }">
          <span
            class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400">
            {{ value }}
          </span>
        </template>

        <!-- Custom cell template for units sold -->
        <template #cell-unitsSold="{ value }">
          <span class="font-semibold text-gray-900 dark:text-gray-100 text-sm">{{ value }}</span>
        </template>

        <!-- Custom cell template for total sales -->
        <template #cell-totalSales="{ value }">
          <span class="font-semibold text-green-600 dark:text-green-400 text-sm">{{ formatCurrency(value) }}</span>
        </template>
      </AppTable>
    </DashboardWidget>
  </div>
</template>
