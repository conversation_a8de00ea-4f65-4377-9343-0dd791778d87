<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ArrowPathIcon, TrashIcon } from '@heroicons/vue/24/outline';
import DashboardWidget from '../dashboard/DashboardWidget.vue';
import SearchFilter from '../ui/SearchFilter.vue';
import AppTable from '../ui/AppTable.vue';
import OrderStatusBadge from './OrderStatusBadge.vue';
import Tooltip from '../ui/Tooltip.vue';
import { formatDate as formatDateUtil } from '../../utils/localeUtils';
import type { AppTableColumn } from '../../types/table';

const { t } = useI18n();

interface ReservedMedication {
  id: string;
  medicationName: string;
  quantityReserved: number;
  reservedBy: string;
  source: string;
  reservationDate: string;
  expiryDate: string;
  status: string;
}

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'refresh'): void;
  (e: 'release-reservation', id: string): void;
}

withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// Search and filter state
const searchQuery = ref('');
const filters = ref({
  status: 'all',
  source: 'all'
});

// Filter configuration
const filterConfig = {
  status: {
    label: t('orders.filterByStatus'),
    options: [
      { value: 'all', label: t('orders.all') },
      { value: 'active', label: t('orders.statuses.active') },
      { value: 'expired', label: t('orders.statuses.expired') }
    ],
    multiSelect: false
  },
  source: {
    label: t('orders.filterBySource'),
    options: [
      { value: 'all', label: t('orders.all') },
      { value: 'app', label: t('orders.sources.app') }
    ],
    multiSelect: false
  }
};

// Mock data
const reservedMedications = ref<ReservedMedication[]>([
  {
    id: '1',
    medicationName: 'Paracetamol 500mg',
    quantityReserved: 10,
    reservedBy: 'Ahmed Hassan',
    source: 'app',
    reservationDate: '2024-01-15',
    expiryDate: '2024-01-22',
    status: 'active'
  },
  {
    id: '2',
    medicationName: 'Amoxicillin 250mg',
    quantityReserved: 5,
    reservedBy: 'Fatima Ali',
    source: 'app',
    reservationDate: '2024-01-10',
    expiryDate: '2024-01-17',
    status: 'expired'
  },
  {
    id: '3',
    medicationName: 'Vitamin D3',
    quantityReserved: 3,
    reservedBy: 'Omar Mohamed',
    source: 'app',
    reservationDate: '2024-01-14',
    expiryDate: '2024-01-21',
    status: 'active'
  },
  {
    id: '4',
    medicationName: 'Ibuprofen 400mg',
    quantityReserved: 8,
    reservedBy: 'Sara Ahmed',
    source: 'app',
    reservationDate: '2024-01-16',
    expiryDate: '2024-01-23',
    status: 'active'
  },
  {
    id: '5',
    medicationName: 'Omeprazole 20mg',
    quantityReserved: 2,
    reservedBy: 'Mohamed Ali',
    source: 'app',
    reservationDate: '2024-01-12',
    expiryDate: '2024-01-19',
    status: 'active'
  },
  {
    id: '6',
    medicationName: 'Aspirin 100mg',
    quantityReserved: 15,
    reservedBy: 'Layla Hassan',
    source: 'app',
    reservationDate: '2024-01-13',
    expiryDate: '2024-01-20',
    status: 'active'
  }
]);

const filteredReservedMedications = computed(() => {
  let filtered = reservedMedications.value;

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(medication =>
      medication.medicationName.toLowerCase().includes(query) ||
      medication.reservedBy.toLowerCase().includes(query)
    );
  }

  // Apply status filter
  if (filters.value.status !== 'all') {
    filtered = filtered.filter(medication => medication.status === filters.value.status);
  }

  // Apply source filter
  if (filters.value.source !== 'all') {
    filtered = filtered.filter(medication => medication.source === filters.value.source);
  }

  return filtered;
});

// Event handlers
const handleSearch = (search: string) => {
  console.log('Search:', search);
};

const handleFilterChange = (filterType: string, value: string | string[]) => {
  console.log('Filter change:', filterType, value);
};

const handleReset = () => {
  console.log('Reset filters');
};

const refreshData = () => {
  emit('refresh');
};

const releaseReservation = (id: string) => {
  emit('release-reservation', id);
};

const formatDate = (date: string | undefined | null) => {
  if (!date) return '-';
  try {
    return formatDateUtil(date);
  } catch (error) {
    console.warn('Invalid date format:', date);
    return '-';
  }
};

// Table columns configuration
const tableColumns = computed<AppTableColumn[]>(() => [
  {
    key: 'medicationName',
    label: t('orders.medicationName'),
    sortable: false,
    width: '20%'
  },
  {
    key: 'quantityReserved',
    label: t('orders.quantityReserved'),
    sortable: false,
    width: '12%'
  },
  {
    key: 'reservedBy',
    label: t('orders.reservedBy'),
    sortable: false,
    width: '15%'
  },
  {
    key: 'source',
    label: t('orders.reservationSource'),
    sortable: false,
    width: '10%'
  },
  {
    key: 'reservationDate',
    label: t('orders.reservationDate'),
    sortable: false,
    width: '13%',
    responsive: 'md'
  },

  {
    key: 'status',
    label: t('orders.status'),
    sortable: false,
    width: '15%'
  },
  {
    key: 'actions',
    label: t('orders.actions'),
    sortable: false,
    width: '12%'
  }
]);
</script>

<template>
  <div class="w-full">
    <!-- Search and Filter Controls -->
    <div class="flex items-center gap-4 mb-6">
      <div class="flex-1">
        <SearchFilter v-model:search="searchQuery" v-model:filters="filters" :filter-config="filterConfig"
          :placeholder="t('orders.searchReservedPlaceholder')" :loading="loading" @search="handleSearch"
          @filter-change="handleFilterChange" @reset="handleReset" />
      </div>
      <Tooltip :content="t('common.refresh')" position="top">
        <button
          class="flex items-center px-5 py-2.5 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-800 dark:text-gray-100 font-semibold cursor-pointer transition-all duration-300 hover:bg-gray-100 dark:hover:bg-slate-600 hover:-translate-y-0.5 hover:shadow-md whitespace-nowrap"
          @click="refreshData">
          <ArrowPathIcon class="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2 text-primary-600 dark:text-primary-400" />
          {{ t('common.refresh') }}
        </button>
      </Tooltip>
    </div>

    <!-- Reserved medications table -->
    <DashboardWidget :title="t('orders.reservedMedications')" id="reserved-medications-table" :full-width="true">
      <AppTable :columns="tableColumns" :data="filteredReservedMedications" :loading="loading" :show-pagination="false"
        :empty-message="t('orders.noReservedMedicationsFound')">
        <!-- Custom cell template for quantity reserved -->
        <template #cell-quantityReserved="{ value }">
          <span class="font-semibold text-teal-600 dark:text-teal-400 text-sm">{{ value }}</span>
        </template>

        <!-- Custom cell template for source -->
        <template #cell-source="{ value }">
          <span
            class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-300">
            {{ t(`orders.sources.${value}`) }}
          </span>
        </template>

        <!-- Custom cell template for reservation date -->
        <template #cell-reservationDate="{ value }">
          {{ formatDate(value) }}
        </template>

        <!-- Custom cell template for status -->
        <template #cell-status="{ row }">
          <OrderStatusBadge :status="row.status" type="reservation" />
        </template>

        <!-- Custom cell template for actions -->
        <template #cell-actions="{ row }">
          <button v-if="row.status === 'active'"
            class="inline-flex items-center px-2.5 py-1.5 bg-primary-600 hover:bg-primary-700 text-white border-none rounded-md font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 hover:shadow-md text-xs gap-1.5"
            @click="releaseReservation(row.id)">
            <TrashIcon class="w-3.5 h-3.5" />
            <span>{{ t('orders.releaseReservation') }}</span>
          </button>
          <span v-else class="text-gray-400 dark:text-gray-500 text-xs">-</span>
        </template>
      </AppTable>
    </DashboardWidget>
  </div>
</template>
