<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { ArrowPathIcon, UserIcon, MapPinIcon, InformationCircleIcon } from '@heroicons/vue/24/outline';
import DashboardWidget from '../dashboard/DashboardWidget.vue';
import SearchFilter from '../ui/SearchFilter.vue';
import AppTable from '../ui/AppTable.vue';
import OrderStatusBadge from './OrderStatusBadge.vue';

const { t } = useI18n();
const route = useRoute();

interface DeliveryOrder {
  id: string;
  orderId: string;
  customerName: string;
  address: string;
  itemsCount: number;
  deliveryTime: string;
  status: string;
  partner: string;
}

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'refresh'): void;
  (e: 'view-details', order: DeliveryOrder): void;
}

withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// Search and filter state
const searchQuery = ref('');
const filters = ref({
  period: 'today',
  status: 'all',
  partner: 'all'
});

// Pagination state
const pagination = ref({
  currentPage: 1,
  itemsPerPage: 10,
  totalItems: 0
});

// Filter configuration
const filterConfig = {
  period: {
    label: 'Time Period',
    options: [
      { value: 'today', label: 'Today' },
      { value: 'last-7-days', label: 'Last 7 Days' },
      { value: 'this-month', label: 'This Month' },
      { value: 'all', label: 'All Time' }
    ],
    multiSelect: false
  },
  status: {
    label: 'Status',
    options: [
      { value: 'all', label: 'All Statuses' },
      { value: 'pending', label: 'Pending' },
      { value: 'out_for_delivery', label: 'Out for Delivery' },
      { value: 'delivered', label: 'Delivered' }
    ],
    multiSelect: false
  },
  partner: {
    label: 'Delivery Partner',
    options: [
      { value: 'all', label: 'All Partners' },
      { value: 'talabat', label: 'Talabat' },
      { value: 'careem', label: 'Careem' },
      { value: 'pharmacy_driver', label: 'Pharmacy Driver' },
      { value: 'other', label: 'Other' }
    ],
    multiSelect: false
  }
};

// Mock data
const deliveryOrders = ref<DeliveryOrder[]>([
  {
    id: '1',
    orderId: 'ORD-001',
    customerName: 'Ahmed Hassan',
    address: '123 Main St, Cairo',
    itemsCount: 3,
    deliveryTime: '2024-01-15T14:30:00',
    status: 'delivered',
    partner: 'talabat'
  },
  {
    id: '2',
    orderId: 'ORD-002',
    customerName: 'Fatima Ali',
    address: '456 Elm St, Giza',
    itemsCount: 2,
    deliveryTime: '2024-01-15T16:00:00',
    status: 'out_for_delivery',
    partner: 'careem'
  },
  {
    id: '3',
    orderId: 'ORD-003',
    customerName: 'Mohamed Ali',
    address: '789 Oak St, Alexandria',
    itemsCount: 4,
    deliveryTime: '2024-01-15T18:00:00',
    status: 'out_for_delivery',
    partner: 'pharmacy_driver'
  },
  {
    id: '4',
    orderId: 'ORD-004',
    customerName: 'Sara Ahmed',
    address: '321 Pine St, Mansoura',
    itemsCount: 1,
    deliveryTime: '2024-01-15T12:00:00',
    status: 'pending',
    partner: 'talabat'
  }
]);

const filteredDeliveryOrders = computed(() => {
  let filtered = deliveryOrders.value;

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(order =>
      order.orderId.toLowerCase().includes(query) ||
      order.customerName.toLowerCase().includes(query) ||
      order.address.toLowerCase().includes(query)
    );
  }

  // Apply status filter
  if (filters.value.status !== 'all') {
    filtered = filtered.filter(order => order.status === filters.value.status);
  }

  // Apply partner filter
  if (filters.value.partner !== 'all') {
    filtered = filtered.filter(order => order.partner === filters.value.partner);
  }

  // Update total items for pagination
  pagination.value.totalItems = filtered.length;

  return filtered;
});

const paginatedDeliveryOrders = computed(() => {
  const filtered = filteredDeliveryOrders.value;
  if (!filtered || filtered.length === 0) {
    return [];
  }

  const start = (pagination.value.currentPage - 1) * pagination.value.itemsPerPage;
  const end = start + pagination.value.itemsPerPage;
  return filtered.slice(start, end);
});

// DataTable configuration
const tableColumns = computed(() => [
  {
    key: 'orderId',
    label: t('orders.orderId'),
    sortable: true,
    width: '120px',
    responsive: 'always' as const
  },
  {
    key: 'customerName',
    label: t('orders.customer'),
    sortable: true,
    responsive: 'always' as const
  },
  {
    key: 'address',
    label: t('orders.address'),
    sortable: false,
    responsive: 'md' as const
  },
  {
    key: 'itemsCount',
    label: t('orders.itemsCount'),
    sortable: true,
    responsive: 'lg' as const
  },
  {
    key: 'deliveryTime',
    label: t('orders.deliveryTime'),
    sortable: true,
    responsive: 'md' as const
  },
  {
    key: 'status',
    label: t('orders.status'),
    sortable: true,
    responsive: 'always' as const
  },
  {
    key: 'partner',
    label: t('orders.deliveryPartner'),
    sortable: false,
    responsive: 'lg' as const
  }
]);

// Event handlers
const handleSearch = (search: string) => {
  console.log('Search:', search);
};

const handleFilterChange = (filterType: string, value: string | string[]) => {
  console.log('Filter change:', filterType, value);
};

const handleReset = () => {
  console.log('Reset filters');
};

const refreshData = () => {
  emit('refresh');
};

const openDeliveryOrderDetails = (order: DeliveryOrder) => {
  emit('view-details', order);
};

const formatDate = (date: string | undefined | null) => {
  if (!date) return '-';
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '-';
    return dateObj.toLocaleDateString();
  } catch (error) {
    console.warn('Invalid date format:', date);
    return '-';
  }
};

// Handle table events
const handleTableSort = (column: string, order: 'asc' | 'desc') => {
  console.log('Sort:', column, order);
  // Implement sorting logic here
};

const handleTableRowClick = (row: any) => {
  openDeliveryOrderDetails(row);
};

// Apply URL parameters on mount
const applyUrlParams = () => {
  try {
    // Check for status parameter in URL
    const statusParam = route.query.status as string;
    if (statusParam && ['pending', 'out_for_delivery', 'delivered'].includes(statusParam)) {
      filters.value.status = statusParam;
    }
  } catch (error) {
    console.error('Error applying URL parameters:', error);
  }
};

// Initialize component
onMounted(() => {
  applyUrlParams();
});
</script>

<template>
  <div class="w-full">
    <!-- Search and Filter Controls -->
    <div class="flex items-center gap-4 mb-6">
      <div class="flex-1">
        <SearchFilter v-model:search="searchQuery" v-model:filters="filters" :filter-config="filterConfig"
          :placeholder="t('orders.searchDeliveryPlaceholder')" :loading="loading" @search="handleSearch"
          @filter-change="handleFilterChange" @reset="handleReset" />
      </div>
      <button
        class="flex items-center px-5 py-2.5 bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-lg text-gray-800 dark:text-neutral-100 font-semibold cursor-pointer transition-all duration-300 hover:bg-gray-100 dark:hover:bg-neutral-700 hover:-translate-y-0.5 hover:shadow-md whitespace-nowrap"
        @click="refreshData">
        <ArrowPathIcon class="w-4 h-4 me-2 text-primary-600 dark:text-primary-400" />
        {{ t('common.refresh') }}
      </button>
    </div>

    <!-- Delivery orders table -->
    <DashboardWidget :title="t('orders.deliveryOrders')" id="delivery-orders-table" :full-width="true">
      <!-- Tooltip banner -->
      <div
        class="flex items-center gap-2 bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-400 px-4 py-3 rounded-lg mb-4 text-sm">
        <InformationCircleIcon class="w-4 h-4" />
        <span>{{ t('orders.clickToViewDetails') }}</span>
      </div>

      <AppTable :columns="tableColumns" :data="paginatedDeliveryOrders" :loading="loading"
        :current-page="pagination.currentPage" :total-items="pagination.totalItems"
        :items-per-page="pagination.itemsPerPage" :show-pagination="true"
        :empty-message="t('orders.noDeliveryOrdersFound')" @sort="handleTableSort" @row-click="handleTableRowClick"
        @update:current-page="pagination.currentPage = $event"
        @update:items-per-page="pagination.itemsPerPage = $event">
        <!-- Custom cell templates -->
        <template #cell-orderId="{ value }">
          <span class="font-mono font-semibold text-primary-600 dark:text-primary-400 text-sm">{{ value }}</span>
        </template>

        <template #cell-customerName="{ value }">
          <div class="flex items-center gap-2">
            <UserIcon class="w-4 h-4 text-primary-600 dark:text-primary-400" />
            <span class="text-sm">{{ value }}</span>
          </div>
        </template>

        <template #cell-address="{ value }">
          <div class="flex items-center gap-2">
            <MapPinIcon class="w-4 h-4 text-primary-600 dark:text-primary-400" />
            <span class="truncate max-w-xs text-sm">{{ value }}</span>
          </div>
        </template>

        <template #cell-itemsCount="{ value }">
          <span class="font-semibold text-sm">{{ value }}</span>
        </template>

        <template #cell-deliveryTime="{ value }">
          <span class="text-sm text-gray-600 dark:text-gray-400">{{ formatDate(value) }}</span>
        </template>

        <template #cell-status="{ value }">
          <OrderStatusBadge :status="value" type="delivery" />
        </template>

        <template #cell-partner="{ value }">
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
            {{ t(`orders.partners.${value}`) }}
          </span>
        </template>
      </AppTable>
    </DashboardWidget>
  </div>
</template>
