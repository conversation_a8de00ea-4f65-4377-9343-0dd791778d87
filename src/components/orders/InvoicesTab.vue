<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { ArrowPathIcon, ArrowDownTrayIcon, CreditCardIcon, BanknotesIcon } from '@heroicons/vue/24/outline';
import DashboardWidget from '../dashboard/DashboardWidget.vue';
import SearchFilter from '../ui/SearchFilter.vue';
import AppTable from '../ui/AppTable.vue';

const { t } = useI18n();
const route = useRoute();

interface InvoiceItem {
  id: string;
  medicationName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

interface Invoice {
  id: string;
  date: string;
  orderId: string;
  totalAmount: number;
  paymentMethod: string;
  status: 'paid' | 'unpaid' | 'canceled';
  pharmacyName: string;
  createdBy: string;
  currency: string;
  subtotal: number;
  discount: number;
  tax: number;
  items: InvoiceItem[];
  notes?: string;
  pdfUrl?: string;
}

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'refresh'): void;
  (e: 'download-invoice', invoice: Invoice): void;
  (e: 'view-invoice', invoice: Invoice): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// Search and filter state
const searchQuery = ref('');
const filters = ref({
  period: 'this-month',
  paymentMethod: 'all'
});

// Pagination state
const pagination = ref({
  currentPage: 1,
  itemsPerPage: 10,
  totalItems: 0
});

// Filter configuration
const filterConfig = {
  period: {
    label: 'Time Period',
    options: [
      { value: 'today', label: 'Today' },
      { value: 'this-week', label: 'This Week' },
      { value: 'this-month', label: 'This Month' },
      { value: 'custom', label: 'Custom Range' }
    ],
    multiSelect: false
  },
  paymentMethod: {
    label: 'Payment Method',
    options: [
      { value: 'all', label: 'All Methods' },
      { value: 'credit_card', label: 'Credit Card' },
      { value: 'cash', label: 'Cash' },
      { value: 'bank_transfer', label: 'Bank Transfer' }
    ],
    multiSelect: false
  }
};

// Mock data
const invoices = ref<Invoice[]>([
  {
    id: 'INV-001',
    date: new Date().toISOString().split('T')[0], // Today's date
    orderId: 'ORD-001',
    totalAmount: 250.50,
    paymentMethod: 'credit_card',
    status: 'paid',
    pharmacyName: 'Dawinii Pharmacy - Main Branch',
    createdBy: 'Ahmed Hassan',
    currency: 'EGP',
    subtotal: 230.00,
    discount: 15.00,
    tax: 35.50,
    items: [
      { id: '1', medicationName: 'Paracetamol 500mg', quantity: 2, unitPrice: 25.00, totalPrice: 50.00 },
      { id: '2', medicationName: 'Amoxicillin 250mg', quantity: 1, unitPrice: 180.00, totalPrice: 180.00 }
    ],
    notes: 'Customer requested express delivery',
    pdfUrl: '/invoices/inv-001.pdf'
  },
  {
    id: 'INV-002',
    date: new Date().toISOString().split('T')[0], // Today's date
    orderId: 'ORD-002',
    totalAmount: 180.25,
    paymentMethod: 'cash',
    status: 'unpaid',
    pharmacyName: 'Dawinii Pharmacy - Main Branch',
    createdBy: 'System',
    currency: 'EGP',
    subtotal: 165.00,
    discount: 0.00,
    tax: 15.25,
    items: [
      { id: '3', medicationName: 'Insulin Pen', quantity: 1, unitPrice: 165.00, totalPrice: 165.00 }
    ]
  },
  {
    id: 'INV-003',
    date: '2024-01-13',
    orderId: 'ORD-003',
    totalAmount: 320.75,
    paymentMethod: 'bank_transfer',
    status: 'paid',
    pharmacyName: 'Dawinii Pharmacy - Main Branch',
    createdBy: 'Sara Mohamed',
    currency: 'EGP',
    subtotal: 300.00,
    discount: 10.00,
    tax: 30.75,
    items: [
      { id: '4', medicationName: 'Blood Pressure Monitor', quantity: 1, unitPrice: 200.00, totalPrice: 200.00 },
      { id: '5', medicationName: 'Vitamin D3', quantity: 2, unitPrice: 50.00, totalPrice: 100.00 }
    ],
    notes: 'Bulk order discount applied',
    pdfUrl: '/invoices/inv-003.pdf'
  }
]);

const filteredInvoices = computed(() => {
  let filtered = invoices.value;

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(invoice =>
      invoice.id.toLowerCase().includes(query) ||
      invoice.orderId.toLowerCase().includes(query) ||
      invoice.totalAmount.toString().includes(query)
    );
  }

  // Apply period filter
  if (filters.value.period !== 'all') {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    filtered = filtered.filter(invoice => {
      const invoiceDate = new Date(invoice.date);

      switch (filters.value.period) {
        case 'today':
          return invoiceDate >= today;
        case 'this-week':
          const weekStart = new Date(today);
          weekStart.setDate(today.getDate() - today.getDay());
          return invoiceDate >= weekStart;
        case 'this-month':
          const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
          return invoiceDate >= monthStart;
        default:
          return true;
      }
    });
  }

  // Apply payment method filter
  if (filters.value.paymentMethod !== 'all') {
    filtered = filtered.filter(invoice => invoice.paymentMethod === filters.value.paymentMethod);
  }

  // Update total items for pagination
  pagination.value.totalItems = filtered.length;

  return filtered;
});

const paginatedInvoices = computed(() => {
  const filtered = filteredInvoices.value;
  if (!filtered || filtered.length === 0) {
    return [];
  }

  const start = (pagination.value.currentPage - 1) * pagination.value.itemsPerPage;
  const end = start + pagination.value.itemsPerPage;
  return filtered.slice(start, end);
});

// DataTable configuration
const tableColumns = computed(() => [
  {
    key: 'id',
    label: t('orders.invoiceId'),
    sortable: true,
    width: '120px',
    responsive: 'always' as const
  },
  {
    key: 'date',
    label: t('orders.date'),
    sortable: true,
    responsive: 'md' as const
  },
  {
    key: 'orderId',
    label: t('orders.orderId'),
    sortable: true,
    responsive: 'always' as const
  },
  {
    key: 'totalAmount',
    label: t('orders.totalAmount'),
    sortable: true,
    responsive: 'always' as const
  },
  {
    key: 'paymentMethod',
    label: t('orders.paymentMethod'),
    sortable: false,
    responsive: 'md' as const
  },
  {
    key: 'actions',
    label: t('common.actions'),
    sortable: false,
    width: '150px',
    responsive: 'always' as const
  }
]);

// Event handlers
const handleSearch = (search: string) => {
  console.log('Search:', search);
};

const handleFilterChange = (filterType: string, value: string | string[]) => {
  console.log('Filter change:', filterType, value);
};

const handleReset = () => {
  console.log('Reset filters');
};

const refreshData = () => {
  emit('refresh');
};

const downloadInvoice = (invoice: Invoice) => {
  emit('download-invoice', invoice);
};

const viewInvoice = (invoice: Invoice) => {
  emit('view-invoice', invoice);
};

const getPaymentMethodIcon = (method: string) => {
  const icons: Record<string, any> = {
    credit_card: CreditCardIcon,
    cash: BanknotesIcon,
    bank_transfer: BanknotesIcon,
    online: CreditCardIcon
  };
  return icons[method] || BanknotesIcon;
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-EG', {
    style: 'currency',
    currency: 'EGP'
  }).format(amount);
};

const formatDate = (date: string | undefined | null) => {
  if (!date) return '-';
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '-';
    return dateObj.toLocaleDateString();
  } catch (error) {
    console.warn('Invalid date format:', date);
    return '-';
  }
};

// Handle table events
const handleTableSort = (column: string, order: 'asc' | 'desc') => {
  console.log('Sort:', column, order);
  // Implement sorting logic here
};

const handleTableRowClick = (row: any) => {
  viewInvoice(row);
};

// Apply URL parameters on mount
const applyUrlParams = () => {
  try {
    // Check for section parameter to determine what filter to apply
    const sectionParam = route.query.section as string;

    if (sectionParam === 'invoices-today') {
      // Filter to show today's invoices
      filters.value.period = 'today';
    } else if (sectionParam === 'revenue') {
      // For revenue, we might want to show this month's invoices
      filters.value.period = 'this-month';
    }
  } catch (error) {
    console.error('Error applying URL parameters:', error);
  }
};

// Initialize component
onMounted(() => {
  applyUrlParams();
});
</script>

<template>
  <div class="w-full">
    <!-- Search and Filter Controls -->
    <div class="flex items-center gap-4 mb-6">
      <div class="flex-1">
        <SearchFilter v-model:search="searchQuery" v-model:filters="filters" :filter-config="filterConfig"
          :placeholder="t('orders.searchInvoicesPlaceholder')" :loading="loading" @search="handleSearch"
          @filter-change="handleFilterChange" @reset="handleReset" />
      </div>
      <button
        class="flex items-center px-5 py-2.5 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-800 dark:text-gray-100 font-semibold cursor-pointer transition-all duration-300 hover:bg-gray-100 dark:hover:bg-slate-600 hover:-translate-y-0.5 hover:shadow-md whitespace-nowrap"
        @click="refreshData">
        <ArrowPathIcon class="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2 text-primary-600 dark:text-primary-400" />
        {{ t('common.refresh') }}
      </button>
    </div>

    <!-- Invoices table -->
    <DashboardWidget :title="t('orders.invoices')" id="invoices-table" :full-width="true">
      <AppTable :columns="tableColumns" :data="paginatedInvoices" :loading="loading"
        :current-page="pagination.currentPage" :total-items="pagination.totalItems"
        :items-per-page="pagination.itemsPerPage" :show-pagination="true" :empty-message="t('orders.noInvoicesFound')"
        @sort="handleTableSort" @row-click="handleTableRowClick" @update:current-page="pagination.currentPage = $event"
        @update:items-per-page="pagination.itemsPerPage = $event">
        <!-- Custom cell templates -->
        <template #cell-id="{ value }">
          <span class="font-mono font-semibold text-primary-600 dark:text-primary-400">{{ value }}</span>
        </template>

        <template #cell-date="{ value }">
          <span class="text-sm text-gray-600 dark:text-gray-400">{{ formatDate(value) }}</span>
        </template>

        <template #cell-orderId="{ value }">
          <span class="font-mono text-gray-900 dark:text-white">{{ value }}</span>
        </template>

        <template #cell-totalAmount="{ value }">
          <span class="font-semibold text-gray-900 dark:text-white">{{ formatCurrency(value) }}</span>
        </template>

        <template #cell-paymentMethod="{ value }">
          <div class="flex items-center gap-2">
            <component :is="getPaymentMethodIcon(value)" class="w-5 h-5 text-primary-600 dark:text-primary-400" />
            <span>{{value.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}}</span>
          </div>
        </template>

        <template #cell-actions="{ row }">
          <button
            class="inline-flex items-center px-3 py-1.5 bg-primary-600 hover:bg-primary-700 text-white border-none rounded-lg font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 hover:shadow-md text-sm gap-2"
            @click.stop="downloadInvoice(row)">
            <ArrowDownTrayIcon class="w-4 h-4" />
            <span>Download</span>
          </button>
        </template>
      </AppTable>
    </DashboardWidget>
  </div>
</template>
