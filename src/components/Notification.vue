<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { CheckCircleIcon, ExclamationCircleIcon, InformationCircleIcon } from '@heroicons/vue/24/outline';

// Define props
const props = defineProps<{
  message: string;
  type?: 'success' | 'error' | 'info';
  duration?: number;
  show: boolean;
}>();

// Define emits
const emit = defineEmits(['close']);

// Reactive state
const isVisible = ref(props.show);
const timer = ref<number | null>(null);

// Watch for show prop changes
watch(() => props.show, (newValue) => {
  isVisible.value = newValue;
  if (newValue && props.duration) {
    startTimer();
  }
});

// Start auto-hide timer
const startTimer = () => {
  if (timer.value) {
    clearTimeout(timer.value);
  }

  if (props.duration) {
    timer.value = window.setTimeout(() => {
      isVisible.value = false;
      emit('close');
    }, props.duration);
  }
};

// Handle close
const handleClose = () => {
  isVisible.value = false;
  emit('close');
};

// Initialize timer on component mount
onMounted(() => {
  if (props.show && props.duration) {
    startTimer();
  }
});

// Clean up timer on component unmount
onUnmounted(() => {
  if (timer.value) {
    clearTimeout(timer.value);
    timer.value = null;
  }
});

// Computed icon component based on type
const iconComponent = () => {
  switch (props.type) {
    case 'success':
      return CheckCircleIcon;
    case 'error':
      return ExclamationCircleIcon;
    case 'info':
    default:
      return InformationCircleIcon;
  }
};

// Computed classes based on type
const notificationClasses = () => {
  const baseClasses = 'fixed bottom-8 left-1/2 transform -translate-x-1/2 flex items-center px-6 py-4 rounded-lg shadow-lg z-[9999] max-w-md w-auto min-w-80 text-start';

  switch (props.type) {
    case 'success':
      return `${baseClasses} bg-success-600 text-white border border-success-700`;
    case 'error':
      return `${baseClasses} bg-error-600 text-white border border-error-700`;
    case 'info':
    default:
      return `${baseClasses} bg-teal-600 text-white border border-teal-700`;
  }
};
</script>

<template>
  <transition enter-active-class="transition-all duration-300 ease-out"
    enter-from-class="opacity-0 transform translate-y-4" enter-to-class="opacity-100 transform translate-y-0"
    leave-active-class="transition-all duration-300 ease-in" leave-from-class="opacity-100 transform translate-y-0"
    leave-to-class="opacity-0 transform translate-y-4">
    <div v-if="isVisible" :class="notificationClasses()" role="alert">
      <div class="flex-shrink-0 mr-3 rtl:mr-0 rtl:ml-3">
        <component :is="iconComponent()" class="w-6 h-6" aria-hidden="true" />
      </div>
      <div class="flex-1 min-w-0">
        <p class="text-sm font-medium leading-5">{{ message }}</p>
      </div>
    </div>
  </transition>
</template>
