<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import BaseCard from '../ui/BaseCard.vue';
import StarRating from './StarRating.vue';
import SkeletonLoader from '../ui/SkeletonLoader.vue';
import type { RatingSummary } from '../../types/reviews';

interface Props {
  summary: RatingSummary;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const { t } = useI18n();

// Calculate percentage for each star rating
const starPercentages = computed(() => {
  const total = props.summary.totalReviews;
  if (total === 0) return { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
  
  return {
    5: Math.round((props.summary.starBreakdown[5] / total) * 100),
    4: Math.round((props.summary.starBreakdown[4] / total) * 100),
    3: Math.round((props.summary.starBreakdown[3] / total) * 100),
    2: Math.round((props.summary.starBreakdown[2] / total) * 100),
    1: Math.round((props.summary.starBreakdown[1] / total) * 100)
  };
});

// Format average rating
const formattedAverageRating = computed(() => {
  return props.summary.averageRating.toFixed(1);
});
</script>

<template>
  <BaseCard
    variant="default"
    padding="lg"
    class="mb-6"
  >
    <!-- Loading State -->
    <div v-if="loading">
      <SkeletonLoader class="w-full">
        <!-- Header skeleton -->
        <div class="text-center mb-6">
          <div class="w-48 h-8 bg-gray-200 dark:bg-gray-700 rounded mx-auto mb-4"></div>
          <!-- Rating display skeleton -->
          <div class="flex items-center justify-center gap-3 mb-2">
            <div class="w-16 h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="flex gap-1">
              <div v-for="i in 5" :key="i" class="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
          <!-- Total count skeleton -->
          <div class="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded mx-auto"></div>
        </div>
        <!-- Breakdown skeleton -->
        <div class="space-y-3">
          <div class="w-40 h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div v-for="i in 5" :key="i" class="flex items-center gap-3">
            <div class="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="w-8 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </SkeletonLoader>
    </div>

    <!-- Content -->
    <div v-else>
      <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          {{ t('reviews.averageRating') }}
        </h2>

        <!-- Large Average Rating Display -->
        <div class="flex items-center justify-center gap-3 mb-2">
          <span class="text-4xl font-bold text-gray-900 dark:text-gray-100">
            {{ formattedAverageRating }}
          </span>
          <StarRating
            :rating="summary.averageRating"
            size="lg"
            color="yellow"
          />
        </div>

        <!-- Total Reviews Count -->
        <p class="text-gray-600 dark:text-gray-400">
          {{ t('reviews.totalReviews') }}: {{ summary.totalReviews }}
        </p>
      </div>

      <!-- Star Breakdown -->
      <div class="space-y-3">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 text-start rtl:text-right">
          {{ t('reviews.starBreakdown') }}
        </h3>
      
      <!-- 5 Stars -->
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-1 w-20 justify-start rtl:justify-end">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">5</span>
          <StarRating :rating="5" :max-rating="1" size="sm" color="yellow" />
        </div>
        <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="bg-yellow-400 h-2 rounded-full transition-all duration-500"
            :style="{ width: `${starPercentages[5]}%` }"
          ></div>
        </div>
        <div class="w-12 text-right rtl:text-left">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ summary.starBreakdown[5] }}
          </span>
        </div>
      </div>

      <!-- 4 Stars -->
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-1 w-20 justify-start rtl:justify-end">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">4</span>
          <StarRating :rating="4" :max-rating="1" size="sm" color="yellow" />
        </div>
        <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="bg-yellow-400 h-2 rounded-full transition-all duration-500"
            :style="{ width: `${starPercentages[4]}%` }"
          ></div>
        </div>
        <div class="w-12 text-right rtl:text-left">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ summary.starBreakdown[4] }}
          </span>
        </div>
      </div>

      <!-- 3 Stars -->
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-1 w-20 justify-start rtl:justify-end">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">3</span>
          <StarRating :rating="3" :max-rating="1" size="sm" color="yellow" />
        </div>
        <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="bg-yellow-400 h-2 rounded-full transition-all duration-500"
            :style="{ width: `${starPercentages[3]}%` }"
          ></div>
        </div>
        <div class="w-12 text-right rtl:text-left">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ summary.starBreakdown[3] }}
          </span>
        </div>
      </div>

      <!-- 2 Stars -->
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-1 w-20 justify-start rtl:justify-end">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">2</span>
          <StarRating :rating="2" :max-rating="1" size="sm" color="yellow" />
        </div>
        <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="bg-yellow-400 h-2 rounded-full transition-all duration-500"
            :style="{ width: `${starPercentages[2]}%` }"
          ></div>
        </div>
        <div class="w-12 text-right rtl:text-left">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ summary.starBreakdown[2] }}
          </span>
        </div>
      </div>

      <!-- 1 Star -->
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-1 w-20 justify-start rtl:justify-end">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">1</span>
          <StarRating :rating="1" :max-rating="1" size="sm" color="yellow" />
        </div>
        <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="bg-yellow-400 h-2 rounded-full transition-all duration-500"
            :style="{ width: `${starPercentages[1]}%` }"
          ></div>
        </div>
        <div class="w-12 text-right rtl:text-left">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ summary.starBreakdown[1] }}
          </span>
        </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
