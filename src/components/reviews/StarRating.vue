<script setup lang="ts">
import { computed } from 'vue';
import { StarIcon } from '@heroicons/vue/24/solid';
import { StarIcon as StarOutlineIcon } from '@heroicons/vue/24/outline';

interface Props {
  rating: number; // Rating value (0-5, can be decimal like 4.7)
  maxRating?: number; // Maximum rating (default: 5)
  size?: 'sm' | 'md' | 'lg' | 'xl'; // Size of stars
  showValue?: boolean; // Whether to show the numeric value
  readonly?: boolean; // Whether the rating is read-only
  color?: 'yellow' | 'orange' | 'red' | 'green' | 'blue'; // Star color
}

const props = withDefaults(defineProps<Props>(), {
  maxRating: 5,
  size: 'md',
  showValue: false,
  readonly: true,
  color: 'yellow'
});

// Size classes for stars
const sizeClasses = computed(() => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  };
  return sizes[props.size];
});

// Color classes for stars
const colorClasses = computed(() => {
  const colors = {
    yellow: 'text-yellow-400',
    orange: 'text-orange-400',
    red: 'text-red-400',
    green: 'text-green-400',
    blue: 'text-blue-400'
  };
  return colors[props.color];
});

// Generate array of stars with their fill states
const stars = computed(() => {
  const starArray = [];
  for (let i = 1; i <= props.maxRating; i++) {
    const filled = i <= Math.floor(props.rating);
    const halfFilled = i === Math.ceil(props.rating) && props.rating % 1 !== 0;
    starArray.push({
      index: i,
      filled,
      halfFilled,
      empty: !filled && !halfFilled
    });
  }
  return starArray;
});

// Format rating value for display
const formattedRating = computed(() => {
  return props.rating % 1 === 0 ? props.rating.toString() : props.rating.toFixed(1);
});
</script>

<template>
  <div class="flex items-center gap-1">
    <!-- Stars -->
    <div class="flex items-center gap-0.5">
      <template v-for="star in stars" :key="star.index">
        <!-- Filled star -->
        <StarIcon
          v-if="star.filled"
          :class="[sizeClasses, colorClasses]"
          aria-hidden="true"
        />
        <!-- Half-filled star (using outline with partial fill) -->
        <div
          v-else-if="star.halfFilled"
          class="relative"
          :class="sizeClasses"
        >
          <StarOutlineIcon
            :class="[sizeClasses, 'text-gray-300 dark:text-gray-600 absolute']"
            aria-hidden="true"
          />
          <div class="overflow-hidden" style="width: 50%">
            <StarIcon
              :class="[sizeClasses, colorClasses]"
              aria-hidden="true"
            />
          </div>
        </div>
        <!-- Empty star -->
        <StarOutlineIcon
          v-else
          :class="[sizeClasses, 'text-gray-300 dark:text-gray-600']"
          aria-hidden="true"
        />
      </template>
    </div>

    <!-- Rating value -->
    <span
      v-if="showValue"
      class="text-sm font-medium text-gray-700 dark:text-gray-300 ml-2 rtl:ml-0 rtl:mr-2"
    >
      {{ formattedRating }}
    </span>

    <!-- Screen reader text -->
    <span class="sr-only">
      {{ formattedRating }} out of {{ maxRating }} stars
    </span>
  </div>
</template>
