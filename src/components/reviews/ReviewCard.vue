<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { UserIcon, CheckBadgeIcon } from '@heroicons/vue/24/outline';
import BaseCard from '../ui/BaseCard.vue';
import StarRating from './StarRating.vue';
import type { Review } from '../../types/reviews';

interface Props {
  review: Review;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const { t, d } = useI18n();

// Format the review date
const formattedDate = computed(() => {
  try {
    const date = new Date(props.review.date);
    return d(date, 'short');
  } catch (error) {
    return props.review.date;
  }
});

// Get customer display name
const customerDisplayName = computed(() => {
  return props.review.customerName || t('reviews.anonymous');
});


</script>

<template>
  <BaseCard variant="default" padding="md" hover :loading="loading" class="transition-all duration-200 hover:shadow-md">
    <!-- Review Header -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex items-center gap-3">
        <!-- Customer Avatar -->
        <div class="flex items-center justify-center w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-full">
          <UserIcon class="w-5 h-5 text-gray-500 dark:text-gray-400" />
        </div>

        <!-- Customer Info -->
        <div class="flex flex-col">
          <div class="flex items-center gap-2">
            <span class="font-medium text-gray-900 dark:text-gray-100">
              {{ customerDisplayName }}
            </span>
            <!-- Verified badge -->
            <CheckBadgeIcon v-if="review.verified" class="w-4 h-4 text-green-500"
              :title="t('reviews.verifiedPurchase')" />
          </div>
          <span class="text-sm text-gray-500 dark:text-gray-400">
            {{ formattedDate }}
          </span>
        </div>
      </div>

      <!-- Star Rating -->
      <StarRating :rating="review.rating" size="sm" color="yellow" />
    </div>



    <!-- Review Message -->
    <div class="mb-0">
      <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-start rtl:text-right">
        {{ review.message }}
      </p>
    </div>

    <!-- Review Footer (if needed for additional info) -->
    <div v-if="review.orderId" class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
      <span class="text-xs text-gray-500 dark:text-gray-400">
        {{ t('reviews.orderId') }}: {{ review.orderId }}
      </span>
    </div>
  </BaseCard>
</template>
