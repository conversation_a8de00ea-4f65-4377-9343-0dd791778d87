<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import ReviewCard from './ReviewCard.vue';
import SkeletonLoader from '../ui/SkeletonLoader.vue';
import type { Review } from '../../types/reviews';

interface Props {
  reviews: Review[];
  loading?: boolean;
  error?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null
});

const { t } = useI18n();

// Check if there are reviews to display
const hasReviews = computed(() => {
  return props.reviews && props.reviews.length > 0;
});

// Generate skeleton items for loading state
const skeletonItems = computed(() => {
  return Array.from({ length: 6 }, (_, index) => ({ id: index }));
});
</script>

<template>
  <div class="w-full">
    <!-- Loading State -->
    <div v-if="loading" class="space-y-4">
      <div
        v-for="item in skeletonItems"
        :key="item.id"
        class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg p-6"
      >
        <SkeletonLoader class="w-full">
          <!-- Review card skeleton -->
          <div class="flex items-start gap-3 mb-4">
            <!-- Avatar skeleton -->
            <div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            <div class="flex-1">
              <!-- Name and date skeleton -->
              <div class="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div class="w-20 h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
            <!-- Rating skeleton -->
            <div class="flex gap-1">
              <div v-for="i in 5" :key="i" class="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
          <!-- Message skeleton -->
          <div class="space-y-2">
            <div class="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="w-3/4 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="w-1/2 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </SkeletonLoader>
      </div>
    </div>

    <!-- Error State -->
    <div
      v-else-if="error"
      class="text-center py-12"
    >
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
        <p class="text-red-800 dark:text-red-200 font-medium mb-2">
          {{ t('common.error') }}
        </p>
        <p class="text-red-600 dark:text-red-300 text-sm">
          {{ error }}
        </p>
      </div>
    </div>

    <!-- Empty State -->
    <div
      v-else-if="!hasReviews"
      class="text-center py-12"
    >
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-8">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.196-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          {{ t('reviews.noReviews') }}
        </h3>
        <p class="text-gray-500 dark:text-gray-400">
          {{ t('reviews.noReviewsDescription', 'Customer reviews will appear here once they start rating your pharmacy.') }}
        </p>
      </div>
    </div>

    <!-- Reviews List -->
    <div
      v-else
      class="space-y-4"
    >
      <ReviewCard
        v-for="review in reviews"
        :key="review.id"
        :review="review"
      />
    </div>
  </div>
</template>
