<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  ArchiveBoxIcon,
  ChatBubbleLeftRightIcon,
  TruckIcon,
  ShieldCheckIcon,
  HandThumbUpIcon
} from '@heroicons/vue/24/outline';
import BaseCard from '../ui/BaseCard.vue';
import SkeletonLoader from '../ui/SkeletonLoader.vue';

interface Props {
  loading?: boolean;
}

withDefaults(defineProps<Props>(), {
  loading: false
});

const { t } = useI18n();

// Tips data with icons and localized content
const tips = computed(() => [
  {
    id: 1,
    icon: ArchiveBoxIcon,
    text: t('reviews.tips.keepStockUpdated'),
    color: 'text-primary-500 dark:text-primary-400'
  },
  {
    id: 2,
    icon: ChatBubbleLeftRightIcon,
    text: t('reviews.tips.politeCommunication'),
    color: 'text-success-500 dark:text-success-400'
  },
  {
    id: 3,
    icon: TruckIcon,
    text: t('reviews.tips.fastDelivery'),
    color: 'text-primary-600 dark:text-primary-300'
  },
  {
    id: 4,
    icon: ShieldCheckIcon,
    text: t('reviews.tips.qualityMedications'),
    color: 'text-warning-500 dark:text-warning-400'
  },
  {
    id: 5,
    icon: HandThumbUpIcon,
    text: t('reviews.tips.respondToFeedback'),
    color: 'text-primary-700 dark:text-primary-200'
  }
]);
</script>

<template>
  <BaseCard variant="default" padding="lg" class="mb-6">
    <!-- Loading State -->
    <div v-if="loading">
      <SkeletonLoader class="w-full">
        <!-- Header skeleton -->
        <div class="w-48 h-6 bg-gray-200 dark:bg-gray-700 rounded mb-6"></div>
        <!-- Tips skeleton -->
        <div class="space-y-4">
          <div v-for="i in 5" :key="i" class="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <!-- Icon skeleton -->
            <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg flex-shrink-0 mt-0.5"></div>
            <!-- Text skeleton -->
            <div class="flex-1 space-y-2">
              <div class="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="w-3/4 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
        <!-- Note skeleton -->
        <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="space-y-2">
            <div class="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="w-2/3 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </SkeletonLoader>
    </div>

    <!-- Content -->
    <div v-else>
      <!-- Tips Header -->
      <div class="mb-6">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 text-start rtl:text-right">
          {{ t('reviews.tips.title') }}
        </h3>
      </div>

      <!-- Tips List -->
      <div class="space-y-4">
        <div v-for="tip in tips" :key="tip.id"
          class="flex items-start gap-4 p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg transition-all duration-200 hover:bg-primary-100 dark:hover:bg-primary-900/30">
          <!-- Tip Icon -->
          <div class="flex-shrink-0 mt-0.5">
            <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-white dark:bg-gray-700 shadow-sm">
              <component :is="tip.icon" class="w-5 h-5" :class="tip.color" />
            </div>
          </div>

          <!-- Tip Text -->
          <div class="flex-1 min-w-0">
            <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-start rtl:text-right">
              {{ tip.text }}
            </p>
          </div>
        </div>

        <!-- Additional Note -->
        <div
          class="mt-6 p-4 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg">
          <p class="text-sm text-primary-800 dark:text-primary-200 text-start rtl:text-right">
            <strong>{{ t('common.note') }}:</strong>
            {{ t('reviews.tips.improvementNote') }}
          </p>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
