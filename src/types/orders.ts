/**
 * Orders and Invoices related types
 */

// Order status
export type OrderStatus = 'delivered' | 'pending' | 'canceled';

// Delivery order status
export type DeliveryStatus = 'pending' | 'out_for_delivery' | 'delivered';

// Delivery partner
export type DeliveryPartner = 'talabat' | 'careem' | 'pharmacy_driver' | 'other';

// Payment method
export type PaymentMethod = 'credit' | 'cash' | 'bank-transfer' | 'wallet';

// Reservation source
export type ReservationSource = 'app' | 'phone' | 'in_person' | 'website';

// Order interface
export interface Order {
  id: string;
  customerName: string;
  totalAmount: number;
  date: string; // ISO date string format
  status: OrderStatus;
  items?: OrderItem[];
  pharmacyName?: string;
  paymentMethod?: PaymentMethod;
  paymentStatus?: 'paid' | 'pending' | 'failed' | 'refunded';
  createdBy?: string;
  notes?: string;
}

// Order item interface
export interface OrderItem {
  id: number;
  medicationId: number;
  medicationName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

// Invoice interface
export interface Invoice {
  id: string;
  orderId: string;
  date: string; // ISO date string format
  totalAmount: number;
  paymentMethod: PaymentMethod;
  isPaid: boolean;
  pdfUrl?: string; // URL to download PDF
}

// Top selling medication interface
export interface TopSellingMedication {
  id: number;
  name: string;
  category: string;
  unitsSold: number;
  totalSales: number;
}

// Delivery order interface
export interface DeliveryOrder {
  id: string;
  orderId: string;
  customerName: string;
  address: string;
  itemsCount: number;
  deliveryTime: string; // ISO date string format
  status: DeliveryStatus;
  partner: DeliveryPartner;
  contactNumber?: string;
  notes?: string;
  date: string; // ISO date string format
  pharmacyName?: string;
  paymentMethod?: PaymentMethod;
  paymentStatus?: 'paid' | 'pending' | 'failed' | 'refunded';
  shippingMethod?: 'standard' | 'express' | 'pickup' | 'same_day';
  createdBy?: string;
  totalAmount?: number;
}

// Reserved medication interface
export interface ReservedMedication {
  id: number;
  medicationId: number;
  medicationName: string;
  quantityReserved: number;
  reservedBy: string;
  source: ReservationSource;
  reservationDate: string; // ISO date string format
  expiryDate: string; // ISO date string format
  status: 'active' | 'expired';
}

// Revenue summary interface
export interface RevenueSummary {
  totalRevenue: number;
  numberOfOrders: number;
  averageOrderValue: number;
}

// Filter periods for orders
export type OrderFilterPeriod = 'this-month' | 'last-6-months' | 'last-year' | 'last-5-years';

// Filter periods for invoices and revenue
export type InvoiceFilterPeriod = 'today' | 'this-week' | 'this-month' | 'custom';

// Filter periods for delivery orders
export type DeliveryFilterPeriod = 'today' | 'last-7-days' | 'this-month' | 'all';

// Filter for delivery status
export type DeliveryStatusFilter = 'all' | DeliveryStatus;

// Filter for delivery partner
export type DeliveryPartnerFilter = 'all' | DeliveryPartner;

// Filter for reserved medications
export type ReservationStatusFilter = 'all' | 'active' | 'expired';

// Filter for reservation source
export type ReservationSourceFilter = 'all' | ReservationSource;

// Custom date range
export interface DateRange {
  startDate: string; // ISO date string format
  endDate: string; // ISO date string format
}

// Sort options for top selling medications
export type TopSellingSort = 'most-sold' | 'highest-revenue';

// Orders and Invoices view state
export interface OrdersViewState {
  orders: Order[];
  invoices: Invoice[];
  topSellingMedications: TopSellingMedication[];
  deliveryOrders: DeliveryOrder[];
  reservedMedications: ReservedMedication[];
  revenueSummary: RevenueSummary;
  orderFilter: OrderFilterPeriod;
  invoiceFilter: InvoiceFilterPeriod;
  revenueFilter: InvoiceFilterPeriod;
  deliveryFilter: DeliveryFilterPeriod;
  deliveryStatusFilter: DeliveryStatusFilter;
  deliveryPartnerFilter: DeliveryPartnerFilter;
  reservationStatusFilter: ReservationStatusFilter;
  reservationSourceFilter: ReservationSourceFilter;
  customDateRange?: DateRange;
  topSellingSort: TopSellingSort;
  activeTab: 'orders' | 'invoices' | 'revenue' | 'top-selling' | 'delivery' | 'reserved';
  isLoading: boolean;
  error: string | null;
}
