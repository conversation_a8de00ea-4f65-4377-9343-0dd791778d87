// Shared table types for AppTable component

export interface AppTableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  responsive?: 'always' | 'md' | 'lg' | 'xl'; // When to show this column
  render?: (value: any, row: any) => string; // Custom render function
}

export interface AppTablePagination {
  page: number;
  pageSize: number;
  totalItems: number;
  limit?: number;
  totalPages?: number;
}

export interface AppTableSort {
  field: string;
  direction: 'asc' | 'desc';
}
