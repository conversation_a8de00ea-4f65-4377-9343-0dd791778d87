/**
 * Order Request related types for incoming pharmacy orders
 */

// Service type for order requests
export type OrderRequestServiceType = 'Reserved' | 'Delivery';

// Medication status in order requests
export type OrderRequestMedicationStatus = 'available' | 'out_of_stock' | 'locked' | 'low_stock';

// Order request status
export type OrderRequestStatus = 'pending' | 'accepted' | 'rejected' | 'expired' | 'cancelled';

// Order request medication interface
export interface OrderRequestMedication {
  id: number;
  medicationId: number;
  name: string;
  quantity: number;
  unitPrice?: number;
  totalPrice?: number;
  status: OrderRequestMedicationStatus;
  lockedSince?: string; // ISO date string - when the medication was locked
  lockDurationHours?: number; // How long it's been locked
}

// Order request interface
export interface OrderRequest {
  id: string;
  userId: string;
  userName: string;
  userPhone: string;
  serviceType: OrderRequestServiceType;
  location: {
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  pointsToEarn: number;
  orderTime: string; // ISO date string
  createdAt: string; // ISO date string
  status: OrderRequestStatus;
  medications: OrderRequestMedication[];
  totalAmount?: number;
  notes?: string;
  estimatedDeliveryTime?: string; // For delivery orders
  reservationExpiryTime?: string; // For reserved orders
}

// Order request action types
export type OrderRequestAction = 'accept' | 'reject' | 'release';

// Order request action result
export interface OrderRequestActionResult {
  success: boolean;
  message?: string;
  error?: string;
}

// Order request notification data
export interface OrderRequestNotification {
  id: string;
  orderRequestId: string;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: 'high' | 'medium' | 'low';
}

// Order request service response
export interface OrderRequestServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// Order request filters for management
export interface OrderRequestFilters {
  status?: OrderRequestStatus[];
  serviceType?: OrderRequestServiceType[];
  dateRange?: {
    startDate: string;
    endDate: string;
  };
}
