/**
 * Store-related type definitions
 */

export interface StoreLocation {
  address: string;
  city: string;
  region: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface WorkingHours {
  open: string;
  close: string;
  isOpen24Hours?: boolean;
}

export interface StoreContact {
  phone?: string;
  email?: string;
  website?: string;
}

export interface Store {
  id: string;
  name: string;
  location: StoreLocation;
  storeType: 'main' | 'branch' | 'partner';
  contact?: StoreContact;
  workingHours?: WorkingHours;
  deliverySupport: boolean;
  isOpen?: boolean;
  description?: string;
  imageUrl?: string;
  verified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StoreInventoryItem {
  id: string;
  medicationId: string;
  name: string;
  category: string;
  quantity: number;
  pricePerUnit: number;
  expiryDate: string;
  status: 'in-stock' | 'low-stock' | 'out-of-stock';
  description?: string;
  manufacturer?: string;
  batchNumber?: string;
}

export interface StoreFilters {
  city?: string;
  storeType?: string;
  availability?: string;
  openNow?: boolean;
  deliverySupport?: boolean;
}

export interface StoreSearch {
  query: string;
  filters: StoreFilters;
}

export interface StorePagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface StoreSort {
  field: 'name' | 'distance' | 'createdAt';
  direction: 'asc' | 'desc';
}

export interface StoreOrderRequest {
  storeId: string;
  medications: {
    medicationId: string;
    quantity: number;
    notes?: string;
  }[];
  deliveryAddress?: string;
  notes?: string;
  priority: 'low' | 'medium' | 'high';
}

export interface StoreServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface StoresListResponse {
  stores: Store[];
  pagination: StorePagination;
  filters: StoreFilters;
}

export interface StoreInventoryResponse {
  storeId: string;
  storeName: string;
  inventory: StoreInventoryItem[];
  pagination: StorePagination;
}
