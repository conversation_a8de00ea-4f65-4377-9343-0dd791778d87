/**
 * Dashboard data types
 */

export interface User {
    userId: string;
    role: 'PharmacyOwner' | 'PharmacyUser';
    email?: string;
    deviceId?: string;
    token: string;
}

export interface InventoryData {
    totalMedications: number;
    outOfStock: number;
    expiringSoon: number;
    recentlyAdded: number;
    lowStock: number;
}

export interface TopSellingItem {
    name: string;
    count: number;
    revenue: number;
}

export interface SalesData {
    todayInvoices: number;
    todayRevenue: number;
    topSelling: TopSellingItem[];
}



export interface DeliveryData {
    new: number;
    inTransit: number;
    completed: number;
}

export interface AppOrdersData {
    totalUsers: number;
    activeUsers: number;
    totalOrders: number;
    averageOrderValue: number;
    pendingReviews: number;
}

export interface StoreOrdersData {
    pending: number;
    inTransit: number;
    completed: number;
    rejected: number;
}

export interface RevenueDataPoint {
    month: string;
    revenue: number;
}

export interface DashboardData {
    inventory: InventoryData;
    sales: SalesData;
    delivery: DeliveryData;
    appOrders: AppOrdersData;
    storeOrders: StoreOrdersData;
    monthlyRevenue: RevenueDataPoint[];
}

export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
}