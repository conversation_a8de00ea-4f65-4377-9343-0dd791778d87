/**
 * Reviews and ratings related types
 */

// Review interface
export interface Review {
  id: string;
  customerName: string | null; // null for anonymous reviews
  rating: 1 | 2 | 3 | 4 | 5; // Star rating from 1 to 5
  message: string;
  date: string; // ISO date string format
  medicationName?: string; // Optional related medication
  medicationId?: number; // Optional related medication ID
  orderId?: string; // Optional related order ID
  verified?: boolean; // Whether the review is from a verified purchase
}

// Rating summary interface
export interface RatingSummary {
  averageRating: number; // Average rating (e.g., 4.7)
  totalReviews: number; // Total number of reviews
  starBreakdown: {
    5: number; // Number of 5-star reviews
    4: number; // Number of 4-star reviews
    3: number; // Number of 3-star reviews
    2: number; // Number of 2-star reviews
    1: number; // Number of 1-star reviews
  };
}

// Reviews view state
export interface ReviewsViewState {
  reviews: Review[];
  ratingSummary: RatingSummary;
  isLoading: boolean;
  error: string | null;
}

// Filter options for reviews
export interface ReviewsFilter {
  rating: 'all' | 1 | 2 | 3 | 4 | 5; // Filter by star rating
  period: 'all' | 'today' | 'this-week' | 'this-month' | 'this-year'; // Filter by time period
  verified: 'all' | 'verified' | 'unverified'; // Filter by verification status
}

// Search parameters for reviews
export interface ReviewsSearch {
  query: string; // Search in customer name, message, or medication name
}

// Sort options for reviews
export type ReviewsSort = 'newest' | 'oldest' | 'highest-rating' | 'lowest-rating';

// Reviews service response
export interface ReviewsResponse {
  reviews: Review[];
  ratingSummary: RatingSummary;
  totalCount: number;
  hasMore: boolean;
}
