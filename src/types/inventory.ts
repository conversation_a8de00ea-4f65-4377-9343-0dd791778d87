/**
 * Inventory and stock related types
 */

// Status of a medication item
export type StockStatus = 'in-stock' | 'low-stock' | 'out-of-stock';

// Expiry status of a medication
export type ExpiryStatus = 'valid' | 'expiring-soon' | 'expired';

// Medication item interface
export interface MedicationItem {
  id: number;
  name: string;
  category: string;
  quantity: number;
  expiryDate: string; // ISO date string format
  addedDate?: string; // ISO date string format for when the medication was added
  pricePerUnit: number;
  status: StockStatus;
  expiryStatus: ExpiryStatus;
}

// Filter options for the stock view
export interface StockFilter {
  status: 'all' | StockStatus;
  expiryStatus: 'all' | ExpiryStatus;
}

// Search parameters
export interface StockSearch {
  query: string;
}

// Pagination parameters
export interface StockPagination {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
}

// Sort parameters
export interface StockSort {
  field: keyof MedicationItem | null;
  direction: 'asc' | 'desc';
}

// Stock view state
export interface StockViewState {
  medications: MedicationItem[];
  filteredMedications: MedicationItem[];
  filter: StockFilter;
  search: StockSearch;
  pagination: StockPagination;
  sort: StockSort;
  isLoading: boolean;
  error: string | null;
}
