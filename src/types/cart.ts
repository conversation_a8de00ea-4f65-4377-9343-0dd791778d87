/**
 * Types for the Cart functionality
 */

import type { MedicationType } from './medicationForm';
import type { OrderRequestServiceType } from './orderRequests';

// Cart item interface
export interface CartItem {
  id: number;
  name: string;
  barcode?: string;
  type: MedicationType;
  quantity: number;
  pricePerUnit: number;
  discount?: {
    hasDiscount: boolean;
    percentage?: number;
    validUntil?: string;
  };
  totalPrice: number;
}

// Detailed medication item interface for the modal
export interface DetailedMedicationItem {
  id: number;
  name: string;
  barcode?: string;
  category: string;
  type: MedicationType;
  description?: string;
  quantity: number;
  expiryDate: string;
  pricePerUnit: number;
  purchasePrice?: number;
  discount?: {
    hasDiscount: boolean;
    percentage?: number;
    validUntil?: string;
  };
  supplier: string;
  storageLocation: string;
}

// Order pack type
export type OrderPackType = 'auto-accepted' | 'local' | 'manual';

// Order pack status
export type OrderPackStatus = 'active' | 'completed' | 'cancelled' | 'expired';

// Order pack interface
export interface OrderPack {
  id: string;
  type: OrderPackType;
  status: OrderPackStatus;
  items: CartItem[];
  subtotal: number;
  tax: number;
  total: number;
  createdAt: string;
  updatedAt: string;

  // User information (for auto-accepted orders)
  userInfo?: {
    userId: string;
    userName: string;
    userPhone: string;
    serviceType: OrderRequestServiceType;
    location?: {
      address: string;
      coordinates?: {
        latitude: number;
        longitude: number;
      };
    };
    pointsToEarn?: number;
    orderRequestId?: string;
  };

  // Timer for reservations (6 hours)
  expiryTime?: string;

  // Local order information
  localOrderInfo?: {
    orderNumber: number;
    customerName?: string;
    notes?: string;
  };
}

// Order pack store state
export interface OrderPackState {
  packs: OrderPack[];
  currentPackId: string | null;
  nextLocalOrderNumber: number;
}

// Legacy cart state interface (for backward compatibility)
export interface CartState {
  items: CartItem[];
  subtotal: number;
  tax: number;
  total: number;
}
