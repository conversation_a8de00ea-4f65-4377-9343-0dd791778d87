/**
 * Notification related types
 */

// Notification type
export type NotificationType = 'order' | 'medication' | 'stock' | 'system';

// Notification priority
export type NotificationPriority = 'low' | 'medium' | 'high';

// Notification status
export type NotificationStatus = 'read' | 'unread';

// Notification interface
export interface Notification {
  id: number;
  title: string;
  message: string;
  time: string; // ISO date string format or relative time string (e.g., "3 minutes ago")
  type: NotificationType;
  read: boolean;
  priority?: NotificationPriority; // Priority level for the notification
  category?: string; // Category or tag for the notification
  relatedId?: string; // ID of related entity (order, medication, etc.)
  relatedRoute?: string; // Route to navigate to when notification is clicked
}

// Filter options for notifications
export interface NotificationFilter {
  type: 'all' | NotificationType;
}

// Search parameters
export interface NotificationSearch {
  query: string;
}

// Pagination parameters
export interface NotificationPagination {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
}

// Notification view state
export interface NotificationViewState {
  notifications: Notification[];
  filteredNotifications: Notification[];
  filter: NotificationFilter;
  search: NotificationSearch;
  pagination: NotificationPagination;
  isLoading: boolean;
  error: string | null;
}
