/**
 * Types for the Add New Medication form
 */

import type { StockStatus, ExpiryStatus } from './inventory';

// Medication type options
export type MedicationType = 'pill' | 'syrup' | 'injection' | 'cream' | 'drops' | 'inhaler' | 'other';

// New medication form data
export interface NewMedicationFormData {
  id?: number; // Optional ID (will be generated on save)
  name: string;
  barcode?: string; // Optional barcode
  category: string;
  customCategory?: string; // For custom category when "Other" is selected
  type: MedicationType;
  quantity: number;
  expiryDate: string; // ISO date string format
  price: number;
  purchasePrice?: number; // Optional purchase price
  discount?: {
    hasDiscount: boolean;
    percentage?: number;
    validUntil?: string; // ISO date string format
  };
  supplier: string;
  storageLocation: string;
}

// Form validation errors
export interface MedicationFormErrors {
  name?: string;
  barcode?: string;
  category?: string;
  type?: string;
  quantity?: string;
  expiryDate?: string;
  price?: string;
  purchasePrice?: string;
  discount?: {
    percentage?: string;
    validUntil?: string;
  };
  supplier?: string;
  storageLocation?: string;
}

// Default values for a new medication form
export const defaultMedicationFormData: NewMedicationFormData = {
  name: '',
  barcode: '',
  category: '',
  type: 'pill',
  quantity: 0,
  expiryDate: '',
  price: 0,
  purchasePrice: 0,
  discount: {
    hasDiscount: false,
    percentage: 0,
    validUntil: ''
  },
  supplier: '',
  storageLocation: ''
};

// Medication type options for dropdown
export const medicationTypeOptions: { value: MedicationType; label: string }[] = [
  { value: 'pill', label: 'Pill' },
  { value: 'syrup', label: 'Syrup' },
  { value: 'injection', label: 'Injection' },
  { value: 'cream', label: 'Cream' },
  { value: 'drops', label: 'Drops' },
  { value: 'inhaler', label: 'Inhaler' },
  { value: 'other', label: 'Other' }
];

// Common medication categories
export const commonCategories: string[] = [
  'Pain Relief',
  'Antibiotics',
  'Allergy',
  'Digestive',
  'Vitamins',
  'Cardiovascular',
  'Respiratory',
  'Diabetes',
  'Mental Health',
  'Skin Care',
  'Eye Care',
  'First Aid'
];
