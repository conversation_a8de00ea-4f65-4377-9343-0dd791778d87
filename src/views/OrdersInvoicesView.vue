<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { ArrowPathIcon } from '@heroicons/vue/24/outline';

// Import tab components
import OrdersTabNavigation from '../components/orders/OrdersTabNavigation.vue';
import OrdersTab from '../components/orders/OrdersTab.vue';
import InvoicesTab from '../components/orders/InvoicesTab.vue';
import RevenueTab from '../components/orders/RevenueTab.vue';
import TopSellingTab from '../components/orders/TopSellingTab.vue';
import DeliveryTab from '../components/orders/DeliveryTab.vue';
import ReservedTab from '../components/orders/ReservedTab.vue';
import OrderDetailsModal from '../components/OrderDetailsModal.vue';
import InvoiceDetailsModal from '../components/InvoiceDetailsModal.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import Notification from '../components/Notification.vue';

// Composables
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// State
const activeTab = ref('orders');
const isLoading = ref(true);
const error = ref<string | null>(null);

// Tab management
const setActiveTab = (tabId: string) => {
  activeTab.value = tabId;
  updateUrlWithTab();
};

// Apply URL parameters on mount
const applyUrlParams = () => {
  try {
    // Check for tab parameter
    const tabParam = route.query.tab as string;
    if (tabParam && ['orders', 'invoices', 'revenue', 'top-selling', 'delivery', 'reserved'].includes(tabParam)) {
      activeTab.value = tabParam;
    }

    // Additional parameters can be handled by individual tab components
    // For example, status, section, etc.
  } catch (error) {
    console.error('Error applying URL parameters:', error);
  }
};

// Update URL with current tab
const updateUrlWithTab = () => {
  const query: Record<string, any> = { ...route.query };

  // Set tab parameter
  if (activeTab.value !== 'orders') {
    query.tab = activeTab.value;
  } else {
    delete query.tab; // Remove tab parameter if it's the default
  }

  // Update the URL without reloading the page
  router.replace({ query });
};

// Data management
const refreshData = () => {
  isLoading.value = true;
  error.value = null;

  // Simulate API call
  setTimeout(() => {
    isLoading.value = false;
  }, 1500);
};

// Modal state
const showOrderModal = ref(false);
const selectedOrder = ref(null);
const modalType = ref<'order' | 'delivery'>('order');

// Invoice modal state
const showInvoiceModal = ref(false);
const selectedInvoice = ref(null);

// Notification state
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationType = ref<'success' | 'error' | 'info'>('success');

// Helper function to show notifications
const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
  notificationMessage.value = message;
  notificationType.value = type;
  showNotification.value = true;
};

// Event handlers
const handleDownloadInvoice = (invoice: any) => {
  console.log('Downloading invoice:', invoice);
  try {
    if (invoice.pdfUrl) {
      // If PDF already exists, download it directly
      const link = document.createElement('a');
      link.href = invoice.pdfUrl;
      link.download = `invoice-${invoice.id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      showToast('PDF downloaded successfully', 'success');
    } else {
      // If no PDF exists, generate it on demand
      showToast('Generating PDF...', 'info');

      // Here you would typically call an API to generate the PDF
      // const pdfBlob = await generateInvoicePDFAPI(invoice.id);
      // downloadBlob(pdfBlob, `invoice-${invoice.id}.pdf`);

      // Simulate PDF generation
      setTimeout(() => {
        showToast('PDF generated and downloaded successfully', 'success');
      }, 1500);
    }
  } catch (error) {
    console.error('Error downloading invoice PDF:', error);
    showToast(t('toasts.error'), 'error');
  }
};

const handleViewDeliveryDetails = (order: any) => {
  console.log('Viewing delivery details:', order);
  selectedOrder.value = order;
  modalType.value = 'delivery';
  showOrderModal.value = true;
};

const handleReleaseReservation = (id: string) => {
  console.log('Releasing reservation:', id);

  // Show confirmation dialog
  if (confirm('Are you sure you want to release this reservation?')) {
    // Here you would typically call an API to release the reservation
    // For now, we'll just show a success message
    alert('Reservation released successfully');

    // Refresh the data
    refreshData();
  }
};

const handleViewOrder = (order: any) => {
  console.log('Viewing order details:', order);
  selectedOrder.value = order;
  modalType.value = 'order';
  showOrderModal.value = true;
};

const closeOrderModal = () => {
  showOrderModal.value = false;
  selectedOrder.value = null;
};

// Invoice handlers
const handleViewInvoice = (invoice: any) => {
  console.log('Viewing invoice details:', invoice);
  selectedInvoice.value = invoice;
  showInvoiceModal.value = true;
};

const closeInvoiceModal = () => {
  showInvoiceModal.value = false;
  selectedInvoice.value = null;
};

const handleDeleteInvoice = (invoice: any) => {
  console.log('Deleting invoice:', invoice);
  try {
    // Here you would typically call an API to delete the invoice
    // await deleteInvoiceAPI(invoice.id);
    showToast(t('toasts.invoiceDeleted'), 'success');
    closeInvoiceModal();
    refreshData();
  } catch (error) {
    console.error('Error deleting invoice:', error);
    showToast(t('toasts.deleteError'), 'error');
  }
};

const handlePrintInvoice = (invoice: any) => {
  console.log('Printing invoice:', invoice);
  try {
    window.print();
  } catch (error) {
    console.error('Error printing invoice:', error);
    showToast(t('toasts.error'), 'error');
  }
};

const handleDownloadPDF = (invoice: any) => {
  console.log('Downloading PDF:', invoice);
  try {
    if (invoice.pdfUrl) {
      // If PDF already exists, download it directly
      const link = document.createElement('a');
      link.href = invoice.pdfUrl;
      link.download = `invoice-${invoice.id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      showToast('PDF downloaded successfully', 'success');
    } else {
      // If no PDF exists, generate it on demand
      showToast('Generating PDF...', 'info');

      // Here you would typically call an API to generate the PDF
      // const pdfBlob = await generateInvoicePDFAPI(invoice.id);
      // downloadBlob(pdfBlob, `invoice-${invoice.id}.pdf`);

      // Simulate PDF generation
      setTimeout(() => {
        showToast('PDF generated and downloaded successfully', 'success');
      }, 1500);
    }
  } catch (error) {
    console.error('Error downloading PDF:', error);
    showToast(t('toasts.error'), 'error');
  }
};

const handleSendEmail = (invoice: any) => {
  console.log('Sending email:', invoice);
  try {
    // Here you would typically call an API to send email
    // await sendInvoiceEmailAPI(invoice.id);
    showToast(t('toasts.emailSent'), 'success');
  } catch (error) {
    console.error('Error sending email:', error);
    showToast(t('toasts.emailError'), 'error');
  }
};

const handleDuplicateInvoice = (invoice: any) => {
  console.log('Duplicating invoice:', invoice);
  try {
    // Here you would typically call an API to duplicate the invoice
    // await duplicateInvoiceAPI(invoice.id);
    showToast(t('toasts.invoiceDuplicated'), 'success');
    refreshData();
  } catch (error) {
    console.error('Error duplicating invoice:', error);
    showToast(t('toasts.duplicateError'), 'error');
  }
};

const handleChangeStatus = (data: any) => {
  console.log('Changing status:', data);
  try {
    // Here you would typically call an API to change status
    // await changeInvoiceStatusAPI(data.invoice.id, data.newStatus);
    showToast(t('toasts.statusChanged'), 'success');
    closeInvoiceModal();
    refreshData();
  } catch (error) {
    console.error('Error changing status:', error);
    showToast(t('toasts.statusChangeError'), 'error');
  }
};

// Order handlers
const handlePrintOrder = (order: any) => {
  console.log('Printing order:', order);
  try {
    window.print();
  } catch (error) {
    console.error('Error printing order:', error);
    showToast(t('toasts.error'), 'error');
  }
};

const handleCancelOrder = (order: any) => {
  console.log('Cancelling order:', order);
  try {
    // Here you would typically call an API to cancel the order
    // await cancelOrderAPI(order.id);
    showToast(t('orders.orderCancelled'), 'success');
    closeOrderModal();
    refreshData();
  } catch (error) {
    console.error('Error cancelling order:', error);
    showToast(t('orders.cancelError'), 'error');
  }
};

const handleReorder = (order: any) => {
  console.log('Reordering:', order);
  try {
    // Here you would typically add items to cart and redirect
    // await addItemsToCartAPI(order.items);
    showToast(t('orders.orderReordered'), 'success');
    closeOrderModal();
  } catch (error) {
    console.error('Error reordering:', error);
    showToast(t('orders.reorderError'), 'error');
  }
};

const handleDownloadOrderPDF = (order: any) => {
  console.log('Downloading order PDF:', order);
  try {
    // Here you would typically generate and download PDF
    // const pdfBlob = await generateOrderPDFAPI(order.id);
    // downloadBlob(pdfBlob, `order-${order.id}.pdf`);
    showToast('PDF downloaded successfully', 'success');
  } catch (error) {
    console.error('Error downloading PDF:', error);
    showToast(t('toasts.error'), 'error');
  }
};

const handleSendOrderEmail = (order: any) => {
  console.log('Sending order email:', order);
  try {
    // Here you would typically call an API to send email
    // await sendOrderEmailAPI(order.id);
    showToast(t('orders.emailSent'), 'success');
  } catch (error) {
    console.error('Error sending email:', error);
    showToast(t('orders.emailError'), 'error');
  }
};

// Initialize
onMounted(() => {
  applyUrlParams();
  refreshData();
});
</script>

<template>
  <div class="w-full max-w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-6 text-start rtl:text-right">{{
      t('orders.title') }}</h2>

    <!-- Tab Navigation -->
    <OrdersTabNavigation :active-tab="activeTab" @update:active-tab="setActiveTab" />

    <!-- Error message -->
    <div v-if="error"
      class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6 text-red-700 dark:text-red-400 text-center">
      <p class="mb-2">{{ error }}</p>
      <button
        class="inline-flex items-center bg-red-600 hover:bg-red-700 text-white border-none rounded px-4 py-2 mt-2 cursor-pointer font-medium transition-colors duration-200"
        @click="refreshData">
        <ArrowPathIcon class="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2 text-white" />
        {{ t('common.retry') }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="w-full">
      <!-- Orders Tab -->
      <OrdersTab v-if="activeTab === 'orders'" :loading="isLoading" @refresh="refreshData"
        @view-order="handleViewOrder" />

      <!-- Invoices Tab -->
      <InvoicesTab v-else-if="activeTab === 'invoices'" :loading="isLoading" @refresh="refreshData"
        @download-invoice="handleDownloadInvoice" @view-invoice="handleViewInvoice" />

      <!-- Revenue Summary Tab -->
      <RevenueTab v-else-if="activeTab === 'revenue'" :loading="isLoading" @refresh="refreshData" />

      <!-- Top Selling Medications Tab -->
      <TopSellingTab v-else-if="activeTab === 'top-selling'" :loading="isLoading" @refresh="refreshData" />

      <!-- Delivery Orders Tab -->
      <DeliveryTab v-else-if="activeTab === 'delivery'" :loading="isLoading" @refresh="refreshData"
        @view-details="handleViewDeliveryDetails" />

      <!-- Reserved Medications Tab -->
      <ReservedTab v-else-if="activeTab === 'reserved'" :loading="isLoading" @refresh="refreshData"
        @release-reservation="handleReleaseReservation" />
    </div>

    <!-- Unified Order Details Modal -->
    <OrderDetailsModal :show="showOrderModal" :order="selectedOrder" :type="modalType" @close="closeOrderModal"
      @print-order="handlePrintOrder" @cancel-order="handleCancelOrder" @reorder="handleReorder"
      @download-pdf="handleDownloadOrderPDF" @send-email="handleSendOrderEmail" />

    <!-- Invoice Details Modal -->
    <InvoiceDetailsModal :show="showInvoiceModal" :invoice="selectedInvoice" @close="closeInvoiceModal"
      @delete-invoice="handleDeleteInvoice" @print-invoice="handlePrintInvoice" @download-pdf="handleDownloadPDF"
      @send-email="handleSendEmail" @duplicate-invoice="handleDuplicateInvoice" @change-status="handleChangeStatus" />

    <!-- Toast Notification -->
    <Notification :show="showNotification" :message="notificationMessage" :type="notificationType" :duration="2000"
      @close="showNotification = false" />
  </div>
</template>
