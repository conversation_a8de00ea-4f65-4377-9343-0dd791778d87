<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { SunIcon, MoonIcon, BellIcon, BellSlashIcon, LanguageIcon, DevicePhoneMobileIcon, GlobeAltIcon, WifiIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/vue/24/outline';
import themeStore from '../store/themeStore';
import languageStore from '../store/languageStore';
import onlineModeStore from '../store/onlineModeStore';
import orderSettingsStore from '../store/orderSettingsStore';
import regionalSettingsStore from '../store/regionalSettingsStore';
import { setLanguage } from '../i18n';
import { currentThemeMode, toggleTheme } from '../theme/themeConfig';

import ToggleSwitch from '../components/ui/ToggleSwitch.vue';
import CountryDropdown from '../components/ui/CountryDropdown.vue';
import CurrencyDropdown from '../components/ui/CurrencyDropdown.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import Notification from '../components/Notification.vue';

// Props (passed from App.vue router-view but not used in this component)
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}

defineProps<{
  user?: User | null;
  isLoggedIn?: boolean;
}>();

// Setup i18n
const { t } = useI18n();

// Theme settings from store and theme config
const isDarkMode = computed(() => currentThemeMode.value === 'dark');
const notificationsEnabled = themeStore.state.notificationsEnabled;

// Online mode settings from store
const onlineModeEnabled = onlineModeStore.state.onlineModeEnabled;
const isTogglingOnlineMode = onlineModeStore.state.isTogglingOnlineMode;

// Order settings from store
const autoAcceptOrders = orderSettingsStore.state.autoAcceptOrders;

// Regional settings from store (reactive computed properties)
const selectedCountryCode = computed(() => regionalSettingsStore.state.value.selectedCountryCode);
const selectedCurrencyCode = computed(() => regionalSettingsStore.state.value.selectedCurrencyCode);
const selectedCountry = computed(() => regionalSettingsStore.selectedCountry.value);
const selectedCurrency = computed(() => regionalSettingsStore.selectedCurrency.value);

// Create v-model compatible computed properties
const isDarkModeModel = computed({
  get: () => isDarkMode.value,
  set: (value: boolean) => {
    // Only toggle if the value is different from current state
    if (value !== isDarkMode.value) {
      toggleTheme();
    }
  }
});

const notificationsEnabledModel = computed({
  get: () => notificationsEnabled.value,
  set: (value: boolean) => {
    if (value !== notificationsEnabled.value) {
      themeStore.toggleNotifications();

      // Show notification with appropriate color
      // Green when enabling, red when disabling
      const notificationType = value ? 'success' : 'error';
      const messageKey = value ? 'settings.notificationsEnabled' : 'settings.notificationsDisabled';
      showNotification(t(messageKey), notificationType);
    }
  }
});

const onlineModeEnabledModel = computed({
  get: () => onlineModeEnabled.value,
  set: async (value: boolean) => {
    if (value !== onlineModeEnabled.value && !isTogglingOnlineMode.value) {
      const result = await onlineModeStore.toggleOnlineMode();

      // Show notification based on result and action
      if (result.success && result.message) {
        // If disabling online mode, show as error/warning (red)
        // If enabling online mode, show as success (green)
        const notificationType = value ? 'success' : 'error';
        showNotification(t(result.message), notificationType);
      } else if (!result.success && result.message) {
        showNotification(t(result.message), 'error');
      }
    }
  }
});

const autoAcceptOrdersModel = computed({
  get: () => autoAcceptOrders.value,
  set: (value: boolean) => {
    if (value !== autoAcceptOrders.value) {
      orderSettingsStore.toggleAutoAcceptOrders();

      // Show notification with appropriate color
      const notificationType = value ? 'success' : 'error';
      const messageKey = value ? 'settings.autoAcceptOrdersEnabled' : 'settings.autoAcceptOrdersDisabled';
      showNotification(t(messageKey), notificationType);
    }
  }
});

// Regional settings v-model compatible computed properties
const selectedCountryCodeModel = computed({
  get: () => selectedCountryCode.value,
  set: (value: string) => {
    regionalSettingsStore.setCountry(value);
  }
});

const selectedCurrencyCodeModel = computed({
  get: () => selectedCurrencyCode.value,
  set: (value: string) => {
    // Currency is read-only, but we need this for v-model compatibility
    // The actual currency is set automatically by setCountry
  }
});

// Notification system
const showNotificationState = ref(false);
const notificationMessage = ref('');
const notificationType = ref<'success' | 'error' | 'info'>('success');

const showNotification = (message: string, type: 'success' | 'error') => {
  // First, hide any existing notification to clean the screen
  showNotificationState.value = false;

  // Use a small delay to ensure the previous notification is fully hidden
  // before showing the new one (allows for smooth transitions)
  setTimeout(() => {
    notificationMessage.value = message;
    notificationType.value = type;
    showNotificationState.value = true;
  }, 100); // 100ms delay for smooth transition
};

const closeNotification = () => {
  showNotificationState.value = false;
};

// Language settings from store
const currentLanguage = computed(() => languageStore.state.currentLanguage);
const languages = computed(() => languageStore.state.availableLanguages);
const showLanguageDropdown = ref(false);

// Mock connected devices data
const connectedDevices = ref([
  {
    id: 'device_abc123',
    name: 'Main Computer',
    lastActive: 'Just now',
    isCurrentDevice: true
  },
  {
    id: 'device_def456',
    name: 'Mobile Phone',
    lastActive: '2 hours ago',
    isCurrentDevice: false
  },
  {
    id: 'device_ghi789',
    name: 'Tablet',
    lastActive: '3 days ago',
    isCurrentDevice: false
  }
]);

// Toggle language dropdown
const toggleLanguageDropdown = () => {
  showLanguageDropdown.value = !showLanguageDropdown.value;
};

// Change language using setLanguage function
const changeLanguage = (langCode: string) => {
  if (langCode !== currentLanguage.value) {
    if (langCode === 'en' || langCode === 'ar') {
      setLanguage(langCode);
    }
  }
  showLanguageDropdown.value = false;
};

// Regional settings handlers
const handleCountryChange = (country: any) => {
  regionalSettingsStore.setCountry(country.code);
  const countryName = currentLanguage.value === 'ar' ? country.name.ar : country.name.en;
  const currency = selectedCurrency.value;
  const currencyName = currency ? (currentLanguage.value === 'ar' ? currency.name.ar : currency.name.en) : '';

  showNotification(
    t('settings.countryChanged', { country: countryName }) +
    (currencyName ? ` (${currencyName})` : ''),
    'success'
  );
};

// Event listeners for theme and notification changes
const handleThemeChange = () => {
  // Show notification for theme change
  const isDark = currentThemeMode.value === 'dark';
  const messageKey = isDark ? 'settings.darkModeEnabled' : 'settings.lightModeEnabled';
  showNotification(t(messageKey), 'success');
  console.log('Theme changed event received');
};

const handleNotificationsChange = () => {
  console.log('Notifications changed event received');
};

// Setup event listeners
onMounted(() => {
  document.addEventListener('themeChanged', handleThemeChange);
  document.addEventListener('notificationsChanged', handleNotificationsChange);

  // Initialize online mode store
  onlineModeStore.initOnlineMode();

  // Initialize order settings store
  orderSettingsStore.initAutoAcceptOrders();

  // Initialize regional settings store
  regionalSettingsStore.initRegionalSettings();
});

// Clean up event listeners
onUnmounted(() => {
  document.removeEventListener('themeChanged', handleThemeChange);
  document.removeEventListener('notificationsChanged', handleNotificationsChange);

  // Cleanup online mode store
  onlineModeStore.cleanup();
});
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <h1 class="text-2xl font-semibold text-gray-800 dark:text-neutral-100 mb-6 text-start">{{ t('settings.title') }}
    </h1>

    <div
      class="bg-white dark:bg-neutral-850 border border-gray-200 dark:border-neutral-700 rounded-xl shadow-sm p-6 mb-5">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-neutral-100 mb-4 text-start">{{ t('settings.appearance')
      }}
      </h2>

      <!-- Theme Toggle -->
      <ToggleSwitch v-model="isDarkModeModel" :label="t('settings.themeMode')"
        :description="isDarkMode ? t('settings.darkMode') : t('settings.lightMode')" :icon-enabled="MoonIcon"
        :icon-disabled="SunIcon" />
    </div>

    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl shadow-sm p-6 mb-5">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-start">{{ t('settings.notifications') }}
      </h2>

      <!-- Notifications Toggle -->
      <ToggleSwitch v-model="notificationsEnabledModel" :label="t('settings.enableNotifications')"
        :description="notificationsEnabled ? t('settings.notificationsEnabledStatus') : t('settings.notificationsDisabledStatus')"
        :icon-enabled="BellIcon" :icon-disabled="BellSlashIcon" />
    </div>

    <!-- System Preferences Section -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl shadow-sm p-6 mb-5">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-start">{{ t('settings.systemPreferences')
        }}
      </h2>

      <!-- Online Mode Toggle -->
      <ToggleSwitch v-model="onlineModeEnabledModel" :label="t('settings.onlineMode')"
        :description="onlineModeEnabled ? t('settings.onlineModeEnabled') : t('settings.onlineModeDisabled')"
        :icon-enabled="GlobeAltIcon" :icon-disabled="WifiIcon" :disabled="isTogglingOnlineMode" />

      <!-- Description -->
      <p class="text-xs text-gray-500 dark:text-gray-400 mt-3 leading-relaxed">
        {{ t('settings.onlineModeDescription') }}
      </p>

      <!-- Auto Accept Orders Toggle -->
      <div class="mt-6 pt-6 border-t border-gray-200 dark:border-slate-600">
        <ToggleSwitch v-model="autoAcceptOrdersModel" :label="t('settings.autoAcceptOrders')"
          :description="autoAcceptOrders ? t('settings.autoAcceptOrdersEnabled') : t('settings.autoAcceptOrdersDisabled')"
          :icon-enabled="CheckCircleIcon" :icon-disabled="XCircleIcon" :disabled="!onlineModeEnabled" />

        <!-- Description -->
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-3 leading-relaxed">
          {{ t('settings.autoAcceptOrdersDescription') }}
        </p>

        <!-- Offline Notice -->
        <div v-if="!onlineModeEnabled"
          class="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <p class="text-xs text-yellow-800 dark:text-yellow-200">
            {{ t('settings.onlineModeRequired', 'Online Mode must be enabled to use Auto Accept Orders.') }}
          </p>
        </div>
      </div>
    </div>

    <!-- Language Settings -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl shadow-sm p-6 mb-5">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-start">{{ t('settings.language') }}</h2>

      <!-- Language Selection -->
      <div
        class="flex justify-between items-center py-4 border-b border-gray-200 dark:border-slate-600 last:border-b-0">
        <div class="flex items-center gap-4">
          <div
            class="w-10 h-10 rounded-lg bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 text-primary-600 dark:text-primary-400 flex items-center justify-center shadow-sm">
            <LanguageIcon class="w-5 h-5" />
          </div>
          <div class="text-start">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ t('settings.changeLanguage') }}</h3>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ currentLanguage === 'en' ? 'English' : 'العربية' }}
            </p>
          </div>
        </div>

        <!-- Language Dropdown -->
        <div class="relative">
          <button @click="toggleLanguageDropdown"
            class="flex items-center gap-2 px-4 py-2 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-900 dark:text-white text-sm font-medium cursor-pointer transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 hover:border-primary-600 hover:-translate-y-0.5 shadow-sm hover:shadow-md"
            aria-label="Select language">
            {{ currentLanguage === 'en' ? 'English' : 'العربية' }}
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-500" viewBox="0 0 20 20"
              fill="currentColor">
              <path fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd" />
            </svg>
          </button>

          <div v-if="showLanguageDropdown"
            class="absolute top-full right-0 rtl:right-auto rtl:left-0 mt-2 min-w-40 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg shadow-lg z-10 overflow-hidden">
            <button v-for="lang in languages" :key="lang.code" @click="changeLanguage(lang.code)"
              class="block w-full px-4 py-3 text-left rtl:text-right text-sm text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200"
              :class="{ 'bg-primary-600 text-white hover:bg-primary-700': currentLanguage === lang.code }">
              {{ lang.name }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Regional Settings -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl shadow-sm p-6 mb-5">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-start">{{ t('settings.regionalSettings')
        }}</h2>

      <!-- Description -->
      <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
        {{ t('settings.regionalSettingsDescription') }}
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Country Selection -->
        <div>
          <CountryDropdown v-model="selectedCountryCodeModel" :label="t('settings.country')"
            :placeholder="t('settings.selectCountry')" @change="handleCountryChange" />
        </div>

        <!-- Currency Selection (Read-only, auto-set by country) -->
        <div>
          <CurrencyDropdown v-model="selectedCurrencyCodeModel" :label="t('settings.currency')"
            :placeholder="t('settings.selectCurrency')" :disabled="true" />
        </div>
      </div>
    </div>

    <!-- Connected Devices Settings -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl shadow-sm p-6 mb-5">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-start">{{ t('settings.connectedDevices')
        || 'Connected Devices' }}</h2>

      <!-- Device List -->
      <div class="space-y-4">
        <div v-for="device in connectedDevices" :key="device.id"
          class="flex items-center py-4 border-b border-gray-200 dark:border-slate-600 last:border-b-0">
          <div class="flex items-center gap-4">
            <div
              class="w-10 h-10 rounded-lg bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 text-primary-600 dark:text-primary-400 flex items-center justify-center shadow-sm">
              <DevicePhoneMobileIcon class="w-5 h-5" />
            </div>
            <div class="text-start">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white flex items-center gap-2">
                {{ device.name }}
                <span v-if="device.isCurrentDevice"
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200">
                  {{ t('settings.currentDevice') || 'Current Device' }}
                </span>
              </h3>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ t('settings.lastActive') || 'Last active' }}: {{
                device.lastActive }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Placeholder for additional settings -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl shadow-sm p-6 mb-5">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-start">{{ t('settings.additional') }}
      </h2>
      <p class="text-gray-500 dark:text-gray-400 italic py-4">{{ t('settings.additionalDescription') }}</p>
    </div>

    <!-- Notification Component -->
    <Notification :show="showNotificationState" :message="notificationMessage" :type="notificationType" :duration="2000"
      @close="closeNotification" />
  </div>
</template>
