<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import Notification from '../components/Notification.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import AutoOrderCheckoutForm from '../components/checkout/AutoOrderCheckoutForm.vue';
import ManualOrderForm from '../components/checkout/ManualOrderForm.vue';
import { formatCurrency, formatDate, formatTime } from '../utils/localeUtils';
import {
  ShoppingCartIcon,
  CreditCardIcon,
  TruckIcon,
  CheckCircleIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  UserIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  PrinterIcon,
  BuildingOffice2Icon
} from '@heroicons/vue/24/outline';
import orderPackStore from '../store/orderPackStore';
import SkeletonLoader from '../components/ui/SkeletonLoader.vue';

// Props (passed from App.vue router-view but not used in this component)
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}

defineProps<{
  user?: User | null;
  isLoggedIn?: boolean;
}>();

const { t } = useI18n();
const router = useRouter();
const { getCurrentPack, completeCurrentPack } = orderPackStore;

const showNotification = ref(false);
const notificationMessage = ref('');
const notificationType = ref<'success' | 'error' | 'info'>('success');
const currentStep = ref(1);
const isProcessing = ref(false);

const customerInfo = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  address: '',
  city: '',
  notes: ''
});

const paymentInfo = ref({
  method: 'card',
  cardNumber: '',
  expiryDate: '',
  cvv: '',
  cardholderName: ''
});

const cardValidation = ref({
  cardNumber: { isValid: true, message: '' },
  expiryDate: { isValid: true, message: '' },
  cvv: { isValid: true, message: '' },
  cardholderName: { isValid: true, message: '' }
});

const deliveryOption = ref('pickup');
const currentPack = computed(() => getCurrentPack());
const hasItems = computed(() => (currentPack.value?.items.length || 0) > 0);
const totalSteps = 3;

// Check if current pack is auto-accepted order
const isAutoAcceptedOrder = computed(() => currentPack.value?.type === 'auto-accepted');
const isLocalOrder = computed(() => currentPack.value?.type === 'local');

// Auto-fill customer info for auto-accepted orders
const autoFilledCustomerInfo = computed(() => {
  if (!isAutoAcceptedOrder.value || !currentPack.value?.userInfo) {
    return null;
  }

  const userInfo = currentPack.value.userInfo;
  const nameParts = userInfo.userName.split(' ');
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';

  // Extract city from address if possible
  const address = userInfo.location?.address || '';
  let city = '';
  if (address) {
    // Try to extract city from address (assuming format like "Street, City, Country")
    const addressParts = address.split(',');
    if (addressParts.length >= 2) {
      city = addressParts[addressParts.length - 2].trim();
    }
  }

  return {
    firstName,
    lastName,
    phone: userInfo.userPhone,
    address,
    city,
    email: '', // Not available from order request
    notes: `Order ID: ${userInfo.orderRequestId || ''}`
  };
});

// Auto-fill delivery option for auto-accepted orders
const autoFilledDeliveryOption = computed(() => {
  if (!isAutoAcceptedOrder.value || !currentPack.value?.userInfo) {
    return 'pickup';
  }

  return currentPack.value.userInfo.serviceType === 'Delivery' ? 'delivery' : 'pickup';
});

// Auto-fill payment method (default to cash for auto-accepted orders)
const autoFilledPaymentMethod = computed(() => {
  if (!isAutoAcceptedOrder.value) {
    return 'card';
  }

  // For auto-accepted orders, default to cash
  return 'cash';
});

// Ensure customerInfo is always defined to prevent undefined access errors
const safeCustomerInfo = computed(() => {
  return customerInfo.value || {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    notes: ''
  };
});

const formatPrice = (price: number) => {
  return formatCurrency(price);
};

// Auto-fill form when pack changes
const initializeFormData = () => {
  if (isAutoAcceptedOrder.value && autoFilledCustomerInfo.value) {
    // Auto-fill customer info
    customerInfo.value = { ...autoFilledCustomerInfo.value };

    // Auto-fill delivery option
    deliveryOption.value = autoFilledDeliveryOption.value;

    // Auto-fill payment method
    paymentInfo.value.method = autoFilledPaymentMethod.value;

    // Skip to step 2 for auto-accepted orders since customer info is pre-filled
    if (currentStep.value === 1) {
      currentStep.value = 2;
    }
  } else {
    // Reset form for local orders or any other case
    customerInfo.value = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      notes: ''
    };
    deliveryOption.value = 'pickup';
    paymentInfo.value.method = 'cash';
    currentStep.value = 1;
  }
};

// Watch for pack changes and auto-fill
watch(currentPack, () => {
  initializeFormData();
}, { immediate: true });

// Initialize on mount
onMounted(() => {
  initializeFormData();
});

const validateStep = (step: number): boolean => {
  switch (step) {
    case 1:
      return !!(customerInfo.value?.firstName && customerInfo.value?.lastName && customerInfo.value?.phone);
    case 2:
      if (deliveryOption.value === 'delivery') {
        if (!customerInfo.value?.address || !customerInfo.value?.city) return false;
      }
      if (paymentInfo.value.method === 'card') {
        // Validate all card fields
        const cardNumberValid = validateCardNumber(paymentInfo.value.cardNumber);
        const expiryValid = validateExpiryDate(paymentInfo.value.expiryDate);
        const cvvValid = validateCVV(paymentInfo.value.cvv);
        const nameValid = validateCardholderName(paymentInfo.value.cardholderName);

        // Update validation state
        cardValidation.value.cardNumber = cardNumberValid;
        cardValidation.value.expiryDate = expiryValid;
        cardValidation.value.cvv = cvvValid;
        cardValidation.value.cardholderName = nameValid;

        return cardNumberValid.isValid && expiryValid.isValid && cvvValid.isValid && nameValid.isValid;
      }
      return true;
    default:
      return true;
  }
};

const nextStep = () => {
  if (validateStep(currentStep.value)) {
    if (currentStep.value < totalSteps) {
      currentStep.value++;
    }
  } else {
    showNotification.value = true;
    notificationMessage.value = t('checkout.pleaseCompleteRequiredFields', 'Please complete all required fields');
    notificationType.value = 'error';
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const goToCart = () => {
  router.push('/cart');
};

const processOrder = async () => {
  if (!validateStep(2)) {
    showNotification.value = true;
    notificationMessage.value = t('checkout.pleaseCompleteRequiredFields', 'Please complete all required fields');
    notificationType.value = 'error';
    return;
  }

  isProcessing.value = true;

  try {
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Complete the current pack (removes it from cart and switches to next pack)
    completeCurrentPack();

    currentStep.value = 3;
    showNotification.value = true;
    notificationMessage.value = t('checkout.orderPlacedSuccessfully', 'Order placed successfully!');
    notificationType.value = 'success';
  } catch (error) {
    showNotification.value = true;
    notificationMessage.value = t('checkout.orderFailed', 'Failed to place order. Please try again.');
    notificationType.value = 'error';
  } finally {
    isProcessing.value = false;
  }
};

const handleNotificationClose = () => {
  showNotification.value = false;
};

const printInvoice = () => {
  try {
    // Hide the current page content and show print content
    const originalContent = document.body.innerHTML;
    const printContent = generateInvoiceHTML();

    // Replace body content with invoice
    document.body.innerHTML = printContent;

    // Trigger print dialog
    window.print();

    // Restore original content after printing
    document.body.innerHTML = originalContent;

    // Re-initialize Vue app after content restoration
    window.location.reload();
  } catch (error) {
    console.error('Error printing invoice:', error);
    showNotification.value = true;
    notificationMessage.value = t('checkout.printError', 'Failed to print invoice. Please try again.');
    notificationType.value = 'error';
  }
};

const generateInvoiceHTML = () => {
  const orderDate = formatDate(new Date());
  const orderTime = formatTime(new Date());
  const invoiceNumber = `INV-${Date.now()}`;

  return `
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
      <style>
        @media print {
          body { margin: 0; padding: 20px; }
          * { -webkit-print-color-adjust: exact; }
        }
        .invoice-header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #007bff;
          padding-bottom: 20px;
        }
        .invoice-header h1 {
          color: #007bff;
          margin: 0;
          font-size: 2.5em;
        }
        .invoice-header h2 {
          color: #666;
          margin: 10px 0 0 0;
          font-weight: normal;
        }
        .invoice-details, .customer-info {
          margin-bottom: 25px;
          background: #f8f9fa;
          padding: 15px;
          border-radius: 5px;
        }
        .customer-info h3 {
          margin-top: 0;
          color: #007bff;
          border-bottom: 1px solid #dee2e6;
          padding-bottom: 10px;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        .items-table th {
          background: #007bff;
          color: white;
          padding: 12px 8px;
          text-align: left;
          font-weight: bold;
        }
        .items-table td {
          border: 1px solid #dee2e6;
          padding: 10px 8px;
          text-align: left;
        }
        .items-table tbody tr:nth-child(even) {
          background-color: #f8f9fa;
        }
        .totals {
          text-align: start;
          margin-top: 30px;
          background: #f8f9fa;
          padding: 20px;
          border-radius: 5px;
        }
        .totals p {
          margin: 8px 0;
          font-size: 1.1em;
        }
        .total-row {
          font-weight: bold;
          font-size: 1.3em;
          color: #007bff;
          border-top: 2px solid #007bff;
          padding-top: 10px;
          margin-top: 15px;
        }
        .footer {
          margin-top: 50px;
          text-align: center;
          color: #666;
          border-top: 1px solid #dee2e6;
          padding-top: 20px;
        }
        .invoice-number {
          font-size: 1.2em;
          font-weight: bold;
          color: #007bff;
        }
      </style>

      <div class="invoice-header">
        <h1>${t('checkout.invoice.pharmacyName')}</h1>
        <h2>${t('checkout.invoice.title')}</h2>
        <div class="invoice-number">${t('checkout.invoice.invoiceNumber')}: ${invoiceNumber}</div>
      </div>

      <div class="invoice-details">
        <h3 style="margin-top: 0; color: #007bff;">${t('checkout.invoice.orderDetails')}</h3>
        <p><strong>${t('checkout.invoice.orderDate')}:</strong> ${orderDate}</p>
        <p><strong>${t('checkout.invoice.orderTime')}:</strong> ${orderTime}</p>
        <p><strong>${t('checkout.invoice.paymentMethod')}:</strong> ${formatPaymentMethod(paymentInfo.value.method)}</p>
        <p><strong>${t('checkout.invoice.deliveryOption')}:</strong> ${deliveryOption.value === 'pickup' ? t('checkout.invoice.pharmacyPickup') : t('checkout.invoice.homeDelivery')}</p>
      </div>

      <div class="customer-info">
        <h3>${t('checkout.invoice.customerInformation')}</h3>
        <p><strong>${t('checkout.invoice.name')}:</strong> ${customerInfo.value?.firstName || ''} ${customerInfo.value?.lastName || ''}</p>
        <p><strong>${t('checkout.invoice.email')}:</strong> ${customerInfo.value?.email || ''}</p>
        <p><strong>${t('checkout.invoice.phone')}:</strong> ${customerInfo.value?.phone || ''}</p>
        ${deliveryOption.value === 'delivery' ? `<p><strong>${t('checkout.invoice.address')}:</strong> ${customerInfo.value?.address || ''}${customerInfo.value?.city ? ', ' + customerInfo.value.city : ''}</p>` : ''}
        ${customerInfo.value?.notes ? `<p><strong>${t('checkout.invoice.notes')}:</strong> ${customerInfo.value.notes}</p>` : ''}
      </div>

      <table class="items-table">
        <thead>
          <tr>
            <th style="width: 50%;">${t('checkout.invoice.item')}</th>
            <th style="width: 15%;">${t('checkout.invoice.quantity')}</th>
            <th style="width: 17.5%;">${t('checkout.invoice.unitPrice')}</th>
            <th style="width: 17.5%;">${t('checkout.invoice.total')}</th>
          </tr>
        </thead>
        <tbody>
          ${(currentPack.value?.items || []).map(item => `
            <tr>
              <td>${item.name}</td>
              <td style="text-align: center;">${item.quantity}</td>
              <td style="text-align: right;">${formatPrice(item.pricePerUnit)}</td>
              <td style="text-align: right;">${formatPrice(item.totalPrice)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="totals">
        <p><strong>${t('checkout.invoice.subtotal')}:</strong> ${formatPrice(currentPack.value?.subtotal || 0)}</p>
        <p><strong>${t('checkout.invoice.tax')}:</strong> ${formatPrice(currentPack.value?.tax || 0)}</p>
        <p class="total-row"><strong>${t('checkout.invoice.totalAmount')}:</strong> ${formatPrice(currentPack.value?.total || 0)}</p>
      </div>

      <div class="footer">
        <p><strong>${t('checkout.invoice.thankYou')}</strong></p>
        <p>${t('checkout.invoice.systemName')}</p>
        <p style="font-size: 0.9em; margin-top: 20px;">
          ${t('checkout.invoice.computerGenerated')}
        </p>
      </div>
    </div>
  `;
};

const formatPaymentMethod = (method: string) => {
  const methods: Record<string, string> = {
    card: t('checkout.paymentMethods.card'),
    cash: t('checkout.paymentMethods.cash'),
    bank: t('checkout.paymentMethods.bank')
  };
  return methods[method] || method;
};

const viewOrders = () => {
  // Complete the current pack and go to orders page
  completeCurrentPack();
  router.push('/orders-invoices');
};

// Card validation functions
const validateCardNumber = (cardNumber: string) => {
  // Remove spaces and non-digits
  const cleaned = cardNumber.replace(/\D/g, '');

  if (!cleaned) {
    return { isValid: false, message: t('checkout.cardNumberRequired', 'Card number is required') };
  }

  if (cleaned.length < 13 || cleaned.length > 19) {
    return { isValid: false, message: t('checkout.cardNumberInvalid', 'Card number must be 13-19 digits') };
  }

  // Luhn algorithm validation
  let sum = 0;
  let isEven = false;

  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned[i]);

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  const isValid = sum % 10 === 0;
  return {
    isValid,
    message: isValid ? '' : t('checkout.cardNumberInvalid', 'Invalid card number')
  };
};

const validateExpiryDate = (expiryDate: string) => {
  if (!expiryDate) {
    return { isValid: false, message: t('checkout.expiryDateRequired', 'Expiry date is required') };
  }

  const regex = /^(0[1-9]|1[0-2])\/([0-9]{2})$/;
  if (!regex.test(expiryDate)) {
    return { isValid: false, message: t('checkout.expiryDateFormat', 'Format: MM/YY') };
  }

  const [month, year] = expiryDate.split('/');
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear() % 100;
  const currentMonth = currentDate.getMonth() + 1;

  const expYear = parseInt(year);
  const expMonth = parseInt(month);

  if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
    return { isValid: false, message: t('checkout.cardExpired', 'Card has expired') };
  }

  return { isValid: true, message: '' };
};

const validateCVV = (cvv: string) => {
  if (!cvv) {
    return { isValid: false, message: t('checkout.cvvRequired', 'CVV is required') };
  }

  if (!/^\d{3,4}$/.test(cvv)) {
    return { isValid: false, message: t('checkout.cvvInvalid', 'CVV must be 3-4 digits') };
  }

  return { isValid: true, message: '' };
};

const validateCardholderName = (name: string) => {
  if (!name.trim()) {
    return { isValid: false, message: t('checkout.cardholderNameRequired', 'Cardholder name is required') };
  }

  if (name.trim().length < 2) {
    return { isValid: false, message: t('checkout.cardholderNameTooShort', 'Name must be at least 2 characters') };
  }

  if (!/^[a-zA-Z\s]+$/.test(name.trim())) {
    return { isValid: false, message: t('checkout.cardholderNameInvalid', 'Name can only contain letters and spaces') };
  }

  return { isValid: true, message: '' };
};

// Format card number with spaces
const formatCardNumber = (value: string) => {
  const cleaned = value.replace(/\D/g, '');
  const formatted = cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
  return formatted;
};

// Format expiry date
const formatExpiryDate = (value: string) => {
  const cleaned = value.replace(/\D/g, '');
  if (cleaned.length >= 2) {
    return cleaned.substring(0, 2) + '/' + cleaned.substring(2, 4);
  }
  return cleaned;
};

// Real-time validation handlers
const handleCardNumberInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const formatted = formatCardNumber(target.value);
  paymentInfo.value.cardNumber = formatted;
  cardValidation.value.cardNumber = validateCardNumber(formatted);
};

const handleExpiryDateInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const formatted = formatExpiryDate(target.value);
  paymentInfo.value.expiryDate = formatted;
  cardValidation.value.expiryDate = validateExpiryDate(formatted);
};

const handleCVVInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value.replace(/\D/g, '').substring(0, 4);
  paymentInfo.value.cvv = value;
  cardValidation.value.cvv = validateCVV(value);
};

const handleCardholderNameInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  paymentInfo.value.cardholderName = target.value;
  cardValidation.value.cardholderName = validateCardholderName(target.value);
};
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <h1 class="text-2xl font-semibold text-gray-900 mb-6 text-start dark:text-gray-100">{{ t('checkout.title',
      'Checkout') }}</h1>

    <!-- Empty Cart -->
    <div v-if="!hasItems" class="mb-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-slate-800 dark:border-slate-700 p-6">
        <div class="flex flex-col items-center justify-center py-16 px-6 text-center">
          <div class="flex items-center justify-center w-20 h-20 bg-gray-100 dark:bg-slate-700 rounded-full mb-6">
            <ShoppingCartIcon class="w-10 h-10 text-gray-400 dark:text-gray-500" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            {{ t('checkout.emptyCart', 'Your cart is empty') }}
          </h2>
          <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
            {{ t('checkout.emptyCartMessage', 'Add items to your cart before proceeding to checkout') }}
          </p>
          <button @click="router.push('/stock')"
            class="inline-flex items-center gap-2 px-6 py-3 text-base bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary-600">
            <ShoppingCartIcon class="w-5 h-5" />
            {{ t('checkout.browseItems', 'Browse Items') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-else-if="!customerInfo" class="mb-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-slate-800 dark:border-slate-700 p-6">
        <div class="flex items-center justify-center py-16">
          <SkeletonLoader type="circle" width="w-8" height="h-8" />
          <span class="ml-3 text-gray-600 dark:text-gray-400">{{ t('common.loading', 'Loading...') }}</span>
        </div>
      </div>
    </div>

    <!-- Checkout Process -->
    <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- Main Checkout Form -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Progress Steps -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-slate-800 dark:border-slate-700 p-6">
          <div class="flex items-center">
            <!-- Step 1 -->
            <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors duration-200"
              :class="currentStep >= 1
                ? 'bg-primary-600 border-primary-600 text-white'
                : 'bg-white dark:bg-slate-800 border-gray-300 dark:border-slate-600 text-gray-400 dark:text-gray-500'">
              <CheckCircleIcon v-if="currentStep > 1" class="w-6 h-6" />
              <span v-else class="text-sm font-semibold">1</span>
            </div>

            <!-- Line 1 to 2 -->
            <div class="flex-1 h-0.5 mx-4 transition-colors duration-200"
              :class="currentStep > 1 ? 'bg-primary-600' : 'bg-gray-300 dark:bg-slate-600'"></div>

            <!-- Step 2 -->
            <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors duration-200"
              :class="currentStep >= 2
                ? 'bg-primary-600 border-primary-600 text-white'
                : 'bg-white dark:bg-slate-800 border-gray-300 dark:border-slate-600 text-gray-400 dark:text-gray-500'">
              <CheckCircleIcon v-if="currentStep > 2" class="w-6 h-6" />
              <span v-else class="text-sm font-semibold">2</span>
            </div>

            <!-- Line 2 to 3 -->
            <div class="flex-1 h-0.5 mx-4 transition-colors duration-200"
              :class="currentStep > 2 ? 'bg-primary-600' : 'bg-gray-300 dark:bg-slate-600'"></div>

            <!-- Step 3 -->
            <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors duration-200"
              :class="currentStep >= 3
                ? 'bg-primary-600 border-primary-600 text-white'
                : 'bg-white dark:bg-slate-800 border-gray-300 dark:border-slate-600 text-gray-400 dark:text-gray-500'">
              <CheckCircleIcon v-if="currentStep > 3" class="w-6 h-6" />
              <span v-else class="text-sm font-semibold">3</span>
            </div>
          </div>
          <div class="flex justify-between mt-4">
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ t('checkout.customerInfo', 'Customer Info') }}
            </span>
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ t('checkout.paymentDelivery', 'Payment & Delivery') }}
            </span>
            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ t('checkout.confirmation', 'Confirmation') }}
            </span>
          </div>
        </div>

        <!-- Step 1: Customer Information -->
        <div v-if="currentStep === 1">
          <!-- Auto-Accepted Order Form -->
          <AutoOrderCheckoutForm v-if="isAutoAcceptedOrder && currentPack" :order-pack="currentPack"
            :customer-info="customerInfo" :delivery-option="deliveryOption" :payment-method="paymentInfo.method"
            @update:delivery-option="deliveryOption = $event" @update:payment-method="paymentInfo.method = $event" />

          <!-- Manual/Local Order Form -->
          <ManualOrderForm v-else-if="currentPack" :order-pack="currentPack" :customer-info="customerInfo"
            :validation-errors="customerValidation" @update:customer-info="customerInfo = $event" />
        </div>

        <!-- Step 2: Payment & Delivery -->
        <div v-if="currentStep === 2" class="space-y-6">
          <!-- Delivery Options -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-slate-800 dark:border-slate-700 p-6">
            <div class="flex items-center gap-3 mb-6">
              <div class="flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <TruckIcon class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ t('checkout.deliveryOptions',
                'Delivery Options') }}</h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label class="relative">
                <input v-model="deliveryOption" type="radio" value="pickup" class="sr-only" />
                <div :class="[
                  'p-4 border-2 rounded-lg cursor-pointer transition-colors duration-200',
                  deliveryOption === 'pickup'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-slate-600 hover:border-gray-400 dark:hover:border-slate-500'
                ]">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ t('checkout.pickup',
                        'Pickup') }}</h3>
                      <p class="text-xs text-gray-600 dark:text-gray-400">
                        {{ t('checkout.pickupDescription', 'Collect from pharmacy') }}
                      </p>
                    </div>
                    <div :class="[
                      'w-4 h-4 rounded-full border-2 transition-colors duration-200',
                      deliveryOption === 'pickup'
                        ? 'border-primary-500 bg-primary-500'
                        : 'border-gray-300 dark:border-slate-600'
                    ]">
                      <div v-if="deliveryOption === 'pickup'" class="w-full h-full rounded-full bg-white scale-50">
                      </div>
                    </div>
                  </div>
                </div>
              </label>

              <label class="relative">
                <input v-model="deliveryOption" type="radio" value="delivery" class="sr-only" />
                <div :class="[
                  'p-4 border-2 rounded-lg cursor-pointer transition-colors duration-200',
                  deliveryOption === 'delivery'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-slate-600 hover:border-gray-400 dark:hover:border-slate-500'
                ]">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                        {{ t('checkout.delivery', 'Home Delivery') }}
                      </h3>
                      <p class="text-xs text-gray-600 dark:text-gray-400">{{ t('checkout.deliveryDescription',
                        'Delivered to your address') }}</p>
                    </div>
                    <div :class="[
                      'w-4 h-4 rounded-full border-2 transition-colors duration-200',
                      deliveryOption === 'delivery'
                        ? 'border-primary-500 bg-primary-500'
                        : 'border-gray-300 dark:border-slate-600'
                    ]">
                      <div v-if="deliveryOption === 'delivery'" class="w-full h-full rounded-full bg-white scale-50">
                      </div>
                    </div>
                  </div>
                </div>
              </label>
            </div>

            <!-- Delivery Address (if delivery selected) -->
            <div v-if="deliveryOption === 'delivery'" class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ t('checkout.address', 'Address') }} <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MapPinIcon class="h-5 w-5 text-gray-400" />
                  </div>
                  <input v-model="customerInfo.address" type="text"
                    class="w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:ring-primary-400 dark:focus:border-primary-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 dark:disabled:bg-slate-800 pl-10"
                    :placeholder="t('checkout.addressPlaceholder', 'Enter delivery address')" />
                </div>
              </div>

              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ t('checkout.city', 'City') }} <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <BuildingOffice2Icon class="h-5 w-5 text-gray-400" />
                  </div>
                  <input v-model="customerInfo.city" type="text"
                    class="w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:ring-primary-400 dark:focus:border-primary-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 dark:disabled:bg-slate-800 pl-10"
                    :placeholder="t('checkout.cityPlaceholder', 'Enter city')" />
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Methods -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-slate-800 dark:border-slate-700 p-6">
            <div class="flex items-center gap-3 mb-6">
              <div class="flex items-center justify-center w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <CreditCardIcon class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ t('checkout.paymentMethod', 'Payment Method') }}
              </h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <label class="relative">
                <input v-model="paymentInfo.method" type="radio" value="card" class="sr-only" />
                <div :class="[
                  'p-4 border-2 rounded-lg cursor-pointer transition-colors duration-200',
                  paymentInfo.method === 'card'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-slate-600 hover:border-gray-400 dark:hover:border-slate-500'
                ]">
                  <div class="text-center">
                    <CreditCardIcon class="w-8 h-8 mx-auto mb-2 text-gray-600 dark:text-gray-400" />
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ t('checkout.creditCard',
                      'Credit Card') }}</h3>
                  </div>
                </div>
              </label>

              <label class="relative">
                <input v-model="paymentInfo.method" type="radio" value="cash" class="sr-only" />
                <div :class="[
                  'p-4 border-2 rounded-lg cursor-pointer transition-colors duration-200',
                  paymentInfo.method === 'cash'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-slate-600 hover:border-gray-400 dark:hover:border-slate-500'
                ]">
                  <div class="text-center">
                    <div
                      class="w-8 h-8 mx-auto mb-2 flex items-center justify-center bg-green-100 dark:bg-green-900/20 rounded-full">
                      <span class="text-green-600 dark:text-green-400 text-lg font-bold">$</span>
                    </div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ t('checkout.cash', 'Cash') }}
                    </h3>
                  </div>
                </div>
              </label>

              <label class="relative">
                <input v-model="paymentInfo.method" type="radio" value="insurance" class="sr-only" />
                <div :class="[
                  'p-4 border-2 rounded-lg cursor-pointer transition-colors duration-200',
                  paymentInfo.method === 'insurance'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-slate-600 hover:border-gray-400 dark:hover:border-slate-500'
                ]">
                  <div class="text-center">
                    <div
                      class="w-8 h-8 mx-auto mb-2 flex items-center justify-center bg-teal-100 dark:bg-teal-900/20 rounded-full">
                      <span class="text-teal-600 dark:text-teal-400 text-xs font-bold">INS</span>
                    </div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ t('checkout.insurance',
                      'Insurance') }}</h3>
                  </div>
                </div>
              </label>
            </div>

            <!-- Credit Card Details (if card selected) -->
            <div v-if="paymentInfo.method === 'card'" class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ t('checkout.cardholderName', 'Cardholder Name') }} <span class="text-red-500">*</span>
                </label>
                <input :value="paymentInfo.cardholderName" @input="handleCardholderNameInput" type="text" :class="[
                  'w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:ring-primary-400 dark:focus:border-primary-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 dark:disabled:bg-slate-800',
                  !cardValidation.cardholderName.isValid ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                ]" :placeholder="t('checkout.cardholderNamePlaceholder', 'Enter cardholder name')" />
                <p v-if="!cardValidation.cardholderName.isValid" class="text-sm text-red-600 mt-1 dark:text-red-400">
                  {{ cardValidation.cardholderName.message }}
                </p>
              </div>

              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ t('checkout.cardNumber', 'Card Number') }} <span class="text-red-500">*</span>
                </label>
                <input :value="paymentInfo.cardNumber" @input="handleCardNumberInput" type="text" :class="[
                  'w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:ring-primary-400 dark:focus:border-primary-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 dark:disabled:bg-slate-800',
                  !cardValidation.cardNumber.isValid ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                ]" maxlength="23" :placeholder="t('checkout.cardNumberPlaceholder', '1234 5678 9012 3456')" />
                <p v-if="!cardValidation.cardNumber.isValid" class="text-sm text-red-600 mt-1 dark:text-red-400">
                  {{ cardValidation.cardNumber.message }}
                </p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ t('checkout.expiryDate', 'Expiry Date') }} <span class="text-red-500">*</span>
                </label>
                <input :value="paymentInfo.expiryDate" @input="handleExpiryDateInput" type="text" :class="[
                  'w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:ring-primary-400 dark:focus:border-primary-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 dark:disabled:bg-slate-800',
                  !cardValidation.expiryDate.isValid ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                ]" maxlength="5" :placeholder="t('checkout.expiryDatePlaceholder', 'MM/YY')" />
                <p v-if="!cardValidation.expiryDate.isValid" class="text-sm text-red-600 mt-1 dark:text-red-400">
                  {{ cardValidation.expiryDate.message }}
                </p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {{ t('checkout.cvv', 'CVV') }} <span class="text-red-500">*</span>
                </label>
                <input :value="paymentInfo.cvv" @input="handleCVVInput" type="text" :class="[
                  'w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:ring-primary-400 dark:focus:border-primary-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 dark:disabled:bg-slate-800',
                  !cardValidation.cvv.isValid ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                ]" maxlength="4" :placeholder="t('checkout.cvvPlaceholder', '123')" />
                <p v-if="!cardValidation.cvv.isValid" class="text-sm text-red-600 mt-1 dark:text-red-400">
                  {{ cardValidation.cvv.message }}
                </p>
              </div>
            </div>
          </div>

          <!-- Order Notes -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-slate-800 dark:border-slate-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              {{ t('checkout.orderNotes', 'Order Notes') }}
            </h3>
            <textarea v-model="customerInfo.notes" rows="3"
              class="w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100 dark:placeholder-gray-400 dark:focus:ring-primary-400 dark:focus:border-primary-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 dark:disabled:bg-slate-800 resize-none"
              :placeholder="t('checkout.orderNotesPlaceholder', 'Any special instructions or notes for your order...')"></textarea>
          </div>
        </div>

        <!-- Step 3: Confirmation -->
        <div v-if="currentStep === 3"
          class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-slate-800 dark:border-slate-700 p-6">
          <div class="text-center">
            <div
              class="flex items-center justify-center w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full mx-auto mb-6">
              <CheckCircleIcon class="w-12 h-12 text-green-600 dark:text-green-400" />
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              {{ t('checkout.orderConfirmed', 'Order Confirmed!') }}
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">{{ t('checkout.orderConfirmedMessage',
              'Thank you for your order. We will process it shortly and notify you when it is ready.') }}</p>

            <div class="flex items-center justify-between gap-4 max-w-md mx-auto">
              <button @click="viewOrders"
                class="inline-flex items-center gap-2 px-6 py-3 text-base bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800 flex-1 justify-center">
                {{ t('checkout.viewOrders', 'View My Orders') }}
              </button>

              <button @click="printInvoice"
                class="inline-flex items-center gap-2 px-6 py-3 text-base bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary-600 flex-1 justify-center">
                <PrinterIcon class="w-5 h-5 mr-2" />
                {{ t('checkout.printInvoice', 'Print Invoice') }}
              </button>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons -->
        <div v-if="currentStep < 3"
          class="flex items-center justify-between bg-gray-50 dark:bg-neutral-900/50 rounded-xl p-4 border border-gray-200 dark:border-neutral-700">

          <!-- Left Side: Next/Place Order Button -->
          <div class="flex-1">
            <!-- Next Button (Step 1) -->
            <button v-if="currentStep === 1" @click="nextStep"
              class="inline-flex items-center gap-2 px-6 py-3 text-base bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary-600">
              {{ t('checkout.next', 'Next') }}
              <ArrowRightIcon class="w-4 h-4" />
            </button>

            <!-- Place Order Button (Step 2) -->
            <button v-if="currentStep === 2" @click="processOrder" :disabled="isProcessing"
              class="inline-flex items-center gap-2 px-6 py-3 text-base bg-success-600 hover:bg-success-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-success-600">
              <SkeletonLoader v-if="isProcessing" type="circle" width="w-4" height="h-4" />
              <CheckCircleIcon v-else class="w-4 h-4" />
              {{ isProcessing ? t('checkout.processing', 'Processing...') : t('checkout.placeOrder', 'Place Order') }}
            </button>
          </div>

          <!-- Center: Empty space for cleaner layout -->
          <div class="flex-1"></div>

          <!-- Right Side: Back to Cart Button (Step 1) or Previous Button (Step 2+) -->
          <div class="flex-1 flex justify-end">
            <!-- Back to Cart Button (Step 1) -->
            <button v-if="currentStep === 1" @click="goToCart"
              class="inline-flex items-center gap-2 px-4 py-2.5 bg-transparent hover:bg-gray-100 text-gray-600 hover:text-gray-800 font-medium rounded-lg transition-all duration-200 dark:hover:bg-slate-800 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed">
              <ArrowLeftIcon class="w-4 h-4" />
              {{ t('checkout.backToCart', 'Back to Cart') }}
            </button>

            <!-- Previous Button (Step 2+) -->
            <button v-if="currentStep > 1" @click="prevStep"
              class="inline-flex items-center gap-2 px-4 py-2.5 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-all duration-200 border border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-gray-200 dark:border-slate-600 dark:hover:border-slate-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-slate-800">
              <ArrowLeftIcon class="w-4 h-4" />
              {{ t('checkout.previous', 'Previous') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Order Summary Sidebar -->
      <div class="lg:col-span-1">
        <div
          class="bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-slate-800 dark:border-slate-700 p-6 sticky top-20">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            {{ t('checkout.orderSummary', 'Order Summary') }}
          </h2>

          <!-- Cart Items -->
          <div class="space-y-4 mb-6">
            <div v-for="item in currentPack?.items || []" :key="item.id" class="flex items-center gap-3">
              <div class="flex-1 min-w-0">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{{ item.name }}</h3>
                <p class="text-xs text-gray-600 dark:text-gray-400">{{ t(`medication.types.${item.type}`) }}</p>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ formatPrice(item.totalPrice) }}</p>
                <p class="text-xs text-gray-600 dark:text-gray-400">{{ item.quantity }} × {{
                  formatPrice(item.pricePerUnit) }}</p>
              </div>
            </div>
          </div>

          <!-- Order Totals -->
          <div class="border-t border-gray-200 dark:border-slate-700 pt-4 space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ t('checkout.subtotal', 'Subtotal') }}</span>
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ formatPrice(currentPack?.subtotal ||
                0) }}</span>
            </div>

            <div v-if="deliveryOption === 'delivery'" class="flex justify-between items-center">
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ t('checkout.deliveryFee', 'Delivery Fee')
              }}</span>
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ formatPrice(5.00) }}</span>
            </div>

            <div v-if="(currentPack?.tax || 0) > 0" class="flex justify-between items-center">
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ t('checkout.tax', 'Tax') }}</span>
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ formatPrice(currentPack?.tax || 0)
              }}</span>
            </div>

            <div class="border-t border-gray-200 dark:border-slate-700 pt-3">
              <div class="flex justify-between items-center">
                <span class="text-base font-semibold text-gray-900 dark:text-gray-100">{{ t('checkout.total', 'Total')
                }}</span>
                <span class="text-lg font-bold text-primary-600 dark:text-primary-400">
                  {{ formatPrice((currentPack?.total || 0) + (deliveryOption === 'delivery' ? 5.00 : 0)) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Order Details Summary (for steps 2 and 3) -->
          <div v-if="currentStep >= 2" class="border-t border-gray-200 dark:border-slate-700 pt-6 mt-6">
            <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4">
              {{ t('checkout.orderDetails', 'Order Details') }}
            </h3>

            <div class="space-y-3 text-sm">
              <div v-if="customerInfo?.firstName || customerInfo?.lastName">
                <span class="text-gray-600 dark:text-gray-400">{{ t('checkout.customer', 'Customer') }}:</span>
                <span class="text-gray-900 dark:text-gray-100 ml-2">{{ customerInfo?.firstName }} {{
                  customerInfo?.lastName }}</span>
              </div>

              <div v-if="customerInfo?.phone">
                <span class="text-gray-600 dark:text-gray-400">{{ t('checkout.phone', 'Phone') }}:</span>
                <span class="text-gray-900 dark:text-gray-100 ml-2">{{ customerInfo?.phone }}</span>
              </div>

              <div>
                <span class="text-gray-600 dark:text-gray-400">{{ t('checkout.deliveryMethod', 'Delivery') }}:</span>
                <span class="text-gray-900 dark:text-gray-100 ml-2">
                  {{
                    deliveryOption === 'pickup' ? t('checkout.pickup', 'Pickup') : t('checkout.delivery', 'Home Delivery')
                  }}
                </span>
              </div>

              <div v-if="deliveryOption === 'delivery' && customerInfo?.address">
                <span class="text-gray-600 dark:text-gray-400">{{ t('checkout.address', 'Address') }}:</span>
                <span class="text-gray-900 dark:text-gray-100 ml-2">{{ customerInfo?.address }}, {{ customerInfo?.city
                  }}</span>
              </div>

              <div>
                <span class="text-gray-600 dark:text-gray-400">{{ t('checkout.payment', 'Payment') }}:</span>
                <span class="text-gray-900 dark:text-gray-100 ml-2">
                  {{ paymentInfo.method === 'card' ? t('checkout.creditCard', 'Credit Card') :
                    paymentInfo.method === 'cash' ? t('checkout.cash', 'Cash') :
                      t('checkout.insurance', 'Insurance') }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Toast Notification -->
    <Notification :show="showNotification" :message="notificationMessage" :type="notificationType" :duration="3000"
      @close="handleNotificationClose" />
  </div>
</template>
