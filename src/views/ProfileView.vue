<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  ChartBarIcon,
  UsersIcon,
  ClipboardDocumentCheckIcon,
  ChartPieIcon,
  PencilIcon,
  KeyIcon,
  BellIcon,
  BellSlashIcon,
  DocumentTextIcon,
  BuildingStorefrontIcon,
  MapPinIcon,
  DocumentArrowDownIcon
} from '@heroicons/vue/24/outline';
import DashboardWidget from '../components/dashboard/DashboardWidget.vue';
import ToggleSwitch from '../components/ui/ToggleSwitch.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import themeStore from '../store/themeStore';

// Setup i18n
const { t } = useI18n();

// Props
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
  name?: string;
  phone?: string;
}

const props = defineProps<{ user?: User }>();

// Mock user data (to be replaced with real data later)
const userData = computed(() => {
  return {
    userId: props.user?.userId || 'user-123',
    role: props.user?.role || 'PharmacyOwner',
    name: props.user?.name || 'Ahmed Hassan',
    phone: props.user?.phone || '+20 ************',
    email: props.user?.email || '<EMAIL>',
    first_name: 'Ahmed',
    last_name: 'Hassan',
    gender: 'Male',
    preferred_language: 'en',
    token: props.user?.token || ''
  };
});

// Mock pharmacy data
const pharmacyData = {
  name: "Health First Pharmacy",
  license_number: "CIR-010-***********",
  tax_number: "EGY-001-**********",
  location: {
    name: "Health First Main Branch",
    address: "Building 123, 4th Floor, Medical Street, Near Tahrir Square, Downtown",
    city: "Cairo",
    state: "Cairo Governorate",
    country: "Egypt",
    zip_code: "11511",
    latitude: 30.0444,
    longitude: 31.2357
  },
  description: "Health First Pharmacy is a fully licensed and registered pharmacy located in the heart of Downtown Cairo. We provide a wide range of prescription and over-the-counter medications, health consultations, and pharmaceutical services."
};

// Role selection (mock for now)
const selectedRole = ref<'PharmacyOwner' | 'PharmacyUser'>(props.user!.role);
const showRoleDropdown = ref(false);

const toggleRoleDropdown = () => {
  showRoleDropdown.value = !showRoleDropdown.value;
};

const selectRole = (role: 'PharmacyOwner' | 'PharmacyUser') => {
  selectedRole.value = role;
  showRoleDropdown.value = false;
};

// Notifications toggle using themeStore
const notificationsEnabled = themeStore.state.notificationsEnabled;
const notificationsEnabledModel = computed({
  get: () => notificationsEnabled.value,
  set: (value: boolean) => {
    if (value !== notificationsEnabled.value) {
      themeStore.toggleNotifications();
    }
  }
});

// PDF viewer
const showPdfViewer = ref(false);
const togglePdfViewer = () => {
  showPdfViewer.value = !showPdfViewer.value;
  if (showPdfViewer.value) {
    // Open PDF in a new window/tab
    window.open('/agreement.pdf', '_blank');
  }
};

// Download agreement
const downloadAgreement = () => {
  const link = document.createElement('a');
  link.href = '/agreement.pdf';
  link.download = 'pharmacy_agreement.pdf';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Computed properties
const isAdmin = computed(() => selectedRole.value === 'PharmacyOwner');
const userInitial = computed(() => userData.value.name.charAt(0).toUpperCase());
const roleName = computed(() => isAdmin.value ? t('user.roleAdmin') : t('user.roleUser'));
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <h1 class="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-6 text-start">{{ t('user.profile') }}</h1>

    <!-- Role Selection Dropdown (Mock) -->
    <div
      class="flex items-center gap-3 mb-6 p-4 bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-800 rounded-lg">
      <span class="text-sm font-medium text-teal-800 dark:text-teal-200">{{ t('user.viewAs') }}:</span>
      <div class="relative">
        <button @click="toggleRoleDropdown"
          class="flex items-center gap-2 px-4 py-2 bg-white dark:bg-neutral-850 border border-gray-200 dark:border-neutral-700 rounded-lg text-gray-900 dark:text-neutral-100 text-sm font-medium cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-neutral-800 hover:border-primary-600 hover:-translate-y-0.5 shadow-sm hover:shadow-md">
          <span>{{ isAdmin ? t('user.roleAdmin') : t('user.roleUser') }}</span>
          <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
              clip-rule="evenodd" />
          </svg>
        </button>
        <div v-if="showRoleDropdown"
          class="absolute top-full left-0 rtl:left-auto rtl:right-0 mt-2 min-w-48 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg shadow-lg z-10 overflow-hidden">
          <button @click="selectRole('PharmacyOwner')"
            class="block w-full px-4 py-3 text-left rtl:text-right text-sm text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200"
            :class="{ 'bg-primary-600 text-white hover:bg-primary-700': selectedRole === 'PharmacyOwner' }">
            {{ t('user.roleAdmin') }}
          </button>
          <button @click="selectRole('PharmacyUser')"
            class="block w-full px-4 py-3 text-left rtl:text-right text-sm text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200"
            :class="{ 'bg-primary-600 text-white hover:bg-primary-700': selectedRole === 'PharmacyUser' }">
            {{ t('user.roleUser') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Profile Card -->
    <div class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl shadow-sm p-6 mb-6">
      <div class="flex flex-col md:flex-row items-center md:items-start gap-6">
        <div class="flex-shrink-0">
          <div
            class="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center text-2xl font-semibold text-white">
            {{ userInitial }}
          </div>
        </div>
        <div class="flex-1 text-center md:text-start">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-1">{{ userData.name }}</h2>
          <div class="text-primary-600 dark:text-primary-400 font-medium mb-4">{{ roleName }}</div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.email') }}</span>
              <span class="text-sm text-gray-900 dark:text-white">{{ userData.email }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.phone') }}</span>
              <span class="text-sm text-gray-900 dark:text-white">{{ userData.phone }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.userId') }}</span>
              <span class="text-sm text-gray-900 dark:text-white font-mono">{{ userData.userId }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Admin Controls (visible only if Admin role is selected) -->
    <DashboardWidget v-if="isAdmin" :title="t('user.adminControls')" id="admin-controls" class="mb-5">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <button
          class="flex items-center gap-3 p-4 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-900 dark:text-white cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-slate-700 hover:border-primary-600 hover:-translate-y-0.5 shadow-sm hover:shadow-md">
          <ChartBarIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
          <span class="text-sm font-medium">{{ t('user.viewAnalytics') }}</span>
        </button>
        <button
          class="flex items-center gap-3 p-4 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-900 dark:text-white cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-slate-700 hover:border-primary-600 hover:-translate-y-0.5 shadow-sm hover:shadow-md">
          <UsersIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
          <span class="text-sm font-medium">{{ t('user.manageStaff') }}</span>
        </button>
        <button
          class="flex items-center gap-3 p-4 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-500 dark:text-gray-400 cursor-not-allowed"
          disabled>
          <ClipboardDocumentCheckIcon class="w-5 h-5" />
          <span class="text-sm font-medium">{{ t('user.inventoryAudit') }}</span>
        </button>
        <button
          class="flex items-center gap-3 p-4 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg text-gray-500 dark:text-gray-400 cursor-not-allowed"
          disabled>
          <ChartPieIcon class="w-5 h-5" />
          <span class="text-sm font-medium">{{ t('user.profitDashboard') }}</span>
        </button>
      </div>
    </DashboardWidget>

    <!-- Pharmacy Information (visible only if Admin role is selected) -->
    <DashboardWidget v-if="isAdmin" :title="t('user.pharmacyInfo')" id="pharmacy-info" class="mb-5">
      <div class="space-y-6">
        <!-- Pharmacy Basic Info -->
        <div class="bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg p-4">
          <div class="flex items-center gap-3 mb-4">
            <BuildingStorefrontIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ t('user.pharmacyName') }}</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.pharmacyName')
                }}</span>
              <span class="text-sm text-gray-900 dark:text-white">{{ pharmacyData.name }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.licenseNumber')
                }}</span>
              <span class="text-sm text-gray-900 dark:text-white font-mono">{{ pharmacyData.license_number }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.taxNumber') }}</span>
              <span class="text-sm text-gray-900 dark:text-white font-mono">{{ pharmacyData.tax_number }}</span>
            </div>
          </div>
        </div>

        <!-- Pharmacy Location -->
        <div class="bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg p-4">
          <div class="flex items-center gap-3 mb-4">
            <MapPinIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ t('user.location') }}</h3>
          </div>
          <div class="grid grid-cols-1 gap-4">
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.address') }}</span>
              <span class="text-sm text-gray-900 dark:text-white">{{ pharmacyData.location.address }}</span>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.city') }}</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ pharmacyData.location.city }}</span>
              </div>
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.state') }}</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ pharmacyData.location.state }}</span>
              </div>
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.country') }}</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ pharmacyData.location.country }}</span>
              </div>
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.zipCode') }}</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ pharmacyData.location.zip_code }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Pharmacy Description -->
        <div class="bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg p-4">
          <div class="flex items-center gap-3 mb-4">
            <DocumentTextIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ t('user.description') }}</h3>
          </div>
          <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">{{ pharmacyData.description }}</p>
        </div>

        <!-- Agreement Document -->
        <div class="bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg p-4">
          <div class="flex items-center gap-3 mb-4">
            <DocumentTextIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ t('user.viewAgreement') }}</h3>
          </div>
          <div class="flex flex-col sm:flex-row gap-3">
            <button @click="togglePdfViewer"
              class="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 shadow-sm hover:shadow-md">
              <DocumentTextIcon class="w-4 h-4" />
              {{ t('user.viewAgreement') }}
            </button>
            <button @click="downloadAgreement"
              class="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 shadow-sm hover:shadow-md">
              <DocumentArrowDownIcon class="w-4 h-4" />
              {{ t('user.downloadAgreement') }}
            </button>
          </div>
        </div>
      </div>
    </DashboardWidget>

    <!-- Personal Information (visible only if Admin role is selected) -->
    <DashboardWidget v-if="isAdmin" :title="t('user.personalInfo')" id="personal-info" class="mb-5">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="flex flex-col">
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.firstName') }}</span>
          <span class="text-sm text-gray-900 dark:text-white">{{ userData.first_name }}</span>
        </div>
        <div class="flex flex-col">
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.lastName') }}</span>
          <span class="text-sm text-gray-900 dark:text-white">{{ userData.last_name }}</span>
        </div>
        <div class="flex flex-col">
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.email') }}</span>
          <span class="text-sm text-gray-900 dark:text-white">{{ userData.email }}</span>
        </div>
        <div class="flex flex-col">
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.phone') }}</span>
          <span class="text-sm text-gray-900 dark:text-white">{{ userData.phone }}</span>
        </div>
        <div class="flex flex-col">
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.gender') }}</span>
          <span class="text-sm text-gray-900 dark:text-white">{{ userData.gender }}</span>
        </div>
        <div class="flex flex-col">
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ t('user.preferredLanguage')
            }}</span>
          <span class="text-sm text-gray-900 dark:text-white">{{ userData.preferred_language === 'en' ?
            t('language.english') : t('language.arabic') }}</span>
        </div>
      </div>
    </DashboardWidget>

    <!-- Account Preferences (visible only if Admin role is selected) -->
    <DashboardWidget v-if="isAdmin" :title="t('user.accountPreferences')" id="account-preferences" class="mb-5">
      <div class="space-y-4">
        <div
          class="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg gap-4">
          <div class="flex items-center gap-3">
            <PencilIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
            <div>
              <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ t('user.updateInfo') }}</h3>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ t('user.updateInfoDesc') }}</p>
            </div>
          </div>
          <button
            class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 shadow-sm hover:shadow-md">
            {{ t('user.update') }}
          </button>
        </div>

        <div
          class="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg gap-4">
          <div class="flex items-center gap-3">
            <KeyIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
            <div>
              <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ t('user.changePassword') }}</h3>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ t('user.changePasswordDesc') }}</p>
            </div>
          </div>
          <button
            class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 shadow-sm hover:shadow-md">
            {{ t('user.change') }}
          </button>
        </div>

        <div class="p-4 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg">
          <ToggleSwitch v-model="notificationsEnabledModel" :label="t('user.notifications')"
            :description="t('user.notificationsDesc')" :icon-enabled="BellIcon" :icon-disabled="BellSlashIcon" />
        </div>
      </div>
    </DashboardWidget>
  </div>
</template>
