<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import DashboardWidget from '../components/dashboard/DashboardWidget.vue';
import EarningPointsCard from '../components/wallet/EarningPointsCard.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import { formatCurrency, formatDate } from '../utils/localeUtils';
import {
  CreditCardIcon,
  CurrencyDollarIcon,
  StarIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon as ClockIconOutline
} from '@heroicons/vue/24/outline';

// Props (passed from App.vue router-view but not used in this component)
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}

defineProps<{
  user?: User | null;
  isLoggedIn?: boolean;
}>();

// Setup i18n for translations
const { t } = useI18n();

// Mock data for wallet
const walletData = ref({
  id: 'DW-3928281',
  currency: 'EGP',
  balance: 15,
  points: 820,
  monthlyLimit: 1000,
  expiringPoints: {
    amount: 200,
    daysLeft: 22
  }
});

// Mock data for transactions
const transactions = ref([
  {
    id: 'TX-78945',
    type: 'Purchase',
    amount: -120.50,
    points: 12,
    date: new Date(2023, 6, 15, 14, 30),
    status: 'Completed'
  },
  {
    id: 'TX-78946',
    type: 'Top-Up',
    amount: 500.00,
    points: 0,
    date: new Date(2023, 6, 14, 10, 15),
    status: 'Completed'
  },
  {
    id: 'TX-78947',
    type: 'Point Earned',
    amount: 0,
    points: 50,
    date: new Date(2023, 6, 13, 16, 45),
    status: 'Completed'
  },
  {
    id: 'TX-78948',
    type: 'Purchase',
    amount: -75.25,
    points: 7,
    date: new Date(2023, 6, 12, 9, 20),
    status: 'Completed'
  },
  {
    id: 'TX-78949',
    type: 'Purchase',
    amount: -200.00,
    points: 20,
    date: new Date(2023, 6, 11, 13, 10),
    status: 'Pending'
  }
]);

// Note: Using imported locale-aware formatting functions from utils/localeUtils

// Get transaction type icon
const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'Purchase':
      return ArrowUpIcon;
    case 'Top-Up':
      return ArrowDownIcon;
    case 'Point Earned':
      return StarIcon;
    default:
      return CurrencyDollarIcon;
  }
};

// Get transaction status icon and class
const getTransactionStatus = (status: string) => {
  switch (status) {
    case 'Completed':
      return {
        icon: CheckCircleIcon,
        class: 'status-completed'
      };
    case 'Pending':
      return {
        icon: ClockIconOutline,
        class: 'status-pending'
      };
    case 'Failed':
      return {
        icon: ExclamationCircleIcon,
        class: 'status-failed'
      };
    default:
      return {
        icon: CheckCircleIcon,
        class: 'status-completed'
      };
  }
};

// Get expiring points message
const expiringPointsMessage = computed(() => {
  const { amount, daysLeft } = walletData.value.expiringPoints;
  if (daysLeft > 30) {
    const months = Math.floor(daysLeft / 30);
    return t('wallet.expiringPointsMonths', { amount, months });
  }
  return t('wallet.expiringPointsDays', { amount, days: daysLeft });
});

// Animation state
const isCardVisible = ref(false);
const areRowsVisible = ref(false);
const isLoading = ref(true);
const isEarningPointsLoading = ref(true);
const isTransactionsLoading = ref(true);

onMounted(() => {
  // Simulate loading wallet data
  setTimeout(() => {
    isLoading.value = false;
    // Trigger wallet card animation after loading
    setTimeout(() => {
      isCardVisible.value = true;
    }, 100);
  }, 1500);

  // Simulate loading earning points data
  setTimeout(() => {
    isEarningPointsLoading.value = false;
  }, 1800);

  // Simulate loading transaction data
  setTimeout(() => {
    isTransactionsLoading.value = false;
    // Trigger transaction rows animation after loading
    setTimeout(() => {
      areRowsVisible.value = true;
    }, 100);
  }, 2000);
});
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <h1 class="text-2xl font-semibold text-gray-900 mb-6 text-start dark:text-gray-100">{{ t('wallet.title') }}</h1>

    <!-- Wallet Card -->
    <div class="mb-6">
      <!-- Loading Skeleton -->
      <div v-if="isLoading" class="animate-pulse">
        <div class="bg-gray-100 dark:bg-neutral-800 rounded-xl p-4 sm:p-6 shadow-lg">
          <!-- Header Skeleton -->
          <div class="flex items-center justify-between mb-4 sm:mb-6">
            <div class="flex items-center gap-2">
              <div class="w-8 h-8 bg-gray-200 dark:bg-neutral-700 rounded-lg"></div>
              <div>
                <div class="w-20 h-4 bg-gray-200 dark:bg-neutral-700 rounded mb-1"></div>
                <div class="w-12 h-3 bg-gray-200 dark:bg-neutral-700 rounded"></div>
              </div>
            </div>
            <div class="text-right">
              <div class="w-8 h-3 bg-gray-200 dark:bg-neutral-700 rounded mb-1"></div>
              <div class="w-16 h-3 bg-gray-200 dark:bg-neutral-700 rounded"></div>
            </div>
          </div>

          <!-- Balance Skeleton -->
          <div class="mb-4">
            <div class="w-16 h-3 bg-gray-200 dark:bg-neutral-700 rounded mb-2"></div>
            <div class="w-32 h-8 bg-gray-200 dark:bg-neutral-700 rounded"></div>
          </div>

          <!-- Cards Skeleton -->
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 max-w-lg">
            <div class="bg-gray-200 dark:bg-neutral-700 rounded-lg p-3">
              <div class="w-20 h-4 bg-gray-300 dark:bg-neutral-600 rounded mb-2"></div>
              <div class="w-16 h-3 bg-gray-300 dark:bg-neutral-600 rounded"></div>
            </div>
            <div class="bg-gray-200 dark:bg-neutral-700 rounded-lg p-3">
              <div class="w-24 h-4 bg-gray-300 dark:bg-neutral-600 rounded mb-2"></div>
              <div class="w-20 h-3 bg-gray-300 dark:bg-neutral-600 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actual Wallet Card -->
      <div v-else class="opacity-0 transform translate-y-3 transition-all duration-500 ease-out text-start"
        :class="{ 'opacity-100 translate-y-0': isCardVisible }">
        <div
          class="relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 dark:from-primary-700 dark:via-primary-800 dark:to-primary-900 rounded-xl p-4 sm:p-6 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 group">
          <!-- Decorative background elements -->
          <div
            class="absolute -top-16 -right-16 w-32 h-32 bg-white/8 rounded-full blur-2xl group-hover:bg-white/12 transition-all duration-300">
          </div>
          <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>
          <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/5 to-transparent opacity-50">
          </div>

          <!-- Card Header -->
          <div class="relative flex items-center justify-between mb-4 sm:mb-6">
            <div class="flex items-center gap-2">
              <div class="p-1.5 bg-white/15 rounded-lg backdrop-blur-sm">
                <CreditCardIcon class="w-4 h-4 sm:w-5 sm:h-5" />
              </div>
              <div>
                <span class="text-base sm:text-lg font-bold tracking-wide">DAWINII</span>
                <div class="text-xs opacity-85 font-medium">{{ t('wallet.walletLabel') }}</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-xs opacity-70 uppercase tracking-wider">{{ t('wallet.id') }}</div>
              <div class="text-xs font-mono opacity-85 break-all">{{ walletData.id }}</div>
            </div>
          </div>

          <!-- Card Body -->
          <div class="relative flex flex-col gap-4">
            <!-- Balance -->
            <div class="flex flex-col">
              <span class="text-xs opacity-80 mb-1 uppercase tracking-wider font-medium">{{ t('wallet.balance')
                }}</span>
              <span class="text-xl sm:text-2xl md:text-3xl font-bold tracking-tight break-all">{{
                formatCurrency(walletData.balance) }}</span>
            </div>

            <!-- Points & Limit Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 max-w-lg">
              <!-- Points -->
              <div class="bg-white/10 backdrop-blur-sm rounded-lg p-2 sm:p-3 border border-white/15">
                <div class="flex items-center gap-1.5 mb-1">
                  <StarIcon class="w-3 h-3 sm:w-4 sm:h-4 text-amber-300" />
                  <span class="text-xs sm:text-sm font-semibold">{{ walletData.points }}</span>
                  <span class="text-xs opacity-80">{{ t('wallet.points') }}</span>
                </div>
                <div class="flex items-center gap-1.5 text-xs opacity-70">
                  <ClockIcon class="w-3 h-3" />
                  <span class="truncate">{{ expiringPointsMessage }}</span>
                </div>
              </div>

              <!-- Monthly Limit -->
              <div class="bg-white/10 backdrop-blur-sm rounded-lg p-2 sm:p-3 border border-white/15">
                <div class="text-xs opacity-70 uppercase tracking-wider mb-1">{{ t('wallet.monthlyLimit') }}</div>
                <div class="text-xs sm:text-sm font-semibold break-all">{{ formatCurrency(walletData.monthlyLimit) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Earning Points Card -->
    <EarningPointsCard :loading="isEarningPointsLoading" />

    <!-- Transaction History -->
    <DashboardWidget :title="t('wallet.transactionHistory')" id="transaction-history" :full-width="true">
      <!-- Loading Skeleton -->
      <div v-if="isTransactionsLoading" class="animate-pulse">
        <!-- Desktop Loading -->
        <div
          class="hidden md:block overflow-hidden rounded-xl border border-gray-200 dark:border-neutral-700 bg-gray-50 dark:bg-neutral-800 shadow-sm">
          <div class="bg-gray-100 dark:bg-neutral-700 px-6 py-3">
            <div class="grid grid-cols-6 gap-4">
              <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
              <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
            </div>
          </div>
          <div class="divide-y divide-gray-200 dark:divide-neutral-700">
            <div v-for="i in 5" :key="i" class="px-6 py-4">
              <div class="grid grid-cols-6 gap-4 items-center">
                <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
                <div class="flex items-center gap-2">
                  <div class="w-4 h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
                  <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded flex-1"></div>
                </div>
                <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
                <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
                <div class="h-4 bg-gray-200 dark:bg-neutral-600 rounded"></div>
                <div class="h-6 bg-gray-200 dark:bg-neutral-600 rounded-full w-20"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Loading -->
        <div class="md:hidden space-y-4">
          <div v-for="i in 3" :key="i"
            class="bg-gray-50 dark:bg-neutral-800 rounded-xl border border-gray-200 dark:border-neutral-700 p-4 shadow-sm">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <div class="w-5 h-5 bg-gray-200 dark:bg-neutral-700 rounded"></div>
                <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-24"></div>
              </div>
              <div class="h-6 bg-gray-200 dark:bg-neutral-700 rounded-full w-16"></div>
            </div>
            <div class="grid grid-cols-2 gap-3">
              <div>
                <div class="h-3 bg-gray-200 dark:bg-neutral-700 rounded w-16 mb-1"></div>
                <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-20"></div>
              </div>
              <div>
                <div class="h-3 bg-gray-200 dark:bg-neutral-700 rounded w-12 mb-1"></div>
                <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-16"></div>
              </div>
              <div>
                <div class="h-3 bg-gray-200 dark:bg-neutral-700 rounded w-10 mb-1"></div>
                <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-14"></div>
              </div>
              <div>
                <div class="h-3 bg-gray-200 dark:bg-neutral-700 rounded w-8 mb-1"></div>
                <div class="h-4 bg-gray-200 dark:bg-neutral-700 rounded w-18"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actual Transaction History -->
      <div v-else>
        <!-- Desktop Table View -->
        <div
          class="hidden md:block overflow-hidden rounded-xl border border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-800 shadow-sm">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 dark:bg-slate-700">
                <tr>
                  <th
                    class="text-start px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t('wallet.transactionId') }}
                  </th>
                  <th
                    class="text-start px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t('wallet.type') }}
                  </th>
                  <th
                    class="text-start px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t('wallet.amount') }}
                  </th>
                  <th
                    class="text-start px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t('wallet.points') }}
                  </th>
                  <th
                    class="text-start px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t('wallet.date') }}
                  </th>
                  <th
                    class="text-start px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t('wallet.status') }}
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-slate-700">
                <tr v-for="(transaction, index) in transactions" :key="transaction.id"
                  class="opacity-0 transform translate-y-2 transition-all duration-300 ease-out hover:bg-gray-50 dark:hover:bg-slate-700"
                  :class="{ 'opacity-100 translate-y-0': areRowsVisible }"
                  :style="{ 'transition-delay': `${index * 100}ms` }">
                  <td class="px-4 lg:px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ transaction.id }}
                  </td>
                  <td class="px-4 lg:px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center gap-2">
                      <component :is="getTransactionIcon(transaction.type)"
                        class="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      <span class="text-sm text-gray-900 dark:text-gray-100">{{
                        t(`wallet.transactionTypes.${transaction.type.toLowerCase().replace(' ', '').replace('-', '')}`)
                        }}</span>
                    </div>
                  </td>
                  <td class="px-4 lg:px-6 py-4 whitespace-nowrap text-sm font-medium"
                    :class="transaction.amount < 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'">
                    {{ formatCurrency(transaction.amount) }}
                  </td>
                  <td class="px-4 lg:px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center gap-1">
                      <StarIcon class="w-4 h-4 text-amber-500" />
                      <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ transaction.points }}</span>
                    </div>
                  </td>
                  <td class="px-4 lg:px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                    {{ formatDate(transaction.date) }}
                  </td>
                  <td class="px-4 lg:px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium" :class="{
                      'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400': transaction.status === 'Completed',
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400': transaction.status === 'Pending',
                      'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400': transaction.status === 'Failed'
                    }">
                      <component :is="getTransactionStatus(transaction.status).icon" class="w-3 h-3" />
                      {{ t(`wallet.statuses.${transaction.status.toLowerCase()}`) }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Mobile Card View -->
        <div class="md:hidden space-y-4">
          <div v-for="(transaction, index) in transactions" :key="transaction.id"
            class="bg-white dark:bg-slate-800 rounded-xl border border-gray-200 dark:border-slate-700 p-4 shadow-sm opacity-0 transform translate-y-2 transition-all duration-300 ease-out"
            :class="{ 'opacity-100 translate-y-0': areRowsVisible }"
            :style="{ 'transition-delay': `${index * 100}ms` }">
            <!-- Transaction Header -->
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <component :is="getTransactionIcon(transaction.type)"
                  class="w-5 h-5 text-gray-600 dark:text-gray-400" />
                <span class="font-medium text-gray-900 dark:text-gray-100 text-sm">
                  {{ t(`wallet.transactionTypes.${transaction.type.toLowerCase().replace(' ', '').replace('-', '')}`) }}
                </span>
              </div>
              <span class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium" :class="{
                'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400': transaction.status === 'Completed',
                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400': transaction.status === 'Pending',
                'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400': transaction.status === 'Failed'
              }">
                <component :is="getTransactionStatus(transaction.status).icon" class="w-3 h-3" />
                {{ t(`wallet.statuses.${transaction.status.toLowerCase()}`) }}
              </span>
            </div>

            <!-- Transaction Details -->
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div>
                <div class="text-gray-500 dark:text-gray-400 text-xs uppercase tracking-wider mb-1">{{
                  t('wallet.transactionId') }}</div>
                <div class="font-mono text-gray-900 dark:text-gray-100">{{ transaction.id }}</div>
              </div>
              <div>
                <div class="text-gray-500 dark:text-gray-400 text-xs uppercase tracking-wider mb-1">{{
                  t('wallet.amount') }}</div>
                <div class="font-semibold"
                  :class="transaction.amount < 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'">
                  {{ formatCurrency(transaction.amount) }}
                </div>
              </div>
              <div>
                <div class="text-gray-500 dark:text-gray-400 text-xs uppercase tracking-wider mb-1">{{
                  t('wallet.points') }}</div>
                <div class="flex items-center gap-1">
                  <StarIcon class="w-4 h-4 text-amber-500" />
                  <span class="font-medium text-gray-900 dark:text-gray-100">{{ transaction.points }}</span>
                </div>
              </div>
              <div>
                <div class="text-gray-500 dark:text-gray-400 text-xs uppercase tracking-wider mb-1">{{ t('wallet.date')
                  }}</div>
                <div class="text-gray-600 dark:text-gray-400">{{ formatDate(transaction.date) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardWidget>
  </div>
</template>
