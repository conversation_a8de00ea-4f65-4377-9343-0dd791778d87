<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ArrowPathIcon } from '@heroicons/vue/24/outline';
import RatingSummary from '../components/reviews/RatingSummary.vue';
import ReviewsList from '../components/reviews/ReviewsList.vue';
import ReviewsTips from '../components/reviews/ReviewsTips.vue';
import SearchFilter from '../components/ui/SearchFilter.vue';
import Pagination from '../components/ui/Pagination.vue';
import AlertMessage from '../components/AlertMessage.vue';
import onlineModeStore from '../store/onlineModeStore';
import reviewsService from '../services/reviewsService';
import type { Review, RatingSummary as RatingSummaryType, ReviewsFilter, ReviewsSearch } from '../types/reviews';

const { t } = useI18n();

// State
const reviews = ref<Review[]>([]);
const ratingSummary = ref<RatingSummaryType>({
  averageRating: 0,
  totalReviews: 0,
  starBreakdown: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
});
const isLoading = ref(true);
const error = ref<string | null>(null);

// Search and filter state
const searchQuery = ref('');
const filters = ref<Record<string, any>>({
  rating: 'all',
  period: 'all',
  verified: 'all'
});

// Pagination state
const currentPage = ref(1);
const itemsPerPage = ref(10);
const totalFilteredReviews = ref(0);

// Online mode state
const isOnlineModeDisabled = computed(() => {
  return !onlineModeStore.state.onlineModeEnabled.value;
});

// Show offline warning when online mode is disabled and we have reviews to display
const showOfflineWarning = computed(() => {
  return isOnlineModeDisabled.value && reviews.value.length > 0 && !isLoading.value;
});

// Filter configuration for SearchFilter component
const filterConfig = computed(() => ({
  rating: {
    label: t('reviews.rating'),
    options: [
      { value: 'all', label: t('common.all') },
      { value: '5', label: t('reviews.stars.5') },
      { value: '4', label: t('reviews.stars.4') },
      { value: '3', label: t('reviews.stars.3') },
      { value: '2', label: t('reviews.stars.2') },
      { value: '1', label: t('reviews.stars.1') }
    ],
    multiSelect: false
  },
  period: {
    label: t('common.period'),
    options: [
      { value: 'all', label: t('common.allTime') },
      { value: 'today', label: t('common.today') },
      { value: 'this-week', label: t('common.thisWeek') },
      { value: 'this-month', label: t('common.thisMonth') },
      { value: 'this-year', label: t('common.thisYear') }
    ],
    multiSelect: false
  },
  verified: {
    label: t('common.verification'),
    options: [
      { value: 'all', label: t('common.all') },
      { value: 'verified', label: t('common.verified') },
      { value: 'unverified', label: t('common.unverified') }
    ],
    multiSelect: false
  }
}));

// All filtered reviews (without pagination)
const allFilteredReviews = computed(() => {
  let filtered = [...reviews.value];

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(review =>
      (review.customerName && review.customerName.toLowerCase().includes(query)) ||
      review.message.toLowerCase().includes(query)
    );
  }

  // Apply rating filter
  const ratingValue = Array.isArray(filters.value.rating) ? filters.value.rating[0] : filters.value.rating;
  if (ratingValue !== 'all') {
    const ratingFilter = parseInt(ratingValue);
    filtered = filtered.filter(review => review.rating === ratingFilter);
  }

  // Apply verification filter
  const verifiedValue = Array.isArray(filters.value.verified) ? filters.value.verified[0] : filters.value.verified;
  if (verifiedValue !== 'all') {
    const isVerified = verifiedValue === 'verified';
    filtered = filtered.filter(review => review.verified === isVerified);
  }

  // Apply period filter
  const periodValue = Array.isArray(filters.value.period) ? filters.value.period[0] : filters.value.period;
  if (periodValue !== 'all') {
    const now = new Date();
    let startDate = new Date();

    switch (periodValue) {
      case 'today':
        // Start of today
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'this-week':
        // Start of this week (7 days ago)
        startDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'this-month':
        // Start of this month
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'this-year':
        // Start of this year
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
    }

    filtered = filtered.filter(review => {
      const reviewDate = new Date(review.date);
      return reviewDate >= startDate && reviewDate <= now;
    });
  }

  // Update total count for pagination
  totalFilteredReviews.value = filtered.length;

  return filtered;
});

// Paginated reviews for display
const filteredReviews = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage.value;
  const endIndex = startIndex + itemsPerPage.value;
  return allFilteredReviews.value.slice(startIndex, endIndex);
});

// Load reviews data
const loadReviews = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await reviewsService.fetchReviews();
    reviews.value = response.reviews;
    ratingSummary.value = response.ratingSummary;
  } catch (err) {
    console.error('Error loading reviews:', err);
    error.value = t('reviews.errorLoading', 'Failed to load reviews. Please try again.');
  } finally {
    isLoading.value = false;
  }
};

// Refresh data
const refreshData = async () => {
  reviewsService.clearCache();
  await loadReviews();
};

// Handle search from SearchFilter component
const handleSearchFilterSearch = (searchValue: string) => {
  searchQuery.value = searchValue;
  resetPagination();
};

// Handle filter change from SearchFilter component
const handleSearchFilterChange = (filterType: string, value: string | string[]) => {
  // Extract the actual value (handle both single values and arrays)
  const actualValue = Array.isArray(value) ? value[0] : value;

  if (filterType === 'rating') {
    filters.value.rating = actualValue;
  } else if (filterType === 'period') {
    filters.value.period = actualValue;
  } else if (filterType === 'verified') {
    filters.value.verified = actualValue;
  }

  resetPagination();
};

// Handle reset from SearchFilter component
const handleSearchFilterReset = () => {
  searchQuery.value = '';
  filters.value = {
    rating: 'all',
    period: 'all',
    verified: 'all'
  };
  currentPage.value = 1; // Reset to first page
};

// Pagination handlers
const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handleItemsPerPageChange = (newItemsPerPage: number) => {
  itemsPerPage.value = newItemsPerPage;
  currentPage.value = 1; // Reset to first page when changing items per page
};

// Reset to first page when filters change
const resetPagination = () => {
  currentPage.value = 1;
};

// Load data on component mount
onMounted(() => {
  loadReviews();
});
</script>

<template>
  <div class="w-full">
    <!-- Offline Data Warning -->
    <AlertMessage v-if="showOfflineWarning" :message="t('reviews.offlineDataWarning')" type="warning"
      :auto-close="false" :show-close-button="true" class="mb-6" />

    <!-- Page Header -->
    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 text-start rtl:text-right">
      {{ t('reviews.title') }}
    </h1>

    <!-- Search and Filter Controls -->
    <SearchFilter v-model:search="searchQuery" v-model:filters="filters" :filter-config="filterConfig"
      :placeholder="t('reviews.searchPlaceholder', 'Search reviews by customer name or message...')"
      :loading="isLoading" @search="handleSearchFilterSearch" @filter-change="handleSearchFilterChange"
      @reset="handleSearchFilterReset" class="mb-6" />

    <!-- Rating Summary -->
    <RatingSummary :summary="ratingSummary" :loading="isLoading" />

    <!-- Reviews List -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Reviews List -->
      <div class="lg:col-span-2">
        <ReviewsList :reviews="filteredReviews" :loading="isLoading" :error="error" />

        <!-- Pagination -->
        <div v-if="!isLoading && !error && totalFilteredReviews > 0" class="mt-8">
          <Pagination :current-page="currentPage" :total-items="totalFilteredReviews" :items-per-page="itemsPerPage"
            :items-per-page-options="[5, 10, 25, 50]" :loading="isLoading" size="md"
            @update:current-page="handlePageChange" @update:items-per-page="handleItemsPerPageChange" />
        </div>
      </div>

      <!-- Tips Sidebar -->
      <div class="lg:col-span-1">
        <ReviewsTips :loading="isLoading" />
      </div>
    </div>
  </div>
</template>
