<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import type { User, DashboardData } from '../types/dashboard';
import type { NewMedicationFormData } from '../types/medicationForm';
import { fetchDashboardData, getDashboardLoadingState } from '../services/dashboardService';
import { fetchAds, type AdData } from '../services/adService';

// Import dashboard components
import AdSlider from '../components/dashboard/AdSlider.vue';
import InventorySummary from '../components/dashboard/InventorySummary.vue';
import SalesSummary from '../components/dashboard/SalesSummary.vue';
import DeliveryOrders from '../components/dashboard/DeliveryOrders.vue';
import StoreOrdersStatus from '../components/dashboard/StoreOrdersStatus.vue';
import AppOrders from '../components/dashboard/AppOrders.vue';
import QuickActions from '../components/dashboard/QuickActions.vue';
import RevenueChart from '../components/dashboard/RevenueChart.vue';
import AddMedicationModal from '../components/medication/AddMedicationModal.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import onlineModeStore from '../store/onlineModeStore';
import Notification from '../components/Notification.vue';

// Setup i18n
const i18n = useI18n();
const { t } = i18n;

// Props
defineProps<{
  user: User
}>();

// Emits
const emit = defineEmits(['toggle-qr-generator', 'navigate']);

// Dashboard data
const dashboardData = ref<DashboardData | null>(null);
const { isLoading, error } = getDashboardLoadingState();

// Ad slider data
const ads = ref<AdData[]>([]);
const adsLoading = ref(false);

// Add Medication Modal state
const showAddMedicationModal = ref(false);
const showNotification = ref(false);
const notificationMessage = ref('');

// Computed properties for each section's data
const inventoryData = computed(() => dashboardData.value?.inventory || {
  totalMedications: 0,
  outOfStock: 0,
  expiringSoon: 0,
  recentlyAdded: 0,
  lowStock: 0
});

const salesData = computed(() => dashboardData.value?.sales || {
  todayInvoices: 0,
  todayRevenue: 0,
  topSelling: []
});



const deliveryData = computed(() => dashboardData.value?.delivery || {
  new: 0,
  inTransit: 0,
  completed: 0
});

const storeOrdersData = computed(() => dashboardData.value?.storeOrders || {
  pending: 0,
  inTransit: 0,
  completed: 0,
  rejected: 0
});

const appOrdersData = computed(() => dashboardData.value?.appOrders || {
  totalUsers: 0,
  activeUsers: 0,
  totalOrders: 0,
  averageOrderValue: 0,
  pendingReviews: 0
});

const monthlyRevenueData = computed(() => dashboardData.value?.monthlyRevenue || []);

// Check if online features (like ads) should be available
const canShowOnlineFeatures = computed(() => onlineModeStore.state.canUseOnlineFeatures.value);

// Handle QR Generator toggle
const toggleQrGenerator = () => {
  emit('toggle-qr-generator');
};

// Handle Add New Medication
const openAddMedicationModal = () => {
  showAddMedicationModal.value = true;
};

// Handle medication save
const handleSaveMedications = (medications: NewMedicationFormData[]) => {
  console.log('Saving medications:', medications);
  // In a real app, this would call an API to save the medications

  // Show success notification
  notificationMessage.value = t('medication.addSuccess');
  showNotification.value = true;

  // Hide notification after 3 seconds
  setTimeout(() => {
    showNotification.value = false;
  }, 3000);
};

// Use router for direct navigation
const router = useRouter();

// Handle navigation to stock view with filters
const handleNavigateToStock = (status?: string) => {
  if (status === 'expiring_soon') {
    // Handle expiring soon as expiryStatus parameter
    router.push('/stock?expiryStatus=expiring_soon');
  } else {
    // Handle regular status parameters (including 'recent')
    const route = status ? `/stock?status=${status}` : '/stock';
    router.push(route);
  }
};

// Handle navigation to orders and invoices view with filters
const handleNavigateToOrders = (section?: string) => {
  if (section === 'invoices-today') {
    // Navigate to invoices tab with today filter
    const route = `/orders-invoices?tab=invoices&section=${section}`;
    router.push(route);
  } else if (section === 'revenue') {
    // Navigate to revenue tab
    const route = `/orders-invoices?tab=revenue`;
    router.push(route);
  } else {
    const route = section ? `/orders-invoices?tab=${section}` : '/orders-invoices';
    router.push(route);
  }
};

// Handle navigation to delivery orders view with filters
const handleNavigateToDelivery = (status?: string) => {
  const route = status ? `/orders-invoices?tab=delivery&status=${status}` : '/orders-invoices?tab=delivery';
  router.push(route);
};



// Handle navigation to app orders view with filters (redirect to orders-invoices with app-orders tab)
const handleNavigateToAppOrders = (section?: string) => {
  const route = section ? `/orders-invoices?tab=orders&section=${section}` : '/orders-invoices?tab=orders';
  router.push(route);
};

// Handle navigation to store orders view with filters
const handleNavigateToStoreOrders = (status?: string) => {
  const route = status ? `/stores?orderStatus=${status}` : '/stores';
  router.push(route);
};

// Fetch dashboard data on component mount
onMounted(async () => {
  try {
    // Always fetch dashboard data
    const dashboardResponse = await fetchDashboardData();

    // Handle dashboard data
    if (dashboardResponse.success && dashboardResponse.data) {
      dashboardData.value = dashboardResponse.data;
    }

    // Only fetch ads if online features are available
    if (canShowOnlineFeatures.value) {
      adsLoading.value = true;
      try {
        const adsData = await fetchAds(i18n.locale.value as 'ar' | 'en');
        ads.value = adsData;
      } catch (adsError) {
        console.error('Failed to fetch ads:', adsError);
        // Don't show ads if fetching fails
        ads.value = [];
      } finally {
        adsLoading.value = false;
      }
    } else {
      // Clear ads when offline
      ads.value = [];
      adsLoading.value = false;
    }
  } catch (err) {
    console.error('Failed to fetch dashboard data:', err);
  }
});

// Watch for online mode changes and fetch/clear ads accordingly
watch(canShowOnlineFeatures, async (canShow) => {
  if (canShow && ads.value.length === 0) {
    // Online mode enabled and no ads loaded - fetch them
    adsLoading.value = true;
    try {
      const adsData = await fetchAds(i18n.locale.value as 'ar' | 'en');
      ads.value = adsData;
    } catch (adsError) {
      console.error('Failed to fetch ads:', adsError);
      ads.value = [];
    } finally {
      adsLoading.value = false;
    }
  } else if (!canShow) {
    // Online mode disabled - clear ads
    ads.value = [];
    adsLoading.value = false;
  }
});
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert />

    <h2 class="text-2xl font-semibold text-gray-900 mb-6 text-start dark:text-gray-100">{{ t('app.welcome') }}</h2>

    <div v-if="error" class="bg-error-50 border border-error-200 rounded-lg p-4 mb-6 text-error-700 text-center"
      role="alert">
      <p class="mb-2">{{ error }}</p>
      <button @click="fetchDashboardData" class="btn-danger">
        {{ t('common.retry') }}
      </button>
    </div>

    <!-- Ad Slider - Only show when online features are available -->
    <AdSlider v-if="canShowOnlineFeatures" :ads="ads" :lang="i18n.locale.value as 'ar' | 'en'"
      :autoplay-interval="10000" :loading="adsLoading" />

    <!-- Dashboard Grid Layout -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 mb-8 text-start w-full">
      <!-- Inventory Summary Widget -->
      <InventorySummary :data="inventoryData" :loading="isLoading" @navigate-to-stock="handleNavigateToStock" />

      <!-- App Orders Widget -->
      <AppOrders :data="appOrdersData" :loading="isLoading" @navigate-to-app-orders="handleNavigateToAppOrders" />

      <!-- Today's Sales Widget -->
      <SalesSummary :data="salesData" :loading="isLoading" @navigate-to-orders="handleNavigateToOrders" />

      <!-- Store Orders Status Widget -->
      <StoreOrdersStatus :data="storeOrdersData" :loading="isLoading"
        @navigate-to-store-orders="handleNavigateToStoreOrders" />

      <!-- Delivery Orders Widget -->
      <DeliveryOrders :data="deliveryData" :loading="isLoading" @navigate-to-delivery="handleNavigateToDelivery" />

      <!-- Quick Actions Panel -->
      <QuickActions :loading="isLoading" @toggle-qr-generator="toggleQrGenerator"
        @add-new-medication="openAddMedicationModal" />

      <!-- Monthly Revenue Chart -->
      <RevenueChart :data="monthlyRevenueData" :loading="isLoading" />
    </div>

    <!-- Add New Medication Modal -->
    <AddMedicationModal :show="showAddMedicationModal" @close="showAddMedicationModal = false"
      @save="handleSaveMedications" />

    <!-- Success Notification -->
    <Notification :show="showNotification" :message="notificationMessage" type="success" :duration="3000"
      @close="showNotification = false" />
  </div>
</template>
