<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import OrderRequestModal from '../components/orders/OrderRequestModal.vue';
import Notification from '../components/Notification.vue';
import {
  initOrderRequestService,
  cleanupOrderRequestService,
  getActiveOrderRequests
} from '../services/orderRequestService';
import type { OrderRequest } from '../types/orderRequests';
import onlineModeStore from '../store/onlineModeStore';

const { t } = useI18n();

// State
const showModal = ref(false);
const selectedOrderRequest = ref<OrderRequest | null>(null);
const activeOrderRequests = getActiveOrderRequests();
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationType = ref<'success' | 'error' | 'info'>('info');

// Mock order request for demo
const createMockOrderRequest = (): OrderRequest => {
  const now = new Date();
  const orderTime = new Date(now.getTime() - Math.random() * 30 * 60 * 1000);

  return {
    id: `DEMO-${Date.now()}`,
    userId: 'USER-12345',
    userName: 'Ahmed Mohamed',
    userPhone: '+20 1234567890',
    serviceType: Math.random() > 0.5 ? 'Delivery' : 'Reserved',
    location: {
      address: '123 Tahrir Street, Downtown Cairo, Egypt',
      coordinates: {
        latitude: 30.0444,
        longitude: 31.2357
      }
    },
    pointsToEarn: 25,
    orderTime: orderTime.toISOString(),
    createdAt: orderTime.toISOString(),
    status: 'pending',
    medications: [
      {
        id: 1,
        medicationId: 101,
        name: 'Paracetamol 500mg',
        quantity: 2,
        unitPrice: 15.50,
        totalPrice: 31.00,
        status: 'available'
      },
      {
        id: 2,
        medicationId: 102,
        name: 'Amoxicillin 250mg',
        quantity: 1,
        unitPrice: 45.00,
        totalPrice: 45.00,
        status: 'locked',
        lockedSince: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        lockDurationHours: 8
      },
      {
        id: 3,
        medicationId: 103,
        name: 'Vitamin C 1000mg',
        quantity: 3,
        unitPrice: 12.00,
        totalPrice: 36.00,
        status: 'low_stock'
      }
    ],
    totalAmount: 112.00,
    notes: 'Please call before delivery. Patient is elderly.',
    estimatedDeliveryTime: new Date(now.getTime() + 2 * 60 * 60 * 1000).toISOString(),
    reservationExpiryTime: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString()
  };
};

// Event handlers
const handleNewOrderRequest = (event: CustomEvent) => {
  const { orderRequest } = event.detail;
  notificationMessage.value = t('orderRequests.newRequestNotification', {
    serviceType: t(`orderRequests.serviceTypes.${orderRequest.serviceType}`),
    userName: orderRequest.userName
  });
  notificationType.value = 'info';
  showNotification.value = true;
};

const openModal = (orderRequest: OrderRequest) => {
  // Check if online mode is enabled before opening modal
  if (!onlineModeStore.state.canUseOnlineFeatures.value) {
    console.log('Cannot open order request modal - online mode is disabled');
    notificationMessage.value = t('orderRequests.onlineModeRequired');
    notificationType.value = 'error';
    showNotification.value = true;
    return;
  }

  selectedOrderRequest.value = orderRequest;
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
  selectedOrderRequest.value = null;
};

const handleActionCompleted = (data: any) => {
  const { action } = data;

  let message = '';
  switch (action) {
    case 'accept':
      message = t('orderRequests.orderAccepted');
      break;
    case 'reject':
      message = t('orderRequests.orderRejected');
      break;
    case 'release':
      message = t('orderRequests.medicationReleased');
      break;
  }

  if (message) {
    notificationMessage.value = message;
    notificationType.value = 'success';
    showNotification.value = true;
  }
};

const createDemoOrder = () => {
  // Check if online mode is enabled before creating demo order
  if (!onlineModeStore.state.canUseOnlineFeatures.value) {
    console.log('Cannot create demo order - online mode is disabled');
    notificationMessage.value = t('orderRequests.onlineModeRequired');
    notificationType.value = 'error';
    showNotification.value = true;
    return;
  }

  const mockOrder = createMockOrderRequest();
  selectedOrderRequest.value = mockOrder;
  showModal.value = true;
};

// Lifecycle
onMounted(() => {
  window.addEventListener('newOrderRequest', handleNewOrderRequest as EventListener);
  initOrderRequestService();
});

onUnmounted(() => {
  window.removeEventListener('newOrderRequest', handleNewOrderRequest as EventListener);
  cleanupOrderRequestService();
});
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-slate-900 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          {{ t('orderRequests.title') }} Demo
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Test the order request modal functionality with mock data
        </p>
      </div>

      <!-- Demo Controls -->
      <div
        class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-slate-700 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Demo Controls
        </h2>
        <div class="flex gap-4">
          <button @click="createDemoOrder"
            class="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200">
            Create Demo Order Request
          </button>
        </div>
      </div>

      <!-- Active Order Requests -->
      <div class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-slate-700">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Active Order Requests ({{ activeOrderRequests.length }})
        </h2>

        <div v-if="activeOrderRequests.length === 0" class="text-center py-8">
          <p class="text-gray-500 dark:text-gray-400">
            {{ t('orderRequests.noActiveRequests') }}
          </p>
          <p class="text-sm text-gray-400 dark:text-gray-500 mt-2">
            Wait for automatic requests or create a demo order above
          </p>
        </div>

        <div v-else class="grid gap-4">
          <div v-for="orderRequest in activeOrderRequests" :key="orderRequest.id"
            class="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer"
            @click="openModal(orderRequest)">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="font-semibold text-gray-900 dark:text-white">
                  {{ orderRequest.userName }}
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {{ t(`orderRequests.serviceTypes.${orderRequest.serviceType}`) }} •
                  {{ orderRequest.medications.length }} medications
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-500">
                  {{ orderRequest.location.address }}
                </p>
              </div>
              <div class="text-right">
                <span
                  class="px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 text-sm rounded-full">
                  {{ orderRequest.pointsToEarn }} points
                </span>
                <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">
                  {{ new Date(orderRequest.orderTime).toLocaleTimeString() }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Request Modal -->
    <OrderRequestModal :show="showModal" :order-request="selectedOrderRequest" @close="closeModal"
      @action-completed="handleActionCompleted" />

    <!-- Notification -->
    <Notification :show="showNotification" :message="notificationMessage" :type="notificationType" :duration="4000"
      @close="showNotification = false" />
  </div>
</template>
