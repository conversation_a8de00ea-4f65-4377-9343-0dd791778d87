<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter, useRoute } from 'vue-router';
import Footer from '../components/Footer.vue';
import NotificationDetailsModal from '../components/NotificationDetailsModal.vue';
import OrderRequestModal from '../components/orders/OrderRequestModal.vue';
import Notification from '../components/Notification.vue';
import Tooltip from '../components/ui/Tooltip.vue';
import PackBadge from '../components/ui/PackBadge.vue';
import useOrderNotifications from '../composables/useOrderNotifications';
import orderPackStore from '../store/orderPackStore';
import {
  Squares2X2Icon,
  ArrowsRightLeftIcon,
  ArchiveBoxIcon,
  ScaleIcon,
  ShoppingBagIcon,
  StarIcon,
  ShoppingCartIcon,
  Cog6ToothIcon,
  BellIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BuildingStorefrontIcon
} from '@heroicons/vue/24/outline';
import {
  initOrderRequestService,
  cleanupOrderRequestService,
} from '../services/orderRequestService';
import type { OrderRequest } from '../types/orderRequests';
import onlineModeStore from '../store/onlineModeStore';

// Setup i18n
const { t } = useI18n();

const props = defineProps<{
  user: {
    userId: string;
    role: 'PharmacyOwner' | 'PharmacyUser';
    email?: string;
    deviceId?: string;
    token: string;
  } | null;
}>();

const emit = defineEmits(['logout']);

const router = useRouter();
const route = useRoute();

const showUserMenu = ref(false);
const showNotifications = ref(false);
const isMobileMenuOpen = ref(false);
const isMobileView = ref(window.innerWidth < 768);

// Notification modal state
const showNotificationModal = ref(false);
const selectedNotification = ref(null);

// Order request modal state
const showOrderRequestModal = ref(false);
const selectedOrderRequest = ref<OrderRequest | null>(null);
// const activeOrderRequests = getActiveOrderRequests();

// Order request notification state
const showOrderRequestNotification = ref(false);
const orderRequestNotificationMessage = ref('');
const orderRequestNotificationType = ref<'success' | 'error' | 'info'>('info');

// Order notifications composable for auto-accept functionality
const { handleNewOrderRequest: checkAutoAccept } = useOrderNotifications();

// Handle window resize for responsive behavior
const handleResize = () => {
  isMobileView.value = window.innerWidth < 768;
  if (!isMobileView.value) {
    isMobileMenuOpen.value = false;
  }
};

// Click outside handler for dropdowns
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as Element;

  // Check if click is outside notifications dropdown
  const notificationsDropdown = document.querySelector('.notifications-dropdown');
  const notificationsButton = document.querySelector('.notifications-button');
  if (showNotifications.value &&
    notificationsDropdown &&
    !notificationsDropdown.contains(target) &&
    !notificationsButton?.contains(target)) {
    showNotifications.value = false;
  }

  // Check if click is outside user menu dropdown
  const userDropdown = document.querySelector('.user-dropdown');
  const userButton = document.querySelector('.user-button');
  if (showUserMenu.value &&
    userDropdown &&
    !userDropdown.contains(target) &&
    !userButton?.contains(target)) {
    showUserMenu.value = false;
  }
};

// Order request event handlers
const handleNewOrderRequest = async (event: CustomEvent) => {
  const { orderRequest, notification } = event.detail;
  console.log('🚀 MainLayout: New order request event received for order:', orderRequest.id);

  // Check if online mode is enabled before processing order request
  if (!onlineModeStore.state.canUseOnlineFeatures.value) {
    console.log('❌ MainLayout: Order request received but online mode is disabled - ignoring');
    return;
  }

  console.log('🔍 MainLayout: Checking auto-accept for order:', orderRequest.id);

  // Check if auto-accept is enabled and process accordingly
  const wasAutoAccepted = await checkAutoAccept(orderRequest);

  console.log('📋 MainLayout: Auto-accept result:', wasAutoAccepted);

  if (wasAutoAccepted) {
    // Order was auto-accepted, don't show popup
    console.log('✅ MainLayout: Order was auto-accepted, popup skipped');
    return;
  }

  // Auto-accept disabled or failed, show popup as normal
  console.log('📱 MainLayout: Showing order request popup');

  // Show notification
  orderRequestNotificationMessage.value = t('orderRequests.newRequestNotification', {
    serviceType: t(`orderRequests.serviceTypes.${orderRequest.serviceType}`),
    userName: orderRequest.userName
  });
  orderRequestNotificationType.value = 'info';
  showOrderRequestNotification.value = true;

  // Auto-open modal for high priority requests
  if (notification.priority === 'high') {
    selectedOrderRequest.value = orderRequest;
    showOrderRequestModal.value = true;
  }
};

const closeOrderRequestModal = () => {
  showOrderRequestModal.value = false;
  selectedOrderRequest.value = null;
};

const handleOrderRequestAction = (data: any) => {
  const { action } = data;

  let message = '';
  switch (action) {
    case 'accept':
      message = t('orderRequests.orderAccepted');
      break;
    case 'reject':
      message = t('orderRequests.orderRejected');
      break;
    case 'release':
      message = t('orderRequests.medicationReleased');
      break;
  }

  if (message) {
    orderRequestNotificationMessage.value = message;
    orderRequestNotificationType.value = 'success';
    showOrderRequestNotification.value = true;
  }
};

// Handle auto-accept notifications
const handleAutoAcceptNotification = (event: CustomEvent) => {
  const { message, type, duration } = event.detail;
  orderRequestNotificationMessage.value = message;
  orderRequestNotificationType.value = type;
  showOrderRequestNotification.value = true;

  // Auto-close after duration
  if (duration) {
    setTimeout(() => {
      showOrderRequestNotification.value = false;
    }, duration);
  }
};

// Add and remove event listeners
onMounted(() => {
  window.addEventListener('resize', handleResize);
  document.addEventListener('click', handleClickOutside);
  window.addEventListener('newOrderRequest', handleNewOrderRequest as any);
  window.addEventListener('showNotification', handleAutoAcceptNotification as EventListener);

  // Initialize order request service
  initOrderRequestService();
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('newOrderRequest', handleNewOrderRequest as any);
  window.removeEventListener('showNotification', handleAutoAcceptNotification as EventListener);

  // Cleanup order request service
  cleanupOrderRequestService();
});

// Toggle mobile menu
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
  // Close other dropdowns when mobile menu is toggled
  if (isMobileMenuOpen.value) {
    showUserMenu.value = false;
    showNotifications.value = false;
  }
};

// Mock notifications - using i18n for localization
const notifications = ref([
  {
    id: 1,
    title: computed(() => t('notifications.mockNotifications.newMedicationTitle')),
    message: computed(() => t('notifications.mockNotifications.newMedicationMessage')),
    time: computed(() => t('notifications.mockNotifications.timeAgo.minutes', { count: 10 })),
    read: false,
    priority: 'medium',
    category: 'Inventory'
  },
  {
    id: 2,
    title: computed(() => t('notifications.mockNotifications.lowStockTitle')),
    message: computed(() => t('notifications.mockNotifications.lowStockMessage')),
    time: computed(() => t('notifications.mockNotifications.timeAgo.hour')),
    read: false,
    priority: 'high',
    category: 'Stock Alert'
  },
  {
    id: 3,
    title: computed(() => t('notifications.mockNotifications.systemUpdateTitle')),
    message: computed(() => t('notifications.mockNotifications.systemUpdateMessage')),
    time: computed(() => t('notifications.mockNotifications.timeAgo.hours', { count: 3 })),
    read: true,
    priority: 'low',
    category: 'System'
  }
]);

const handleLogout = () => {
  emit('logout');
};

const navigateTo = (route: string) => {
  router.push(route);
  // Close mobile menu when navigating
  if (isMobileView.value) {
    isMobileMenuOpen.value = false;
  }
};

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value;
  // Close notifications dropdown if open
  if (showNotifications.value) {
    showNotifications.value = false;
  }
};

const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value;
  // Close user menu dropdown if open
  if (showUserMenu.value) {
    showUserMenu.value = false;
  }
};

const markAllNotificationsAsRead = () => {
  notifications.value = notifications.value.map(notification => ({
    ...notification,
    read: true
  }));
};

// Notification modal handlers
const openNotificationModal = (notification: any) => {
  selectedNotification.value = notification;
  showNotificationModal.value = true;
  showNotifications.value = false; // Close dropdown when opening modal
};

const closeNotificationModal = () => {
  showNotificationModal.value = false;
  selectedNotification.value = null;
};

const markNotificationAsRead = (notificationId: number) => {
  notifications.value = notifications.value.map(notification =>
    notification.id === notificationId
      ? { ...notification, read: true }
      : notification
  );
};

const markNotificationAsUnread = (notificationId: number) => {
  notifications.value = notifications.value.map(notification =>
    notification.id === notificationId
      ? { ...notification, read: false }
      : notification
  );
};

// Sort notifications with unread first, then by time (newest first)
const sortedNotifications = computed(() => {
  return [...notifications.value].sort((a, b) => {
    // First sort by read status
    if (a.read !== b.read) {
      return a.read ? 1 : -1; // Unread notifications first
    }
    // Then sort by time (newest first)
    return b.id - a.id;
  });
});
</script>

<template>
  <div class="min-h-screen bg-gray-100 dark:bg-neutral-900 flex flex-col pb-16">

    <!-- Header/Nav Bar -->
    <header
      class="bg-white dark:bg-neutral-850 text-gray-800 dark:text-neutral-100 px-6 py-3 md:px-6 flex justify-between items-center shadow-light dark:shadow-dark-light fixed top-0 left-0 right-0 z-10 h-16 border-b border-gray-200 dark:border-neutral-700">
      <div class="flex items-center">
        <!-- Mobile menu toggle button - only visible on small screens -->
        <Tooltip v-if="isMobileView" :content="isMobileMenuOpen ? t('common.closeMenu') : t('common.openMenu')"
          position="bottom">
          <button @click="toggleMobileMenu"
            class="flex items-center justify-center bg-transparent border-none mr-3 p-2 cursor-pointer rounded-md hover:bg-gray-100 dark:hover:bg-neutral-800 transition-colors duration-200"
            :aria-expanded="isMobileMenuOpen" aria-controls="mobile-menu"
            :aria-label="isMobileMenuOpen ? t('common.closeMenu') : t('common.openMenu')">
            <Bars3Icon v-if="!isMobileMenuOpen" class="w-6 h-6" />
            <XMarkIcon v-else class="w-6 h-6" />
          </button>
        </Tooltip>

        <img src="../assets/images/logo.svg?v=teal" alt="DAWINII Logo" class="w-10 h-10 mr-3 md:w-10 md:h-10" />
        <h1
          class="text-xl font-bold text-primary-600 dark:text-primary-400 md:text-xl max-w-[150px] md:max-w-none whitespace-nowrap overflow-hidden text-ellipsis">
          {{ t('app.title') }}</h1>
      </div>

      <div class="flex items-center">
        <!-- Notifications Bell -->
        <div class="relative text-start">
          <Tooltip :content="t('header.notifications')" position="bottom">
            <button @click="toggleNotifications"
              class="notifications-button bg-transparent border-none cursor-pointer flex items-center justify-center relative p-2 rounded-md mr-2 transition-colors duration-200 hover:bg-gray-100 dark:hover:bg-neutral-800"
              aria-label="Notifications">
              <BellIcon class="w-6 h-6 text-gray-600 dark:text-gray-300" />
              <span v-if="notifications.filter(n => !n.read).length > 0"
                class="absolute top-0 right-0 bg-error-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-bold">
                {{notifications.filter(n => !n.read).length}}
              </span>
            </button>
          </Tooltip>

          <!-- Notifications Dropdown -->
          <div v-if="showNotifications"
            class="notifications-dropdown absolute right-0 rtl:right-auto rtl:left-0 top-full mt-2 bg-white dark:bg-neutral-850 rounded-lg shadow-medium dark:shadow-dark-medium w-88 max-h-[30rem] flex flex-col z-50 overflow-hidden border border-gray-200 dark:border-neutral-700 text-start rtl:text-right">
            <div class="p-4 border-b border-gray-200 dark:border-neutral-700">
              <h3 class="text-base font-semibold m-0 text-gray-800 dark:text-gray-100">{{ t('notifications.title') }}
              </h3>
              <button @click="markAllNotificationsAsRead"
                class="bg-transparent border-none text-primary-600 dark:text-primary-400 text-xs cursor-pointer p-0">
                {{ t('notifications.markAllAsRead') }}
              </button>
            </div>

            <div class="overflow-y-auto max-h-80">
              <div v-for="notification in sortedNotifications" :key="notification.id"
                @click="openNotificationModal(notification)"
                class="p-4 border-b border-gray-200 dark:border-neutral-700 transition-colors duration-200 cursor-pointer"
                :class="{
                  'bg-teal-50 dark:bg-teal-900/20 hover:bg-teal-100 dark:hover:bg-teal-800/30': !notification.read,
                  'hover:bg-gray-50 dark:hover:bg-neutral-800': notification.read
                }">
                <div class="flex flex-col">
                  <h4 class="text-sm font-semibold mb-1 text-gray-800 dark:text-gray-100">{{ notification.title }}</h4>
                  <p class="text-xs text-gray-600 dark:text-gray-300 mb-2">{{ notification.message }}</p>
                  <span class="text-xs text-gray-500 dark:text-gray-400">{{ notification.time }}</span>
                </div>
              </div>
            </div>

            <div class="p-3 border-t border-gray-200 dark:border-neutral-700 text-center">
              <button @click="navigateTo('/notifications')"
                class="bg-gray-100 dark:bg-neutral-800 text-gray-800 dark:text-neutral-200 border-none py-2 px-4 rounded-md text-sm cursor-pointer transition-colors duration-200 w-full hover:bg-gray-200 dark:hover:bg-neutral-700">
                {{ t('notifications.viewAll') }}
              </button>
            </div>
          </div>
        </div>

        <!-- User Profile Menu -->
        <div class="relative">
          <Tooltip :content="t('header.userMenu')" position="bottom">
            <button @click="toggleUserMenu"
              class="user-button flex items-center bg-transparent border-none cursor-pointer p-2 rounded-md transition-colors duration-200 hover:bg-gray-100 dark:hover:bg-neutral-800"
              aria-label="User menu" :aria-expanded="showUserMenu">
              <UserCircleIcon class="w-6 h-6 text-gray-600 dark:text-gray-300" />
            </button>
          </Tooltip>

          <!-- User Dropdown Menu -->
          <div v-if="showUserMenu"
            class="user-dropdown absolute right-0 rtl:right-auto rtl:left-0 top-full mt-2 bg-white dark:bg-neutral-850 rounded-lg shadow-medium dark:shadow-dark-medium w-72 z-50 overflow-hidden border border-gray-200 dark:border-neutral-700 text-start rtl:text-right">
            <div class="p-4 border-b border-gray-200 dark:border-neutral-700">
              <h3 class="text-base font-semibold m-0 text-gray-800 dark:text-gray-100">{{ props.user?.email ||
                t('user.userAccount') }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ props.user?.role === 'PharmacyOwner' ?
                t('user.owner') : t('user.staff') }}</p>
            </div>

            <div class="p-2">
              <button @click="navigateTo('/profile')"
                class="flex items-center w-full p-3 bg-transparent border-none cursor-pointer rounded-md transition-colors duration-200 text-gray-800 dark:text-gray-200 text-left rtl:text-right  hover:bg-gray-100 dark:hover:bg-slate-700">
                <UserCircleIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-gray-600 dark:text-gray-400" />
                <span>{{ t('user.profile') }}</span>
              </button>

              <button @click="navigateTo('/settings')"
                class="flex items-center w-full p-3 bg-transparent border-none cursor-pointer rounded-md transition-colors duration-200 text-gray-800 dark:text-gray-200 text-left rtl:text-right  hover:bg-gray-100 dark:hover:bg-slate-700">
                <Cog6ToothIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-gray-600 dark:text-gray-400" />
                <span>{{ t('user.settings') }}</span>
              </button>

              <button @click="handleLogout"
                class="flex items-center w-full p-3 bg-transparent border-none cursor-pointer rounded-md transition-colors duration-200 text-error-600 dark:text-error-400 text-left rtl:text-right  hover:bg-gray-100 dark:hover:bg-slate-700">
                <ArrowRightOnRectangleIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 text-error-600 dark:text-error-400" />
                <span>{{ t('user.logout') }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex mt-16 flex-1">
      <!-- Sidebar - Desktop version -->
      <aside
        class="w-64 bg-white dark:bg-slate-800 border-r rtl:border-r-0 rtl:border-l border-gray-200 dark:border-slate-700 h-[calc(100dvh-126px)] fixed top-16 left-0 rtl:left-auto rtl:right-0 overflow-y-auto z-[20] transition-transform duration-300 ease-in-out"
        :class="{
          'transform -translate-x-full rtl:translate-x-full': !isMobileMenuOpen && isMobileView,
          'transform translate-x-0 rtl:translate-x-0': isMobileMenuOpen && isMobileView,
          'w-4/5 max-w-64': isMobileView
        }" id="mobile-menu">
        <nav class="flex flex-col h-full justify-between">
          <ul class="list-none p-2 m-0 space-y-1">
            <router-link to="/dashboard" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right "
                :class="isActive ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-neutral-800 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <Squares2X2Icon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 flex-shrink-0"
                  :class="isActive ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                <span class="text-sm">{{ t('home.dashboard') }}</span>
              </li>
            </router-link>
            <router-link to="/import-export" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right "
                :class="isActive ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-slate-700 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <ArrowsRightLeftIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 flex-shrink-0"
                  :class="isActive ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                <span class="text-sm">{{ t('home.importExport') }}</span>
              </li>
            </router-link>
            <router-link to="/stock" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right "
                :class="isActive ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-neutral-800 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <ArchiveBoxIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 flex-shrink-0"
                  :class="isActive ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                <span class="text-sm">{{ t('home.viewStock') }}</span>
              </li>
            </router-link>
            <router-link to="/stores" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right "
                :class="isActive ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-neutral-800 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <BuildingStorefrontIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 flex-shrink-0"
                  :class="isActive ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                <span class="text-sm">{{ t('stores.title') }}</span>
              </li>
            </router-link>

            <router-link to="/cart" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right relative"
                :class="(isActive || route.path === '/checkout') ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-neutral-800 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <div class="relative mr-3 rtl:mr-0 rtl:ml-3">
                  <ShoppingCartIcon class="w-5 h-5 flex-shrink-0"
                    :class="(isActive || route.path === '/checkout') ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                  <PackBadge :count="orderPackStore.activePackCount.value" variant="error" size="sm"
                    position="bottom-right" />
                </div>
                <span class="text-sm">{{ t('home.cart') }}</span>
              </li>
            </router-link>
            <router-link to="/pharmacy-balance" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right "
                :class="isActive ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-slate-700 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <ScaleIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 flex-shrink-0"
                  :class="isActive ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                <span class="text-sm">{{ t('home.pharmacyBalance') }}</span>
              </li>
            </router-link>
            <router-link to="/orders-invoices" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right "
                :class="isActive ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-slate-700 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <ShoppingBagIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 flex-shrink-0"
                  :class="isActive ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                <span class="text-sm">{{ t('orders.title') }}</span>
              </li>
            </router-link>

            <router-link to="/reviews" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right "
                :class="isActive ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-slate-700 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <StarIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 flex-shrink-0"
                  :class="isActive ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                <span class="text-sm">{{ t('reviews.title') }}</span>
              </li>
            </router-link>
          </ul>

          <!-- Settings and Profile at the bottom of sidebar -->
          <div class="p-4 mt-auto border-t border-gray-200 dark:border-slate-700 space-y-1"
            :class="{ 'border-t-0': isMobileView }">
            <router-link to="/profile" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right "
                :class="isActive ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-slate-700 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <UserCircleIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 flex-shrink-0"
                  :class="isActive ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                <span class="text-sm">{{ t('user.profile') }}</span>
              </li>
            </router-link>
            <router-link to="/settings" custom v-slot="{ navigate, isActive }">
              <li
                class="flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-700 dark:text-gray-200 font-medium text-start rtl:text-right "
                :class="isActive ? 'bg-primary-600 text-white dark:bg-primary-700 dark:text-white shadow-light' : 'hover:bg-gray-50 dark:hover:bg-slate-700 hover:-translate-y-0.5 hover:shadow-light hover:text-primary-600 dark:hover:text-primary-400'"
                @click="navigate">
                <Cog6ToothIcon class="w-5 h-5 mr-3 rtl:mr-0 rtl:ml-3 flex-shrink-0"
                  :class="isActive ? 'text-white' : 'text-primary-600 dark:text-primary-400'" />
                <span class="text-sm">{{ t('home.settings') }}</span>
              </li>
            </router-link>
          </div>
        </nav>
      </aside>

      <!-- Overlay for mobile menu -->
      <div v-if="isMobileMenuOpen && isMobileView"
        class="fixed top-16 left-0 right-0 bottom-0 bg-black bg-opacity-50 z-[15]" @click="isMobileMenuOpen = false"
        aria-hidden="true"></div>

      <!-- Main Content Area -->
      <main class="flex-1 ml-0 md:ml-64 rtl:ml-0 rtl:md:mr-64 relative min-h-[calc(100vh-4rem)]">
        <div class="p-6 pb-20">
          <!-- Router view will render the current route component -->
          <router-view :user="props.user" />
        </div>
        <Footer />
      </main>
    </div>

    <!-- Notification Details Modal -->
    <NotificationDetailsModal :notification="selectedNotification" :show="showNotificationModal"
      @close="closeNotificationModal" @mark-as-read="markNotificationAsRead"
      @mark-as-unread="markNotificationAsUnread" />

    <!-- Order Request Modal -->
    <OrderRequestModal :show="showOrderRequestModal" :order-request="selectedOrderRequest"
      @close="closeOrderRequestModal" @action-completed="handleOrderRequestAction" />

    <!-- Order Request Notification -->
    <Notification :show="showOrderRequestNotification" :message="orderRequestNotificationMessage"
      :type="orderRequestNotificationType" :duration="4000" @close="showOrderRequestNotification = false" />
  </div>
</template>
