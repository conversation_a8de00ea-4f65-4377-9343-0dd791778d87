<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import QrGenerator from '../components/QrGenerator.vue';
import Notification from '../components/Notification.vue';
import { useTheme } from '../theme/useTheme';
import {
  ArrowPathIcon,
  ClockIcon,
  DevicePhoneMobileIcon
} from '@heroicons/vue/24/outline';

// Setup i18n and theme
const { t } = useI18n();
const { isDarkMode } = useTheme();

// Reactive state
const showSuccessNotification = ref(false);
const isSimulatingLogin = ref(false);
const roomCode = ref('');
const qrGeneratorRef = ref<InstanceType<typeof QrGenerator>>();
const expiryMinutes = ref(15);
const countdownInterval = ref<number | null>(null);

// Mock user data for login simulation
const mockUserData = {
  userId: 'ahmed_hassan',
  role: 'PharmacyOwner' as const,
  email: '<EMAIL>',
  deviceId: 'desktop_device',
  token: 'mock_token_' + Math.random().toString(36).substring(2)
};

// Emit events
const emit = defineEmits(['login-success']);

// Handle room code generation
const handleRoomCodeGenerated = (code: string) => {
  roomCode.value = code;
  startCountdown();
};

// Start countdown timer
const startCountdown = () => {
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value);
  }

  expiryMinutes.value = 15;
  countdownInterval.value = setInterval(() => {
    expiryMinutes.value--;
    if (expiryMinutes.value <= 0) {
      clearInterval(countdownInterval.value!);
      refreshCode();
    }
  }, 60000); // Update every minute
};

// Refresh QR code and room code
const refreshCode = () => {
  qrGeneratorRef.value?.refreshCode();
};

// Simulate QR code scan and login (for demo purposes)
const simulateLogin = () => {
  if (isSimulatingLogin.value) return;

  isSimulatingLogin.value = true;

  // Simulate a 3-second delay for the QR code scan
  setTimeout(() => {
    // Show success notification
    showSuccessNotification.value = true;

    // Wait for notification to be visible before redirecting
    setTimeout(() => {
      // Emit login event to parent component with mock user data
      emit('login-success', mockUserData);
      isSimulatingLogin.value = false;
    }, 1500);
  }, 3000);
};

// Auto-simulate login after 10 seconds for demo
onMounted(() => {
  setTimeout(() => {
    simulateLogin();
  }, 10000);
});

// Cleanup
onUnmounted(() => {
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value);
  }
});

// Handle notification close
const handleNotificationClose = () => {
  showSuccessNotification.value = false;
};

// Format room code for display
const formattedRoomCode = computed(() => {
  if (!roomCode.value) return '';
  return roomCode.value.match(/.{1,4}/g)?.join('-') || roomCode.value;
});
</script>

<template>
  <div class="min-h-screen w-full bg-gray-50 dark:bg-slate-900 transition-colors duration-200">
    <!-- Main Container -->
    <div class="min-h-screen flex flex-col lg:flex-row">

      <!-- Left Section - Instructions & Tips -->
      <div class="lg:w-1/2 flex flex-col justify-center px-6 py-12 lg:px-12 xl:px-16">
        <div class="max-w-md mx-auto lg:mx-0">

          <!-- Logo & Header -->
          <div class="text-center lg:text-start mb-8">
            <div class="flex justify-center lg:justify-start mb-6">
              <img src="../assets/images/logo.svg?v=teal" alt="DAWINII Logo" class="w-20 h-20" />
            </div>
            <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-3">
              {{ t('login.title') }}
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-300">
              {{ t('login.subtitle') }}
            </p>
          </div>

          <!-- Tips Section -->
          <div class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-slate-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
              <DevicePhoneMobileIcon class="w-5 h-5 text-primary-600 dark:text-primary-400" />
              {{ t('login.tips.title') }}
            </h3>

            <!-- Step-by-step instructions -->
            <div class="space-y-4">
              <div class="flex items-start gap-4">
                <div
                  class="flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                  <span class="text-lg">📱</span>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ t('login.tips.step1') }}</p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div
                  class="flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                  <span class="text-lg">⚙️</span>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ t('login.tips.step2') }}</p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div
                  class="flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                  <span class="text-lg">➕</span>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ t('login.tips.step3') }}</p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div
                  class="flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                  <span class="text-lg">📷</span>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ t('login.tips.step4') }}</p>
                </div>
              </div>
            </div>

            <!-- Security notes -->
            <div class="mt-6 pt-4 border-t border-gray-200 dark:border-slate-600">
              <div class="space-y-3">
                <div class="flex items-start gap-3">
                  <div
                    class="flex-shrink-0 w-6 h-6 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center">
                    <span class="text-xs">🔐</span>
                  </div>
                  <p class="text-xs text-gray-600 dark:text-gray-400">{{ t('login.tips.deviceLimit') }}</p>
                </div>
                <div class="flex items-start gap-3">
                  <div
                    class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                    <span class="text-xs">📶</span>
                  </div>
                  <p class="text-xs text-gray-600 dark:text-gray-400">{{ t('login.tips.subscriptionNote') }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Section - QR Code & Room Code -->
      <div
        class="lg:w-1/2 flex flex-col justify-center items-center px-6 py-12 lg:px-12 bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 lg:shadow-xl">
        <div class="w-full max-w-md">

          <!-- QR Code Section -->
          <div class="text-center mb-8">
            <div class="mb-6">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {{ t('login.scanQr') }}
              </h2>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ t('login.qrInstructions') }}
              </p>
            </div>

            <!-- QR Code Container with enhanced styling -->
            <div class="flex justify-center mb-6">
              <div class="relative">
                <div
                  class="absolute inset-0 bg-gradient-to-r from-primary-400 to-primary-600 rounded-2xl blur opacity-20">
                </div>
                <div class="relative">
                  <QrGenerator ref="qrGeneratorRef" :isDarkMode="isDarkMode"
                    @roomCodeGenerated="handleRoomCodeGenerated" />
                </div>
              </div>
            </div>

            <!-- Status with enhanced styling -->
            <div
              class="inline-flex items-center gap-2 px-4 py-2 bg-primary-50 dark:bg-primary-900/20 rounded-full text-sm text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800">
              <div class="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
              <span>{{ t('login.waitingForConnection') }}</span>
            </div>
          </div>

          <!-- Room Code Section with enhanced design -->
          <div
            class="bg-gradient-to-br from-gray-50 to-white dark:from-slate-700 dark:to-slate-800 rounded-2xl p-6 border border-gray-200 dark:border-slate-600 shadow-sm">
            <div class="text-center">
              <div class="flex items-center justify-center gap-2 mb-3">
                <div
                  class="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                  <span class="text-sm">🔑</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                  {{ t('login.roomCode') }}
                </h3>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                {{ t('login.roomCodeInstructions') }}
              </p>

              <!-- Room Code Display with enhanced styling -->
              <div class="relative mb-6">
                <div
                  class="absolute inset-0 bg-gradient-to-r from-primary-100 to-primary-50 dark:from-primary-900/20 dark:to-primary-800/20 rounded-xl">
                </div>
                <div
                  class="relative bg-white dark:bg-slate-800 rounded-xl p-6 border-2 border-primary-200 dark:border-primary-800">
                  <div class="text-3xl font-mono font-bold text-primary-600 dark:text-primary-400 tracking-wider">
                    {{ formattedRoomCode || '••••-••••' }}
                  </div>
                </div>
              </div>

              <!-- Expiry & Refresh with enhanced styling -->
              <div class="flex items-center justify-between p-3 bg-gray-100 dark:bg-slate-700 rounded-lg">
                <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <ClockIcon class="w-4 h-4" />
                  <span>{{ t('login.codeExpires', { minutes: expiryMinutes }) }}</span>
                </div>
                <button @click="refreshCode"
                  class="flex items-center gap-2 px-3 py-1.5 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-all duration-200 hover:shadow-md">
                  <ArrowPathIcon class="w-4 h-4" />
                  {{ t('login.refreshCode') }}
                </button>
              </div>
            </div>
          </div>

          <!-- Loading Indicator with enhanced styling -->
          <div v-if="isSimulatingLogin" class="mt-8 text-center">
            <div
              class="inline-flex items-center gap-3 px-6 py-3 bg-green-50 dark:bg-green-900/20 rounded-full border border-green-200 dark:border-green-800">
              <div
                class="animate-spin rounded-full h-5 w-5 border-2 border-green-300 border-t-green-600 dark:border-green-700 dark:border-t-green-400">
              </div>
              <span class="text-sm font-medium text-green-700 dark:text-green-300">{{ t('login.authenticating')
                }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Notification -->
    <Notification :show="showSuccessNotification" :message="t('login.loginSuccess')" type="success" :duration="3000"
      @close="handleNotificationClose" />
  </div>
</template>
