<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  BellIcon,
  BellAlertIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/vue/24/outline';
import { useRTL } from '../composables/useRTL';
import SearchFilter from '../components/ui/SearchFilter.vue';
import SkeletonLoader from '../components/ui/SkeletonLoader.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import NotificationDetailsModal from '../components/NotificationDetailsModal.vue';
import type { Notification, NotificationType, NotificationFilter, NotificationSearch, NotificationPagination } from '../types/notifications';
import { fetchNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '../services/notificationService';

// Props (passed from App.vue router-view but not used in this component)
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}

defineProps<{
  user?: User | null;
  isLoggedIn?: boolean;
}>();

// Setup i18n
const { t } = useI18n();
const { getNavigationArrow } = useRTL();

// Emits
const emit = defineEmits(['navigate']);

// State for notifications data
const notifications = ref<Notification[]>([]);
const isLoading = ref(false);
const error = ref<string | null>(null);

// Filter state
const filter = reactive<NotificationFilter>({
  type: 'all'
});

// Search state
const search = reactive<NotificationSearch>({
  query: ''
});

// Filter configuration for SearchFilter component
const filterConfig = {
  type: {
    label: t('notifications.filterByType'),
    options: [
      { value: 'all', label: t('notifications.notificationTypes.all') },
      { value: 'order', label: t('notifications.notificationTypes.order') },
      { value: 'medication', label: t('notifications.notificationTypes.medication') },
      { value: 'stock', label: t('notifications.notificationTypes.stock') },
      { value: 'system', label: t('notifications.notificationTypes.system') }
    ],
    multiSelect: false
  }
};

// Pagination state
const pagination = reactive<NotificationPagination>({
  currentPage: 1,
  itemsPerPage: 10,
  totalItems: 0
});

// UI state
const showFilters = ref(false);

// Notification modal state
const showNotificationModal = ref(false);
const selectedNotification = ref<Notification | null>(null);

// Fetch notifications data
const fetchNotificationsData = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    const result = await fetchNotifications();
    if (result.success && result.data) {
      notifications.value = result.data;
      pagination.totalItems = result.data.length;
    } else {
      error.value = result.error || 'Failed to fetch notifications';
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred';
  } finally {
    isLoading.value = false;
  }
};

// Filter notifications based on current filters and search
const filteredNotifications = computed(() => {
  let result = [...notifications.value];

  // Apply type filter
  if (filter.type !== 'all') {
    result = result.filter(notification => notification.type === filter.type);
  }

  // Apply search
  if (search.query) {
    const query = search.query.toLowerCase();
    result = result.filter(
      notification =>
        notification.title.toLowerCase().includes(query) ||
        notification.message.toLowerCase().includes(query)
    );
  }

  // Sort by read status first (unread first), then by time (newest first)
  result.sort((a, b) => {
    // First sort by read status
    if (a.read !== b.read) {
      return a.read ? 1 : -1; // Unread notifications first
    }
    // Then sort by time (newest first)
    return b.id - a.id;
  });

  return result;
});

// Paginated notifications
const paginatedNotifications = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.itemsPerPage;
  const end = start + pagination.itemsPerPage;
  return filteredNotifications.value.slice(start, end);
});

// Total pages
const totalPages = computed(() => {
  return Math.ceil(filteredNotifications.value.length / pagination.itemsPerPage);
});

// Get RTL-aware arrow icons
const previousArrowIcon = computed(() => {
  const iconName = getNavigationArrow('previous');
  return iconName === 'ChevronLeftIcon' ? ChevronLeftIcon : ChevronRightIcon;
});

const nextArrowIcon = computed(() => {
  const iconName = getNavigationArrow('next');
  return iconName === 'ChevronLeftIcon' ? ChevronLeftIcon : ChevronRightIcon;
});

// Handle filter change
const handleFilterChange = (type: 'all' | NotificationType) => {
  filter.type = type;
  // Reset to first page when filter changes
  pagination.currentPage = 1;
  // Close filter dropdown
  showFilters.value = false;
};

// Handle search
const handleSearch = () => {
  // Reset to first page when search changes
  pagination.currentPage = 1;
};

// Handle pagination
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    pagination.currentPage = page;
  }
};

// Handle notification click - open modal
const handleNotificationClick = (notification: Notification) => {
  selectedNotification.value = notification;
  showNotificationModal.value = true;
};

// Notification modal handlers
const closeNotificationModal = () => {
  showNotificationModal.value = false;
  selectedNotification.value = null;
};

const markNotificationAsReadFromModal = async (notificationId: number) => {
  await markNotificationAsRead(notificationId);
  // Update the notification in the local state
  const index = notifications.value.findIndex(n => n.id === notificationId);
  if (index !== -1) {
    notifications.value[index].read = true;
  }
  // Update selected notification if it's the same one
  if (selectedNotification.value?.id === notificationId) {
    selectedNotification.value.read = true;
  }
};

const markNotificationAsUnreadFromModal = async (notificationId: number) => {
  // Note: You'll need to implement markNotificationAsUnread in the service
  // For now, we'll just update the local state
  const index = notifications.value.findIndex(n => n.id === notificationId);
  if (index !== -1) {
    notifications.value[index].read = false;
  }
  // Update selected notification if it's the same one
  if (selectedNotification.value?.id === notificationId) {
    selectedNotification.value.read = false;
  }
};

// Mark all as read
const handleMarkAllAsRead = async () => {
  await markAllNotificationsAsRead();
  // Update all notifications in the local state
  notifications.value.forEach(notification => {
    notification.read = true;
  });
};

// Refresh notifications
const refreshNotifications = async () => {
  await fetchNotificationsData();
};

// Toggle filters
const toggleFilters = () => {
  showFilters.value = !showFilters.value;
};

// Handle search from SearchFilter component
const handleSearchFilterSearch = (searchValue: string) => {
  search.query = searchValue;
  pagination.currentPage = 1;
};

// Handle filter change from SearchFilter component
const handleSearchFilterChange = (filterType: string, value: string | string[]) => {
  if (filterType === 'type') {
    filter.type = Array.isArray(value) ? value[0] as NotificationType : value as NotificationType;
  }
  pagination.currentPage = 1;
};

// Handle reset from SearchFilter component
const handleSearchFilterReset = () => {
  filter.type = 'all';
  search.query = '';
  pagination.currentPage = 1;
};

// Fetch data on component mount
onMounted(async () => {
  await fetchNotificationsData();
});
</script>

<template>
  <div class="w-full text-start">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <h1 class="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-6">{{ t('notifications.title') }}</h1>

    <!-- Search and Filter Bar -->
    <div class="flex flex-col lg:flex-row gap-4 mb-6">
      <div class="flex-1">
        <SearchFilter v-model:search="search.query" v-model:filters="filter" :filter-config="filterConfig"
          :placeholder="t('notifications.searchNotifications')" :loading="isLoading" @search="handleSearchFilterSearch"
          @filter-change="handleSearchFilterChange" @reset="handleSearchFilterReset" />
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center gap-3">
        <button @click="refreshNotifications"
          class="flex items-center justify-center p-2.5 bg-gray-50 dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded-lg text-gray-900 dark:text-white cursor-pointer transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 hover:-translate-y-0.5 shadow-sm hover:shadow-md"
          aria-label="Refresh notifications">
          <SkeletonLoader v-if="isLoading" type="circle" width="w-5" height="h-5" />
          <ArrowPathIcon v-else class="w-5 h-5" aria-hidden="true" />
        </button>

        <button @click="handleMarkAllAsRead"
          class="flex items-center gap-2 px-4 py-2.5 bg-primary-600 hover:bg-primary-700 text-white border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 hover:-translate-y-0.5 shadow-sm hover:shadow-md"
          aria-label="Mark all as read">
          <CheckCircleIcon class="w-4 h-4" aria-hidden="true" />
          <span>{{ t('notifications.markAllAsRead') }}</span>
        </button>
      </div>
    </div>

    <!-- Notifications List -->
    <div
      class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-xl shadow-sm overflow-hidden">
      <!-- Loading State -->
      <div v-if="isLoading" class="p-8">
        <SkeletonLoader type="list" :rows="6" />
      </div>

      <!-- Error State -->
      <div v-else-if="error"
        class="flex flex-col items-center justify-center p-12 text-center text-gray-500 dark:text-gray-400">
        <p class="mb-4">{{ error }}</p>
        <button @click="refreshNotifications"
          class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200">
          {{ t('common.retry') }}
        </button>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredNotifications.length === 0"
        class="flex flex-col items-center justify-center p-12 text-center text-gray-500 dark:text-gray-400">
        <BellIcon class="w-12 h-12 text-gray-400 dark:text-gray-500 mb-4" aria-hidden="true" />
        <p>{{ t('notifications.noNotifications') }}</p>
      </div>

      <!-- Notifications List -->
      <div v-else class="flex flex-col">
        <div v-for="notification in paginatedNotifications" :key="notification.id"
          class="flex p-5 border-b border-gray-200 dark:border-neutral-700 last:border-b-0 cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-neutral-800/50"
          :class="!notification.read ? 'bg-teal-50/50 dark:bg-teal-900/10' : 'bg-white dark:bg-neutral-850'"
          @click="handleNotificationClick(notification)">
          <div class="mr-4 flex items-start">
            <BellAlertIcon v-if="!notification.read" class="w-6 h-6 text-primary-600 dark:text-primary-400"
              aria-hidden="true" />
            <BellIcon v-else class="w-6 h-6 text-gray-400 dark:text-gray-500" aria-hidden="true" />
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="text-base font-semibold text-gray-900 dark:text-white mb-2">{{ notification.title }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300 mb-3 leading-relaxed">{{ notification.message }}</p>
            <div class="flex justify-between items-center text-xs">
              <span class="text-gray-500 dark:text-gray-400">{{ notification.time }}</span>
              <span
                class="px-2 py-1 bg-gray-100 dark:bg-neutral-800 text-gray-700 dark:text-neutral-300 rounded-full font-medium">
                {{ t(`notifications.notificationTypes.${notification.type}`) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="filteredNotifications.length > 0"
        class="flex flex-col sm:flex-row justify-between items-center p-4 border-t border-gray-200 dark:border-neutral-700 gap-4">
        <div class="flex items-center gap-4">
          <button @click="goToPage(pagination.currentPage - 1)"
            class="flex items-center gap-1 px-3 py-2 bg-gray-50 dark:bg-neutral-800 border border-gray-300 dark:border-neutral-700 rounded-md text-gray-900 dark:text-neutral-100 text-sm cursor-pointer transition-all duration-200 hover:bg-gray-100 dark:hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="pagination.currentPage === 1">
            <component :is="previousArrowIcon" class="w-4 h-4" aria-hidden="true" />
            <span>{{ t('stock.pagination.previous') }}</span>
          </button>

          <div class="flex items-center gap-1 text-sm text-gray-700 dark:text-gray-300">
            {{ t('stock.pagination.showing') }} {{ (pagination.currentPage - 1) * pagination.itemsPerPage + 1 }}
            {{ t('stock.pagination.to') }}
            {{ Math.min(pagination.currentPage * pagination.itemsPerPage, filteredNotifications.length) }}
            {{ t('stock.pagination.of') }} {{ filteredNotifications.length }}
            {{ t('stock.pagination.results') }}
          </div>

          <button @click="goToPage(pagination.currentPage + 1)"
            class="flex items-center gap-1 px-3 py-2 bg-gray-50 dark:bg-neutral-800 border border-gray-300 dark:border-neutral-700 rounded-md text-gray-900 dark:text-neutral-100 text-sm cursor-pointer transition-all duration-200 hover:bg-gray-100 dark:hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="pagination.currentPage === totalPages">
            <span>{{ t('stock.pagination.next') }}</span>
            <component :is="nextArrowIcon" class="w-4 h-4" aria-hidden="true" />
          </button>
        </div>

        <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
          <span>{{ t('stock.itemsPerPage') }}:</span>
          <select v-model="pagination.itemsPerPage"
            class="px-2 py-1 border border-gray-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-850 text-gray-900 dark:text-neutral-100 text-sm">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Notification Details Modal -->
    <NotificationDetailsModal :notification="selectedNotification" :show="showNotificationModal"
      @close="closeNotificationModal" @mark-as-read="markNotificationAsReadFromModal"
      @mark-as-unread="markNotificationAsUnreadFromModal" />
  </div>
</template>
