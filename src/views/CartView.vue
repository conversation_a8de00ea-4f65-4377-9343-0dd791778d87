<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import Notification from '../components/Notification.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import OrderPackSelector from '../components/cart/OrderPackSelector.vue';
import {
  ShoppingCartIcon,
  TrashIcon,
  PlusIcon,
  MinusIcon,
  ArrowLeftIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';
import orderPackStore from '../store/orderPackStore';
import { useRouter } from 'vue-router';

// Props (passed from App.vue router-view but not used in this component)
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}

defineProps<{
  user?: User | null;
  isLoggedIn?: boolean;
}>();

// Setup i18n for translations
const { t } = useI18n();

// Get router
const router = useRouter();

// Notification state
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationType = ref<'success' | 'error' | 'info'>('success');

// Get order pack store
const {
  state: orderPackState,
  getCurrentPack,
  updateQuantityInCurrentPack,
  removeFromCurrentPack,
  clearCurrentPack,
  switchToPack,
  addLocalOrder,
  cancelPack,
  activePacks,
  canAddPacks,
  maxPacksReached
} = orderPackStore;

// Computed properties
const currentPack = computed(() => getCurrentPack());
const hasItems = computed(() => (currentPack.value?.items.length || 0) > 0);
const shouldShowPackWarning = computed(() => activePacks.value.length > 5);

// Format price for display
const formatPrice = (price: number) => {
  return new Intl.NumberFormat(undefined, {
    style: 'currency',
    currency: 'USD'
  }).format(price);
};

// Increase quantity
const increaseQuantity = (id: number, currentQuantity: number) => {
  updateQuantityInCurrentPack(id, currentQuantity + 1);
};

// Decrease quantity
const decreaseQuantity = (id: number, currentQuantity: number) => {
  if (currentQuantity > 1) {
    updateQuantityInCurrentPack(id, currentQuantity - 1);
  }
};

// Remove item
const removeItem = (id: number) => {
  removeFromCurrentPack(id);

  // Show error notification
  notificationMessage.value = t('cart.removedFromCart');
  notificationType.value = 'error';
  showNotification.value = true;
};

// Clear cart
const handleClearCart = () => {
  clearCurrentPack();

  // Show notification when cart is cleared
  notificationMessage.value = t('cart.cartCleared');
  notificationType.value = 'error';
  showNotification.value = true;
};

// Order pack handlers
const handleSelectPack = (packId: string) => {
  switchToPack(packId);
};

const handleCreateLocalPack = () => {
  const packId = addLocalOrder();

  if (packId) {
    showNotification.value = true;
    notificationMessage.value = t('cart.orderPacks.localOrderCreated');
    notificationType.value = 'success';
  } else {
    showNotification.value = true;
    notificationMessage.value = t('cart.orderPacks.maxPacksReached');
    notificationType.value = 'error';
  }
};

const handleCancelPack = (packId: string) => {
  cancelPack(packId);
  showNotification.value = true;
  notificationMessage.value = t('cart.orderPacks.packCancelled');
  notificationType.value = 'info';
};

// Go to stock page
const goToStock = () => {
  router.push('/stock');
};

// Proceed to checkout
const proceedToCheckout = () => {
  router.push('/checkout');
};
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <!-- Pack Warning Alert -->
    <div v-if="shouldShowPackWarning"
      class="mb-6 bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 rounded-lg p-4"
      role="alert">
      <div class="flex items-start gap-3">
        <ExclamationTriangleIcon class="w-5 h-5 text-warning-600 dark:text-warning-400 flex-shrink-0 mt-0.5" />
        <div class="flex-1">
          <h3 class="text-sm font-semibold text-warning-800 dark:text-warning-200 mb-1">
            {{ t('cart.packWarning.title') }}
          </h3>
          <p class="text-sm text-warning-700 dark:text-warning-300">
            {{ t('cart.packWarning.message') }}
          </p>
        </div>
      </div>
    </div>

    <h1 class="text-2xl font-semibold text-gray-900 mb-6 text-start dark:text-gray-100">{{ t('cart.title') }}</h1>

    <!-- Order Pack Selector -->
    <div class="mb-6">
      <OrderPackSelector :packs="activePacks" :current-pack-id="orderPackState.currentPackId"
        :can-add-more-packs="canAddPacks" :max-packs-reached="maxPacksReached" @select-pack="handleSelectPack"
        @create-local-pack="handleCreateLocalPack" @cancel-pack="handleCancelPack" />
    </div>

    <!-- Empty Cart -->
    <div v-if="!hasItems" class="mb-6">
      <div
        class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-light dark:shadow-dark-light">
        <div class="flex flex-col items-center justify-center py-16 px-6 text-center">
          <div class="flex items-center justify-center w-20 h-20 bg-gray-100 dark:bg-slate-700 rounded-full mb-6">
            <ShoppingCartIcon class="w-10 h-10 text-gray-400 dark:text-gray-500" />
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">{{ t('cart.emptyCart') }}</h2>
          <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md">{{ t('cart.emptyCartMessage') }}</p>
          <button @click="goToStock"
            class="inline-flex items-center gap-2 px-6 py-3 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            <ShoppingCartIcon class="w-5 h-5" />
            {{ t('cart.browseItems') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Cart Content -->
    <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- Cart Items -->
      <div class="lg:col-span-2">
        <div
          class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-light dark:shadow-dark-light overflow-hidden">
          <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-slate-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ t('cart.items') }}</h2>
            <button @click="handleClearCart"
              class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors duration-200">
              <TrashIcon class="w-4 h-4" />
              {{ t('cart.clearCart') }}
            </button>
          </div>

          <div class="divide-y divide-gray-200 dark:divide-slate-700">
            <div v-for="item in currentPack?.items || []" :key="item.id" class="p-6 flex items-center gap-4">
              <!-- Item Details -->
              <div class="flex-1 min-w-0">
                <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100 mb-1">{{ item.name }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ t(`medication.types.${item.type}`) }}</p>
              </div>

              <!-- Quantity Controls -->
              <div class="flex items-center gap-3">
                <button @click="decreaseQuantity(item.id, item.quantity)" :disabled="item.quantity <= 1"
                  aria-label="Decrease quantity"
                  class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                  <MinusIcon class="w-4 h-4" />
                </button>

                <span class="text-sm font-semibold text-gray-900 dark:text-gray-100 min-w-[2rem] text-center">{{
                  item.quantity }}</span>

                <button @click="increaseQuantity(item.id, item.quantity)" aria-label="Increase quantity"
                  class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors duration-200">
                  <PlusIcon class="w-4 h-4" />
                </button>
              </div>

              <!-- Price -->
              <div class="text-right min-w-[120px]">
                <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">{{ formatPrice(item.pricePerUnit) }} × {{
                  item.quantity }}</p>
                <p class="text-base font-semibold text-gray-900 dark:text-gray-100">{{ formatPrice(item.totalPrice) }}
                </p>
              </div>

              <!-- Remove Button -->
              <button @click="removeItem(item.id)" aria-label="Remove item"
                class="flex items-center justify-center w-8 h-8 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors duration-200">
                <TrashIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Cart Summary -->
      <div class="lg:col-span-1">
        <div
          class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-light dark:shadow-dark-light p-6 h-fit">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">{{ t('cart.summary') }}</h2>

          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ t('cart.subtotal') }}</span>
              <span class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ formatPrice(currentPack?.subtotal
                || 0)
                }}</span>
            </div>

            <div v-if="(currentPack?.tax || 0) > 0" class="flex justify-between items-center">
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ t('cart.tax') }}</span>
              <span class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ formatPrice(currentPack?.tax || 0)
                }}</span>
            </div>

            <div class="border-t border-gray-200 dark:border-slate-700 pt-4">
              <div class="flex justify-between items-center">
                <span class="text-base font-semibold text-gray-900 dark:text-gray-100">{{ t('cart.total') }}</span>
                <span class="text-lg font-bold text-primary-600 dark:text-primary-400">{{ formatPrice(currentPack?.total
                  || 0)
                  }}</span>
              </div>
            </div>

            <button @click="proceedToCheckout"
              class="w-full px-4 py-3 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
              {{ t('cart.checkout') }}
            </button>

            <button @click="goToStock"
              class="w-full inline-flex items-center justify-center gap-2 px-4 py-3 bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors duration-200">
              <ArrowLeftIcon class="w-4 h-4" />
              {{ t('cart.continueShopping') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast Notification -->
  <Notification :show="showNotification" :message="notificationMessage" :type="notificationType" :duration="3000"
    @close="showNotification = false" />
</template>
