<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  DocumentTextIcon,
  SparklesIcon,
  ExclamationCircleIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline';
import SkeletonLoader from '../components/ui/SkeletonLoader.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import onlineModeStore from '../store/onlineModeStore';

// Props (passed from App.vue router-view but not used in this component)
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}

defineProps<{
  user?: User | null;
  isLoggedIn?: boolean;
}>();

// Setup i18n for translations
const { t } = useI18n();

// Check if online features (like AI) should be available
const canUseAiFeatures = computed(() => onlineModeStore.state.canUseOnlineFeatures.value);

// Dynamic import card title and description based on online mode
const importCardTitle = computed(() => {
  return canUseAiFeatures.value
    ? t('importExport.importTitle')
    : t('importExport.importTitleOffline');
});

const importCardDescription = computed(() => {
  return canUseAiFeatures.value
    ? t('importExport.importDescription')
    : t('importExport.importDescriptionOffline');
});

// State for file upload
const fileInputRef = ref<HTMLInputElement | null>(null);
const isUploading = ref(false);
const uploadedFile = ref<File | null>(null);
const fileData = ref<any[] | null>(null);
const fileError = ref('');
const uploadSuccess = ref(false);

// State for AI processing
const isAiProcessing = ref(false);

// State for drag and drop
const isDragOver = ref(false);
const dragCounter = ref(0);
const aiProcessed = ref(false);
const aiResults = ref<{ category: string; status: string }[]>([]);

// State for export
const isExporting = ref(false);
const exportFormat = ref<'csv' | 'json' | null>(null);
const exportSuccess = ref(false);

// Mock inventory data for export
const mockInventoryData = reactive([
  { id: 1, name: 'Paracetamol', category: 'Pain Relief', stock: 150, price: 5.99, expiryDate: '2025-06-30' },
  { id: 2, name: 'Amoxicillin', category: 'Antibiotics', stock: 75, price: 12.50, expiryDate: '2024-12-15' },
  { id: 3, name: 'Ibuprofen', category: 'Pain Relief', stock: 200, price: 4.75, expiryDate: '2025-08-22' },
  { id: 4, name: 'Loratadine', category: 'Allergy', stock: 120, price: 8.25, expiryDate: '2025-03-10' },
  { id: 5, name: 'Omeprazole', category: 'Digestive', stock: 90, price: 15.30, expiryDate: '2024-11-05' },
]);

// Computed property for file name display
const fileName = computed(() => {
  if (!uploadedFile.value) return '';
  return uploadedFile.value.name.length > 20
    ? uploadedFile.value.name.substring(0, 17) + '...'
    : uploadedFile.value.name;
});

// Computed property for file size display
const fileSize = computed(() => {
  if (!uploadedFile.value) return '';
  const size = uploadedFile.value.size;
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  return `${(size / (1024 * 1024)).toFixed(1)} MB`;
});

// Computed property for file type
const fileType = computed(() => {
  if (!uploadedFile.value) return '';
  const fileName = uploadedFile.value.name.toLowerCase();
  if (fileName.endsWith('.json')) return 'JSON';
  if (fileName.endsWith('.csv')) return 'CSV';
  return 'Unknown';
});

// Computed property for file icon color
const fileIconColor = computed(() => {
  if (!uploadedFile.value) return 'text-gray-600 dark:text-gray-400';
  const fileName = uploadedFile.value.name.toLowerCase();
  if (fileName.endsWith('.json')) return 'text-teal-600 dark:text-teal-400';
  if (fileName.endsWith('.csv')) return 'text-green-600 dark:text-green-400';
  return 'text-gray-600 dark:text-gray-400';
});

// Supported file types
const SUPPORTED_FILE_TYPES = ['.csv', '.json'];
const SUPPORTED_MIME_TYPES = [
  // JSON MIME types
  'application/json',
  'text/json',
  'application/x-json',

  // CSV MIME types
  'text/csv',
  'text/comma-separated-values',
  'application/csv',
  'application/excel',
  'application/vnd.ms-excel',
  'application/vnd.msexcel',
  'text/anytext',
  'text/plain',

  // Empty MIME type (common for CSV files)
  ''
];

// File validation
const validateFile = (file: File): { isValid: boolean; error?: string } => {
  // Check file extension
  const fileName = file.name.toLowerCase();
  const hasValidExtension = SUPPORTED_FILE_TYPES.some(ext => fileName.endsWith(ext));

  if (!hasValidExtension) {
    return {
      isValid: false,
      error: t('importExport.invalidFileType', 'Please select a CSV or JSON file')
    };
  }

  // Check MIME type for additional security
  const hasValidMimeType = SUPPORTED_MIME_TYPES.includes(file.type);

  if (!hasValidMimeType) {
    return {
      isValid: false,
      error: t('importExport.invalidMimeType', 'File type not supported. Please select a valid CSV or JSON file')
    };
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: t('importExport.fileTooLarge')
    };
  }

  return { isValid: true };
};

// Trigger file input click
const triggerFileUpload = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

// Reset file upload state
const resetFileUpload = () => {
  uploadedFile.value = null;
  fileData.value = null;
  fileError.value = '';
  uploadSuccess.value = false;
  aiProcessed.value = false;
  aiResults.value = [];
  isDragOver.value = false;
  dragCounter.value = 0;

  // Reset file input
  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }
};

// Drag and drop handlers
const handleDragEnter = (e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
  dragCounter.value++;
  isDragOver.value = true;
};

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
  dragCounter.value--;
  if (dragCounter.value === 0) {
    isDragOver.value = false;
  }
};

const handleDragOver = (e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
};

const handleDrop = async (e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();

  isDragOver.value = false;
  dragCounter.value = 0;

  const files = e.dataTransfer?.files;
  if (!files || files.length === 0) return;

  const file = files[0];

  // Validate file
  const validation = validateFile(file);
  if (!validation.isValid) {
    fileError.value = validation.error || 'Invalid file';
    return;
  }

  // Process the file
  await processFile(file);
};

// Process file (shared between drag/drop and file input)
const processFile = async (file: File) => {
  uploadedFile.value = file;
  fileError.value = '';
  isUploading.value = true;
  uploadSuccess.value = false;

  try {
    // Read and parse the file
    const fileContent = await readFileContent(file);

    if (file.name.toLowerCase().endsWith('.json')) {
      fileData.value = JSON.parse(fileContent);
    } else if (file.name.toLowerCase().endsWith('.csv')) {
      fileData.value = parseCSV(fileContent);
    } else {
      throw new Error(t('importExport.unsupportedFileType'));
    }

    // Mark upload as successful
    uploadSuccess.value = true;
  } catch (error) {
    console.error('Error parsing file:', error);
    fileError.value = error instanceof Error ? error.message : String(error);
    fileData.value = null;
  } finally {
    isUploading.value = false;
  }
};

// Handle file upload
const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files || input.files.length === 0) return;

  const file = input.files[0];

  // Validate file
  const validation = validateFile(file);
  if (!validation.isValid) {
    fileError.value = validation.error || 'Invalid file';
    return;
  }

  // Process the file
  await processFile(file);
};

// Read file content as text
const readFileContent = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error(t('importExport.errorReadingFile')));
      }
    };
    reader.onerror = () => reject(new Error(t('importExport.errorReadingFile')));
    reader.readAsText(file);
  });
};

// Parse CSV content
const parseCSV = (content: string): any[] => {
  const lines = content.split('\n');
  if (lines.length < 2) throw new Error(t('importExport.invalidCsvFormat'));

  const headers = lines[0].split(',').map(header => header.trim());

  return lines.slice(1)
    .filter(line => line.trim() !== '')
    .map(line => {
      const values = line.split(',').map(value => value.trim());
      const entry: Record<string, string> = {};

      headers.forEach((header, index) => {
        entry[header] = values[index] || '';
      });

      return entry;
    });
};

// Run AI assist on the uploaded data
const runAiAssist = () => {
  if (!fileData.value) return;

  // Check if AI features are available (online mode enabled)
  if (!canUseAiFeatures.value) {
    console.warn('AI features are not available in offline mode');
    return;
  }

  isAiProcessing.value = true;
  aiProcessed.value = false;

  // Simulate AI processing with a timeout
  setTimeout(() => {
    // In a real implementation, this would call an AI service
    // For now, we'll simulate AI categorization results
    aiResults.value = [
      { category: 'Medication Names', status: 'Valid' },
      { category: 'Stock Quantities', status: 'Valid' },
      { category: 'Categories', status: 'Auto-assigned' },
      { category: 'Pricing', status: 'Validated' }
    ];

    aiProcessed.value = true;
    isAiProcessing.value = false;
  }, 2000);
};

// Export data as CSV or JSON
const exportData = (format: 'csv' | 'json') => {
  exportFormat.value = format;
  isExporting.value = true;
  exportSuccess.value = false;

  // Simulate export processing
  setTimeout(() => {
    let content = '';
    let filename = `pharmacy-inventory-${new Date().toISOString().split('T')[0]}`;
    let type = '';

    if (format === 'csv') {
      // Generate CSV content
      const headers = Object.keys(mockInventoryData[0]).join(',');
      const rows = mockInventoryData.map(item => Object.values(item).join(','));
      content = [headers, ...rows].join('\n');
      filename += '.csv';
      type = 'text/csv';
    } else {
      // Generate JSON content
      content = JSON.stringify(mockInventoryData, null, 2);
      filename += '.json';
      type = 'application/json';
    }

    // Create download link
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up
    URL.revokeObjectURL(url);

    exportSuccess.value = true;
    isExporting.value = false;

    // Reset export format after a delay
    setTimeout(() => {
      exportFormat.value = null;
      exportSuccess.value = false;
    }, 3000);
  }, 1000);
};

// Watch for online mode changes and clear AI state when going offline
watch(canUseAiFeatures, (canUse) => {
  if (!canUse) {
    // Clear AI processing state when going offline
    isAiProcessing.value = false;
    aiProcessed.value = false;
    aiResults.value = [];
  }
});
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <!-- Page Header -->
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">{{ t('importExport.title') }}</h1>

    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6">
      <!-- Import Card -->
      <div
        class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 p-6 space-y-6">
        <div class="flex items-start gap-4">
          <div
            class="flex items-center justify-center w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex-shrink-0 mt-1">
            <ArrowUpTrayIcon class="w-6 h-6 text-primary-600 dark:text-primary-400" />
          </div>
          <div class="flex-1 min-w-0">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">{{ importCardTitle }}</h2>
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{{ importCardDescription }}</p>
          </div>
        </div>

        <!-- File Upload Section -->
        <div class="space-y-4">
          <input type="file" ref="fileInputRef" accept=".csv,.json" class="hidden" @change="handleFileUpload"
            aria-label="Upload CSV or JSON file" />

          <div @dragenter="handleDragEnter" @dragleave="handleDragLeave" @dragover="handleDragOver" @drop="handleDrop"
            :class="[
              'border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer',
              isDragOver
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 scale-[1.02]'
                : 'border-gray-300 dark:border-slate-600 hover:border-primary-400 dark:hover:border-primary-500'
            ]" @click="triggerFileUpload">
            <div class="space-y-4">
              <div class="flex justify-center">
                <div :class="[
                  'flex items-center justify-center w-12 h-12 rounded-lg transition-colors duration-200',
                  isDragOver
                    ? 'bg-primary-100 dark:bg-primary-800'
                    : 'bg-gray-100 dark:bg-slate-700'
                ]">
                  <ArrowUpTrayIcon :class="[
                    'w-6 h-6 transition-colors duration-200',
                    isDragOver
                      ? 'text-primary-600 dark:text-primary-400'
                      : 'text-gray-600 dark:text-gray-400'
                  ]" />
                </div>
              </div>

              <div class="space-y-2">
                <div class="space-y-1">
                  <p :class="[
                    'text-sm font-medium transition-colors duration-200',
                    isDragOver
                      ? 'text-primary-700 dark:text-primary-300'
                      : 'text-gray-700 dark:text-gray-300'
                  ]">
                    {{ isDragOver ? t('importExport.dropFile') : t('importExport.dragDropText') }}
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    {{ t('importExport.supportedFormats') }}
                  </p>
                </div>

                <button type="button" @click.stop="triggerFileUpload" :disabled="isUploading" :aria-busy="isUploading"
                  class="inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
                  <ArrowUpTrayIcon class="w-4 h-4" />
                  <span>{{ t('importExport.uploadButton') }}</span>
                </button>
              </div>
            </div>

            <!-- File info when uploaded -->
            <div v-if="uploadedFile && !fileError"
              class="mt-4 flex items-center justify-between p-4 bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-600 shadow-sm">
              <div class="flex items-center gap-3">
                <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 dark:bg-slate-700">
                  <DocumentTextIcon :class="['w-5 h-5', fileIconColor]" />
                </div>
                <div class="text-start">
                  <div class="flex items-center gap-2">
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ fileName }}</p>
                    <span :class="[
                      'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
                      fileType === 'JSON' ? 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-300' :
                        fileType === 'CSV' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
                    ]">
                      {{ fileType }}
                    </span>
                  </div>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ fileSize }}</p>
                </div>
              </div>
              <button @click="resetFileUpload"
                class="flex items-center justify-center w-8 h-8 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors duration-200"
                aria-label="Remove uploaded file">
                <span class="text-lg font-semibold">×</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Loading indicator -->
        <div v-if="isUploading"
          class="p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700">
          <SkeletonLoader type="text" width="w-full" height="h-4" class="mb-2" />
          <p class="text-sm font-medium text-gray-700 dark:text-gray-300 text-center">{{ t('common.loading') }}</p>
        </div>

        <!-- Error Message -->
        <div v-if="fileError"
          class="flex items-start gap-3 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
          <ExclamationCircleIcon class="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
          <p class="text-sm text-red-700 dark:text-red-300">{{ fileError }}</p>
        </div>

        <!-- Upload Success Message -->
        <div v-if="uploadSuccess && !fileError && !isUploading"
          class="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <CheckCircleIcon class="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0" />
          <p class="text-sm font-medium text-green-700 dark:text-green-300">
            {{ t('importExport.fileUploaded') }}
          </p>
        </div>

        <!-- File Preview -->
        <div v-if="fileData && fileData.length > 0" class="space-y-4">
          <div class="flex items-center gap-3">
            <div class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-slate-700 rounded-lg">
              <DocumentTextIcon class="w-4 h-4 text-gray-600 dark:text-gray-400" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ t('importExport.dataPreview') }}</h3>
          </div>

          <div
            class="overflow-x-auto bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-sm">
            <table class="w-full">
              <thead class="bg-gray-50 dark:bg-slate-700">
                <tr>
                  <th v-for="(key, index) in Object.keys(fileData[0])" :key="index"
                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {{ key }}
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                <tr v-for="(row, rowIndex) in fileData.slice(0, 5)" :key="rowIndex"
                  class="hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200">
                  <td v-for="(key, keyIndex) in Object.keys(fileData[0])" :key="keyIndex"
                    class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {{ row[key] }}
                  </td>
                </tr>
              </tbody>
            </table>

            <div v-if="fileData.length > 5"
              class="px-4 py-3 bg-gray-50 dark:bg-slate-700 border-t border-gray-200 dark:border-slate-700">
              <p class="text-sm text-gray-500 dark:text-gray-400 text-center">
                {{ t('importExport.moreRows', { count: fileData.length - 5 }) }}
              </p>
            </div>
          </div>

          <!-- AI Features Offline Message -->
          <div v-if="!canUseAiFeatures"
            class="p-4 bg-teal-50 dark:bg-teal-900/20 rounded-lg border border-teal-200 dark:border-teal-800">
            <div class="flex items-start gap-3">
              <SparklesIcon class="w-5 h-5 text-teal-600 dark:text-teal-400 flex-shrink-0 mt-0.5" />
              <div class="text-start">
                <h4 class="text-sm font-semibold text-teal-800 dark:text-teal-200 mb-1">
                  {{ t('importExport.aiFeatureOffline', 'AI-Powered Import Assistant') }}
                </h4>
                <p class="text-sm text-teal-700 dark:text-teal-300 mb-2">
                  {{ t('importExport.aiFeatureOfflineMessage') }}
                </p>
                <p class="text-xs text-teal-600 dark:text-teal-400">
                  {{ t('importExport.enableOnlineModeHint') }}
                </p>
              </div>
            </div>
          </div>

          <!-- AI Assist Button - Only show when online features are available -->
          <button v-if="canUseAiFeatures" @click="runAiAssist" :disabled="isAiProcessing || aiProcessed"
            :aria-busy="isAiProcessing"
            class="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-teal-600 to-teal-700 text-white rounded-lg font-medium hover:from-teal-700 hover:to-teal-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
            <SparklesIcon class="w-4 h-4" />
            <span>{{ t('importExport.runAiAssist') }}</span>
          </button>

          <!-- AI Processing Status - Only show when online features are available -->
          <div v-if="canUseAiFeatures && isAiProcessing"
            class="p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700">
            <SkeletonLoader type="text" width="w-full" height="h-4" class="mb-2" />
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300 text-center">{{
              t('importExport.aiProcessing') }}</p>
          </div>

          <!-- AI Processed Results - Only show when online features are available -->
          <div v-if="canUseAiFeatures && aiProcessed" class="space-y-4">
            <div
              class="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <CheckCircleIcon class="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0" />
              <p class="text-sm font-medium text-green-700 dark:text-green-300">{{ t('importExport.aiProcessed') }}</p>
            </div>

            <div
              class="overflow-x-auto bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-sm">
              <table class="w-full">
                <thead class="bg-gray-50 dark:bg-slate-700">
                  <tr>
                    <th
                      class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {{ t('importExport.category') || 'Category' }}
                    </th>
                    <th
                      class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {{ t('importExport.status') || 'Status' }}
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                  <tr v-for="(result, index) in aiResults" :key="index"
                    class="hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200">
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ result.category
                      }}</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300">
                        {{ result.status }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Export Card -->
      <div
        class="bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 p-6 space-y-6">
        <div class="flex items-start gap-4">
          <div
            class="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex-shrink-0 mt-1">
            <ArrowDownTrayIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div class="flex-1 min-w-0">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">{{ t('importExport.exportTitle') }}
            </h2>
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{{ t('importExport.exportDescription')
            }}</p>
          </div>
        </div>

        <!-- Export Buttons -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <button @click="exportData('csv')" :disabled="isExporting" :aria-busy="isExporting && exportFormat === 'csv'"
            class="flex items-center justify-center gap-2 px-4 py-3 bg-teal-600 text-white rounded-lg font-medium hover:bg-teal-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
            <DocumentTextIcon class="w-5 h-5" />
            <span>{{ t('importExport.exportCsv') }}</span>
          </button>

          <button @click="exportData('json')" :disabled="isExporting"
            :aria-busy="isExporting && exportFormat === 'json'"
            class="flex items-center justify-center gap-2 px-4 py-3 bg-teal-600 text-white rounded-lg font-medium hover:bg-teal-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
            <ArrowDownTrayIcon class="w-5 h-5" />
            <span>{{ t('importExport.exportJson') }}</span>
          </button>
        </div>

        <!-- Export Processing Status -->
        <div v-if="isExporting"
          class="p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700">
          <SkeletonLoader type="text" width="w-full" height="h-4" class="mb-2" />
          <p class="text-sm font-medium text-gray-700 dark:text-gray-300 text-center">{{ t('importExport.exporting', {
            format:
              exportFormat?.toUpperCase()
          }) }}</p>
        </div>

        <!-- Export Success Message -->
        <div v-if="exportSuccess"
          class="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <CheckCircleIcon class="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0" />
          <p class="text-sm font-medium text-green-700 dark:text-green-300">{{ t('importExport.exportSuccess', {
            format:
              exportFormat?.toUpperCase()
          }) || `Export as ${exportFormat?.toUpperCase()} completed successfully!` }}</p>
        </div>

        <!-- Preview of data to be exported -->
        <div class="space-y-4">
          <div class="flex items-center gap-3">
            <div class="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-slate-700 rounded-lg">
              <DocumentTextIcon class="w-4 h-4 text-gray-600 dark:text-gray-400" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ t('importExport.exportDataPreview') ||
              'Data to be exported' }}</h3>
          </div>

          <div
            class="overflow-x-auto bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-sm">
            <table class="w-full">
              <thead class="bg-gray-50 dark:bg-slate-700">
                <tr>
                  <th
                    class="text-start px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {{ t('importExport.name') || 'Name' }}
                  </th>
                  <th
                    class="text-start px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {{ t('importExport.category') || 'Category' }}
                  </th>
                  <th
                    class="text-start px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {{ t('importExport.stock') || 'Stock' }}
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                <tr v-for="(item, index) in mockInventoryData.slice(0, 3)" :key="index"
                  class="hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200">
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ item.name }}</td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ item.category }}
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ item.stock }}</td>
                </tr>
              </tbody>
            </table>

            <div v-if="mockInventoryData.length > 3"
              class="px-4 py-3 bg-gray-50 dark:bg-slate-700 border-t border-gray-200 dark:border-slate-700">
              <p class="text-sm text-gray-500 dark:text-gray-400 text-center">
                {{ t('importExport.moreRows', { count: mockInventoryData.length - 3 }) }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
