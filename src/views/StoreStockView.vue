<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import debounce from 'debounce';
import {
  ArrowPathIcon,
  ArrowLeftIcon,
  ExclamationTriangleIcon,
  BuildingStorefrontIcon,
} from '@heroicons/vue/24/outline';
import type { StoreInventoryItem } from '../types/stores';
import { getStoreInventory, getStoreLoadingState } from '../services/storeService';
import SearchFilter from '../components/ui/SearchFilter.vue';
import AppTable from '../components/ui/AppTable.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import type { AppTableColumn, AppTablePagination } from '../types/table';

// Props (passed from App.vue router-view but not used in this component)
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}

defineProps<{
  user?: User | null;
  isLoggedIn?: boolean;
}>();

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// Get store ID from route params
const storeId = computed(() => route.params.id as string);

// Reactive state
const inventory = ref<StoreInventoryItem[]>([]);
const storeName = ref('');
const search = ref('');

const pagination = reactive<AppTablePagination>({
  page: 1,
  limit: 10,
  totalItems: 0,
  totalPages: 0,
  pageSize: 10
});

// Get loading state from service
const { isLoading, error } = getStoreLoadingState();

// Computed properties
const hasInventory = computed(() => inventory.value.length > 0);
const hasError = computed(() => !!error.value);
const showEmptyState = computed(() => !isLoading.value && !hasInventory.value && !hasError.value);

// Table configuration
const columns = computed<AppTableColumn[]>(() => [
  {
    key: 'name',
    label: t('stock.columns.name'),
    sortable: true,
    width: '25%'
  },
  {
    key: 'category',
    label: t('stock.columns.category'),
    sortable: true,
    width: '15%'
  },
  {
    key: 'quantity',
    label: t('stock.columns.quantity'),
    sortable: true,
    width: '10%',
    align: 'center'
  },
  {
    key: 'pricePerUnit',
    label: t('stock.columns.pricePerUnit'),
    sortable: true,
    width: '12%',
    align: 'right'
  },
  {
    key: 'expiryDate',
    label: t('stock.columns.expiryDate'),
    sortable: true,
    width: '12%'
  },
  {
    key: 'manufacturer',
    label: t('medication.supplier'),
    sortable: false,
    width: '14%'
  }
]);

// Methods
const loadStoreInventory = async (resetPage = false) => {
  if (resetPage) {
    pagination.page = 1;
  }

  const result = await getStoreInventory(storeId.value, search.value, pagination);

  if (result.success && result.data) {
    inventory.value = result.data.inventory;
    storeName.value = result.data.storeName;
    pagination.totalItems = result.data.pagination.total;
    pagination.totalPages = result.data.pagination.totalPages;
  }
};

const handleSearch = debounce(async (searchValue: string) => {
  search.value = searchValue;
  await loadStoreInventory(true);
}, 500);

const handleRefresh = async () => {
  await loadStoreInventory();
};

const handlePageChange = async (page: number) => {
  pagination.page = page;
  await loadStoreInventory();
};

const handlePaginationUpdate = async (newPagination: AppTablePagination) => {
  pagination.page = newPagination.page;
  pagination.pageSize = newPagination.pageSize;
  pagination.limit = newPagination.pageSize; // Keep both for compatibility
  await loadStoreInventory();
};

const goBack = () => {
  router.push('/stores');
};



const formatPrice = (price: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'EGP',
    minimumFractionDigits: 2
  }).format(price);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

// Lifecycle
onMounted(async () => {
  await loadStoreInventory();
});

// Watch for store ID changes
watch(() => storeId.value, async () => {
  if (storeId.value) {
    await loadStoreInventory(true);
  }
});
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <!-- Header -->
    <div class="flex items-center gap-4 mb-6">
      <button @click="goBack"
        class="inline-flex items-center gap-2 px-3 py-2 bg-gray-100 dark:bg-neutral-700 border border-gray-300 dark:border-neutral-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-all duration-200 hover:bg-gray-200 dark:hover:bg-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-neutral-800">
        <ArrowLeftIcon class="w-4 h-4" />
        <span class="text-sm">{{ t('common.back') }}</span>
      </button>

      <div>
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
          {{ t('stores.stockModal.title') }}
        </h1>
        <p v-if="storeName" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {{ t('stores.stockModal.subtitle', { storeName }) }}
        </p>
      </div>
    </div>

    <!-- Search and Refresh Bar -->
    <SearchFilter v-model:search="search" :placeholder="t('stores.stockModal.searchStock')" :loading="isLoading"
      @search="handleSearch" class="mb-6">
      <template #actions>
        <button @click="handleRefresh" :disabled="isLoading"
          class="inline-flex items-center gap-2 px-4 py-2.5 bg-white dark:bg-neutral-800 border border-gray-300 dark:border-neutral-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-all duration-200 hover:bg-gray-50 dark:hover:bg-neutral-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-neutral-800 disabled:opacity-50 disabled:cursor-not-allowed">
          <ArrowPathIcon class="w-4 h-4" :class="{ 'animate-spin': isLoading }" />
          <span class="text-sm">{{ t('common.refresh') }}</span>
        </button>
      </template>
    </SearchFilter>

    <!-- Error State -->
    <div v-if="hasError" class="text-center py-12">
      <ExclamationTriangleIcon class="w-16 h-16 text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {{ t('stores.errorLoading') }}
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
      <button @click="handleRefresh"
        class="inline-flex items-center gap-2 px-4 py-2.5 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-neutral-800">
        <ArrowPathIcon class="w-4 h-4" />
        {{ t('stores.retryLoading') }}
      </button>
    </div>

    <!-- Empty State -->
    <div v-else-if="showEmptyState" class="text-center py-12">
      <div class="w-16 h-16 bg-gray-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-4">
        <BuildingStorefrontIcon class="w-8 h-8 text-gray-400 dark:text-gray-600" />
      </div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {{ t('stores.stockModal.noStock') }}
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        {{ t('stores.stockModal.noStockDescription') }}
      </p>
    </div>

    <!-- Inventory Table -->
    <div v-else>
      <AppTable :columns="columns" :data="inventory" :loading="isLoading" :pagination="pagination"
        :show-items-per-page="true" @update:pagination="handlePaginationUpdate" @update:current-page="handlePageChange"
        @page-change="handlePageChange">
        <!-- Custom cell renderers -->
        <template #cell-quantity="{ row }">
          <span class="font-medium">{{ row.quantity }}</span>
        </template>

        <template #cell-pricePerUnit="{ row }">
          <span class="font-medium">{{ formatPrice(row.pricePerUnit) }}</span>
        </template>

        <template #cell-expiryDate="{ row }">
          <span>{{ formatDate(row.expiryDate) }}</span>
        </template>

        <template #cell-manufacturer="{ row }">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ row.manufacturer || '-' }}
          </span>
        </template>
      </AppTable>
    </div>
  </div>
</template>
