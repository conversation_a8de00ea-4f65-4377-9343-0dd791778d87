<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick, shallowRef } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import debounce from 'debounce';
import {
  ArrowPathIcon
} from '@heroicons/vue/24/outline';
import type {
  MedicationItem,
  StockSearch,
  StockPagination,
  StockSort,
  StockStatus,
  ExpiryStatus
} from '../types/inventory';
import { getInventoryData, getInventoryLoadingState } from '../services/inventoryService';
import ItemDetailsModal from '../components/medication/ItemDetailsModal.vue';
import SearchFilter from '../components/ui/SearchFilter.vue';
import AppTable from '../components/ui/AppTable.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import BaseBadge from '../components/ui/BaseBadge.vue';
import type { DetailedMedicationItem } from '../types/cart';
import type { AppTableColumn, AppTablePagination } from '../types/table';

// Props (passed from App.vue router-view but not used in this component)
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}

defineProps<{
  user?: User | null;
  isLoggedIn?: boolean;
}>();

// Setup i18n for translations
const { t } = useI18n();

// Get route and router for URL parameters
const route = useRoute();
const router = useRouter();


// Get loading state
const { isLoading, error } = getInventoryLoadingState();

// State for medications data - using shallowRef for better performance with large datasets
const medications = shallowRef<MedicationItem[]>([]);

// Filter state - now supports multiple selections
const filter = reactive({
  status: ['all'] as string[],
  expiryStatus: ['all'] as string[]
});

// Filter configuration for SearchFilter component
const filterConfig = computed(() => ({
  status: {
    label: t('stock.status'),
    options: [
      { value: 'all', label: t('stock.allItems') },
      { value: 'in-stock', label: t('stock.inStock') },
      { value: 'low-stock', label: t('stock.lowStock') },
      { value: 'out-of-stock', label: t('stock.outOfStock') },
      { value: 'recent', label: t('stock.recentlyAdded') }
    ],
    multiSelect: true
  },
  expiryStatus: {
    label: t('stock.expiryStatus'),
    options: [
      { value: 'all', label: t('stock.allItems') },
      { value: 'valid', label: t('stock.valid') },
      { value: 'expiring-soon', label: t('stock.expiringSoon') },
      { value: 'expired', label: t('stock.expired') }
    ],
    multiSelect: true
  }
}));

// Search state
const search = reactive<StockSearch>({
  query: ''
});

// Pagination state
const pagination = reactive<StockPagination>({
  currentPage: 1,
  itemsPerPage: 10,
  totalItems: 0
});

// Sort state
const sort = reactive<StockSort>({
  field: 'name' as keyof MedicationItem,
  direction: 'asc'
});

// UI state

// Selected medication for modal
const selectedMedication = ref<DetailedMedicationItem | null>(null);
const showDetailsModal = ref(false);

// Table columns configuration
const tableColumns = computed<AppTableColumn[]>(() => [
  {
    key: 'name',
    label: t('stock.columns.name'),
    sortable: true,
    width: '25%'
  },
  {
    key: 'category',
    label: t('stock.columns.category'),
    sortable: true,
    width: '15%',
    responsive: 'md'
  },
  {
    key: 'quantity',
    label: t('stock.columns.quantity'),
    sortable: true,
    width: '10%'
  },
  {
    key: 'expiryDate',
    label: t('stock.columns.expiryDate'),
    sortable: true,
    width: '15%',
    responsive: 'lg',
    render: (value: string) => formatDate(value)
  },
  {
    key: 'pricePerUnit',
    label: t('stock.columns.pricePerUnit'),
    sortable: true,
    width: '15%',
    responsive: 'lg',
    render: (value: number) => formatPrice(value)
  },
  {
    key: 'status',
    label: t('stock.columns.status'),
    sortable: false,
    width: '20%'
  }
]);

// Table pagination
const tablePagination = computed<AppTablePagination>(() => ({
  page: pagination.currentPage,
  pageSize: pagination.itemsPerPage,
  totalItems: pagination.totalItems
}));

// Fetch inventory data
const fetchInventoryData = async () => {
  const result = await getInventoryData();
  if (result.success && result.data) {
    medications.value = result.data;
    pagination.totalItems = result.data.length;
  }
};

// Open medication details modal
const openMedicationDetails = (medication: MedicationItem) => {
  // Convert MedicationItem to DetailedMedicationItem
  selectedMedication.value = {
    id: medication.id,
    name: medication.name,
    barcode: `MED-${medication.id}`, // Example barcode format
    category: medication.category,
    type: 'pill', // Default type, would come from actual data
    description: '', // Would come from actual data
    quantity: medication.quantity,
    expiryDate: medication.expiryDate,
    pricePerUnit: medication.pricePerUnit,
    purchasePrice: medication.pricePerUnit * 0.7, // Example purchase price
    supplier: 'Default Supplier', // Would come from actual data
    storageLocation: 'Main Storage', // Would come from actual data
    discount: {
      hasDiscount: false,
      percentage: 0,
      validUntil: ''
    }
  };

  showDetailsModal.value = true;
};

// Handle medication deletion
const handleDeleteMedication = (id: number) => {
  // This would call a service to delete the medication
  // For now, just remove it from the local array
  medications.value = medications.value.filter(med => med.id !== id);

  // Close modal
  showDetailsModal.value = false;
};

// Filter medications based on current filters and search
const filteredMedications = computed(() => {
  let result = [...medications.value];

  // Apply status filter
  if (!filter.status.includes('all')) {
    result = result.filter(med => {
      // Handle "recent" as a special status filter
      if (filter.status.includes('recent')) {
        // Check if medication was added in the last 7 days
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        const sevenDaysAgoStr = sevenDaysAgo.toISOString().split('T')[0];

        const isRecent = med.addedDate && med.addedDate >= sevenDaysAgoStr;

        // If "recent" is selected along with other statuses, include both recent items and items with matching status
        const hasMatchingStatus = filter.status.some(status => status !== 'recent' && status === med.status);

        return isRecent || hasMatchingStatus;
      }

      // Regular status filtering
      return filter.status.includes(med.status);
    });
  }

  // Apply expiry status filter
  if (!filter.expiryStatus.includes('all')) {
    result = result.filter(med => filter.expiryStatus.includes(med.expiryStatus));
  }

  // Apply search
  if (search.query) {
    // Normal search by name or category
    const query = search.query.toLowerCase();
    result = result.filter(
      med => med.name.toLowerCase().includes(query) ||
        med.category.toLowerCase().includes(query)
    );
  }

  // Update total items
  pagination.totalItems = result.length;

  // Apply sort
  if (sort.field) {
    result.sort((a, b) => {
      const fieldA = a[sort.field as keyof MedicationItem];
      const fieldB = b[sort.field as keyof MedicationItem];

      if (fieldA && fieldB && fieldA < fieldB) return sort.direction === 'asc' ? -1 : 1;
      if (fieldA && fieldB && fieldA > fieldB) return sort.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }

  return result;
});

// Paginated medications
const paginatedMedications = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.itemsPerPage;
  const end = start + pagination.itemsPerPage;
  return filteredMedications.value.slice(start, end);
});





// Handle table sort (for AppTable component)
const handleTableSort = (column: string, order: 'asc' | 'desc') => {
  sort.field = column as keyof MedicationItem;
  sort.direction = order;
};

// Handle table pagination update
const handlePaginationUpdate = (newPagination: AppTablePagination) => {
  pagination.currentPage = newPagination.page;
  pagination.itemsPerPage = newPagination.pageSize;
};

// Handle table row click
const handleTableRowClick = (row: MedicationItem) => {
  openMedicationDetails(row);
};

// Debounced search function to update URL after user stops typing
const debouncedUpdateSearch = debounce(() => {
  // Reset to first page when search changes
  pagination.currentPage = 1;
  // Update URL after debounce delay
  updateUrlWithFilters();
}, 500); // 500ms delay for better UX

// Handle search from SearchFilter component
const handleSearchFilterSearch = (searchValue: string) => {
  search.query = searchValue;
};

// Handle filter change from SearchFilter component
const handleSearchFilterChange = (filterType: string, value: string | string[]) => {
  if (filterType === 'status') {
    filter.status = Array.isArray(value) ? value : [value];
  } else if (filterType === 'expiryStatus') {
    filter.expiryStatus = Array.isArray(value) ? value : [value];
  }
  // Reset to first page when filter changes
  pagination.currentPage = 1;
  // Immediately update URL
  updateUrlWithFilters();
};

// Handle reset from SearchFilter component
const handleSearchFilterReset = () => {
  resetFilters();
};

// Format date for display
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Format price for display
const formatPrice = (price: number) => {
  return new Intl.NumberFormat(undefined, {
    style: 'currency',
    currency: 'USD'
  }).format(price);
};



// Get status text
const getStatusText = (status: StockStatus) => {
  switch (status) {
    case 'in-stock': return t('stock.inStock');
    case 'low-stock': return t('stock.lowStock');
    case 'out-of-stock': return t('stock.outOfStock');
    default: return status;
  }
};

// Get expiry status text
const getExpiryStatusText = (status: ExpiryStatus) => {
  switch (status) {
    case 'valid': return t('stock.valid');
    case 'expiring-soon': return t('stock.expiringSoon');
    case 'expired': return t('stock.expired');
    default: return status;
  }
};

// Reset filters
const resetFilters = () => {
  filter.status = ['all'];
  filter.expiryStatus = ['all'];
  search.query = '';
  pagination.currentPage = 1;
  // Cancel any pending debounced search
  debouncedUpdateSearch.clear();
  // Immediately update URL
  updateUrlWithFilters();
};



// Watch for pagination changes to update URL (only when page changes independently)
watch(() => pagination.currentPage, () => {
  updateUrlWithFilters();
});

// Watch for items per page changes
watch(() => pagination.itemsPerPage, () => {
  pagination.currentPage = 1;
  updateUrlWithFilters();
});

// Apply filters based on URL parameters
const applyUrlParams = () => {
  try {
    // Check for status parameter in URL
    const statusParam = route.query.status as string;
    if (statusParam) {
      // Convert snake_case back to kebab-case and handle multiple values
      const statusValues = statusParam.split(',').map(s => s.replace(/_/g, '-'));
      const validStatuses = statusValues.filter(s => ['in-stock', 'low-stock', 'out-of-stock', 'recent'].includes(s));
      if (validStatuses.length > 0) {
        filter.status = validStatuses;
      }
    }

    // Check for expiry status parameter in URL
    const expiryStatusParam = route.query.expiryStatus as string;
    if (expiryStatusParam) {
      // Convert snake_case back to kebab-case and handle multiple values
      const expiryStatusValues = expiryStatusParam.split(',').map(s => s.replace(/_/g, '-'));
      const validExpiryStatuses = expiryStatusValues.filter(s => ['valid', 'expiring-soon', 'expired'].includes(s));
      if (validExpiryStatuses.length > 0) {
        filter.expiryStatus = validExpiryStatuses;
      }
    }

    // Check for search parameter in URL
    const searchParam = route.query.search as string;
    if (searchParam) {
      search.query = searchParam;
    }

    // Check for page parameter in URL
    const pageParam = route.query.page as string;
    if (pageParam && !isNaN(parseInt(pageParam))) {
      pagination.currentPage = parseInt(pageParam);
    }
  } catch (error) {
    console.error('Error applying URL parameters:', error);
  }
};

// Update URL with current filters
const updateUrlWithFilters = () => {
  const query: Record<string, string> = {};

  // Add status to URL if it's not 'all'
  if (!filter.status.includes('all')) {
    query.status = filter.status.map(s => s.replace(/-/g, '_')).join(','); // Convert kebab-case to snake_case for URL
  }

  // Add expiry status to URL if it's not 'all'
  if (!filter.expiryStatus.includes('all')) {
    query.expiryStatus = filter.expiryStatus.map(s => s.replace(/-/g, '_')).join(','); // Convert kebab-case to snake_case for URL
  }

  // Add search to URL if it's not empty
  if (search.query) {
    query.search = search.query;
  }

  // Add page to URL if it's not 1
  if (pagination.currentPage > 1) {
    query.page = pagination.currentPage.toString();
  }

  // Update the URL without reloading the page
  router.replace({ query });
};

// Fetch data on component mount
onMounted(async () => {
  // First apply URL params to set the correct filter state
  applyUrlParams();
  // Then fetch data
  await fetchInventoryData();
  // Force reactivity update to ensure SearchFilter component gets the updated filter values
  await nextTick();
});

// Clean up debounced functions
onUnmounted(() => {
  // Clear any pending debounced search
  debouncedUpdateSearch.clear();
});
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <h1 class="text-2xl font-semibold text-gray-900 mb-6 text-start dark:text-gray-100">{{ t('stock.title') }}</h1>

    <!-- Search and Filter Bar -->
    <SearchFilter v-model:search="search.query" v-model:filters="filter" :filter-config="filterConfig"
      :placeholder="t('stock.search')" :loading="isLoading" @search="handleSearchFilterSearch"
      @filter-change="handleSearchFilterChange" @reset="handleSearchFilterReset" class="mb-6" />

    <!-- Loading State -->
    <AppTable v-if="isLoading" :columns="tableColumns" :data="[]" :loading="true" :pagination="tablePagination" />

    <!-- Error State -->
    <div v-else-if="error" class="flex flex-col items-center justify-center p-12 text-center">
      <p class="text-error-600 dark:text-error-400 mb-4">{{ t('stock.error') }}: {{ error }}</p>
      <button @click="fetchInventoryData"
        class="flex items-center gap-2 px-3 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-primary-500/50 focus:ring-offset-1">
        <ArrowPathIcon class="w-4 h-4" aria-hidden="true" />
        <span>{{ t('stock.retry') }}</span>
      </button>
    </div>

    <!-- Empty State -->
    <div v-else-if="filteredMedications.length === 0"
      class="flex flex-col items-center justify-center p-12 text-center text-gray-500 dark:text-gray-400">
      <p class="mb-4">{{ t('stock.noResults') }}</p>
      <button @click="resetFilters"
        class="flex items-center gap-2 px-3 py-2 bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-slate-600 text-sm font-medium rounded-md hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-gray-400/50 focus:ring-offset-1">
        <ArrowPathIcon class="w-4 h-4" aria-hidden="true" />
        <span>{{ t('common.retry') }}</span>
      </button>
    </div>

    <!-- Data Table -->
    <AppTable v-else :columns="tableColumns" :data="paginatedMedications" :loading="isLoading"
      :pagination="tablePagination" :sort-by="sort.field as string" :sort-order="sort.direction"
      :empty-message="t('stock.noResults')" @sort="handleTableSort" @row-click="handleTableRowClick"
      @update:pagination="handlePaginationUpdate">
      <!-- Custom cell template for status -->
      <template #cell-status="{ row }">
        <div class="flex flex-wrap gap-1">
          <BaseBadge :variant="row.status === 'in-stock' ? 'success' : row.status === 'low-stock' ? 'warning' : 'error'"
            size="sm" rounded>
            {{ getStatusText(row.status) }}
          </BaseBadge>
          <BaseBadge v-if="row.expiryStatus !== 'valid'"
            :variant="row.expiryStatus === 'expiring-soon' ? 'warning' : 'error'" size="sm" rounded>
            {{ getExpiryStatusText(row.expiryStatus) }}
          </BaseBadge>
        </div>
      </template>
    </AppTable>
  </div>

  <!-- Medication Details Modal -->
  <ItemDetailsModal :show="showDetailsModal" :medication="selectedMedication" @close="showDetailsModal = false"
    @delete="handleDeleteMedication" />
</template>
