<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import debounce from 'debounce';
import {
  ArrowPathIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';
import type { Store, StoreSearch, StoreFilters } from '../types/stores';
import { getStores, getStoreLoadingState } from '../services/storeService';
import StoreCard from '../components/stores/StoreCard.vue';
import SearchFilter from '../components/ui/SearchFilter.vue';
import SkeletonLoader from '../components/ui/SkeletonLoader.vue';
import PageOfflineAlert from '../components/ui/PageOfflineAlert.vue';
import Notification from '../components/Notification.vue';
import OrderRequestModal from '../components/orders/OrderRequestModal.vue';
import Pagination from '../components/ui/Pagination.vue';

// Props (passed from App.vue router-view but not used in this component)
interface User {
  userId: string;
  role: 'PharmacyOwner' | 'PharmacyUser';
  email?: string;
  deviceId?: string;
  token: string;
}

defineProps<{
  user?: User | null;
  isLoggedIn?: boolean;
}>();

const { t } = useI18n();
const router = useRouter();

// Reactive state
const stores = ref<Store[]>([]);
const search = reactive<StoreSearch>({
  query: '',
  filters: {
    city: 'all',
    storeType: 'all',
    openNow: 'all',
    deliverySupport: 'all'
  }
});



const pagination = reactive({
  page: 1,
  limit: 12,
  total: 0,
  totalPages: 0
});

// UI state
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationType = ref<'success' | 'error' | 'info'>('info');
const showOrderModal = ref(false);
const selectedStore = ref<Store | null>(null);

// Get loading state from service
const { isLoading, error } = getStoreLoadingState();

// Filter configuration for SearchFilter component
const filterConfig = computed(() => ({
  city: {
    label: t('stores.filters.city'),
    options: [
      { value: 'all', label: t('common.all') },
      { value: 'Cairo', label: 'Cairo' },
      { value: 'Alexandria', label: 'Alexandria' },
      { value: 'Giza', label: 'Giza' },
      { value: 'Shubra El Kheima', label: 'Shubra El Kheima' },
      { value: 'Port Said', label: 'Port Said' },
      { value: 'Suez', label: 'Suez' }
    ]
  },
  storeType: {
    label: t('stores.filters.storeType'),
    options: [
      { value: 'all', label: t('common.all') },
      { value: 'main', label: t('stores.storeTypes.main') },
      { value: 'branch', label: t('stores.storeTypes.branch') },
      { value: 'partner', label: t('stores.storeTypes.partner') }
    ]
  },

  openNow: {
    label: t('stores.filters.openNow'),
    options: [
      { value: 'all', label: t('common.all') },
      { value: 'true', label: t('stores.openNow') },
      { value: 'false', label: t('stores.closedNow') }
    ]
  },
  deliverySupport: {
    label: t('stores.filters.deliverySupport'),
    options: [
      { value: 'all', label: t('common.all') },
      { value: 'true', label: t('stores.available') },
      { value: 'false', label: t('stores.unavailable') }
    ]
  }
}));

// Computed properties
const hasStores = computed(() => stores.value.length > 0);
const hasError = computed(() => !!error.value);
const showEmptyState = computed(() => !isLoading.value && !hasStores.value && !hasError.value);

// Methods
const loadStores = async (resetPage = false) => {
  if (resetPage) {
    pagination.page = 1;
  }

  const result = await getStores(search, pagination);

  if (result.success && result.data) {
    stores.value = result.data.stores;
    pagination.total = result.data.pagination.total;
    pagination.totalPages = result.data.pagination.totalPages;
  } else if (result.error) {
    showNotificationMessage(result.error, 'error');
  }
};

const handleSearchFilterSearch = debounce(async (searchValue: string) => {
  search.query = searchValue;
  await loadStores(true);
}, 500);

const handleSearchFilterChange = async (filterType: string, value: string | string[]) => {
  // Convert filter values to appropriate types
  const processedFilters: any = { ...search.filters };

  if (value && value !== '' && value !== 'all') {
    if (filterType === 'openNow' || filterType === 'deliverySupport') {
      processedFilters[filterType] = value === 'true';
    } else {
      processedFilters[filterType] = value as string;
    }
  } else {
    // Set to 'all' to maintain filter state for UI
    processedFilters[filterType] = 'all';
  }

  search.filters = processedFilters;
  await loadStores(true);
};

const handleSearchFilterReset = async () => {
  search.query = '';
  search.filters = {
    city: 'all',
    storeType: 'all',
    openNow: 'all',
    deliverySupport: 'all'
  };
  await loadStores(true);
};

const handleRefresh = async () => {
  await loadStores();
};

const handleSeeStock = (store: Store) => {
  router.push(`/stores/${store.id}/stock`);
};

const handleOrder = (store: Store) => {
  selectedStore.value = store;
  showOrderModal.value = true;
};

const closeOrderModal = () => {
  showOrderModal.value = false;
  selectedStore.value = null;
};

const handleOrderCompleted = () => {
  showNotificationMessage(t('stores.orderRequest.requestSent'), 'success');
  closeOrderModal();
};

const handlePageChange = async (page: number) => {
  pagination.page = page;
  await loadStores();
};

const handleItemsPerPageChange = async (itemsPerPage: number) => {
  pagination.limit = itemsPerPage;
  pagination.page = 1; // Reset to first page when changing items per page
  await loadStores();
};

const showNotificationMessage = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  notificationMessage.value = message;
  notificationType.value = type;
  showNotification.value = true;
};

// Lifecycle
onMounted(async () => {
  await loadStores();
});

// Watch for filter changes
watch(() => search.filters, async () => {
  await loadStores(true);
}, { deep: true });
</script>

<template>
  <div class="w-full">
    <!-- Offline Alert -->
    <PageOfflineAlert :show-only-on-home-page="true" />

    <!-- Header -->
    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
      {{ t('stores.title') }}
    </h1>

    <!-- Search, Filter and Refresh Bar -->
    <SearchFilter v-model:search="search.query" v-model:filters="search.filters" :filter-config="filterConfig"
      :placeholder="t('stores.search')" :loading="isLoading" @search="handleSearchFilterSearch"
      @filter-change="handleSearchFilterChange" @reset="handleSearchFilterReset" class="mb-6">
      <template #actions>
        <button @click="handleRefresh" :disabled="isLoading"
          class="inline-flex items-center gap-2 px-4 py-2.5 bg-white dark:bg-neutral-800 border border-gray-300 dark:border-neutral-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-all duration-200 hover:bg-gray-50 dark:hover:bg-neutral-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-neutral-800 disabled:opacity-50 disabled:cursor-not-allowed">
          <ArrowPathIcon class="w-4 h-4" :class="{ 'animate-spin': isLoading }" />
          <span class="text-sm">{{ t('common.refresh') }}</span>
        </button>
      </template>
    </SearchFilter>

    <!-- Error State -->
    <div v-if="hasError" class="text-center py-12">
      <ExclamationTriangleIcon class="w-16 h-16 text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {{ t('stores.errorLoading') }}
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
      <button @click="handleRefresh"
        class="inline-flex items-center gap-2 px-4 py-2.5 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md dark:focus:ring-offset-neutral-800">
        <ArrowPathIcon class="w-4 h-4" />
        {{ t('stores.retryLoading') }}
      </button>
    </div>

    <!-- Loading State -->
    <div v-else-if="isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <SkeletonLoader v-for="i in 12" :key="i" type="card" class="h-64" />
    </div>

    <!-- Empty State -->
    <div v-else-if="showEmptyState" class="text-center py-12">
      <div class="w-16 h-16 bg-gray-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-gray-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
          </path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {{ t('stores.noStores') }}
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        {{ t('stores.noStoresDescription') }}
      </p>
    </div>

    <!-- Stores Grid -->
    <div v-else>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <StoreCard v-for="store in stores" :key="store.id" :store="store" :loading="isLoading"
          @see-stock="handleSeeStock" @order="handleOrder" />
      </div>

      <!-- Pagination -->
      <div v-if="pagination.totalPages > 1" class="flex justify-center">
        <Pagination :current-page="pagination.page" :total-items="pagination.total" :items-per-page="pagination.limit"
          :show-items-per-page="false" :show-info="false" :items-per-page-options="[6, 12, 24, 48]"
          @update:current-page="handlePageChange" @update:items-per-page="handleItemsPerPageChange" />
      </div>
    </div>

    <!-- Order Request Modal -->
    <OrderRequestModal v-if="showOrderModal && selectedStore" :order-request="{
      id: `STORE-REQ-${Date.now()}`,
      userId: 'current-user',
      userName: 'Current User',
      userPhone: '+20 1234567890',
      serviceType: 'store-order',
      location: {
        address: selectedStore.location.address,
        coordinates: selectedStore.location.coordinates || { latitude: 0, longitude: 0 }
      },
      medications: [],
      totalAmount: 0,
      pointsToEarn: 0,
      status: 'pending',
      createdAt: new Date().toISOString(),
      estimatedDeliveryTime: '30-45 minutes'
    }" @close="closeOrderModal" @action-completed="handleOrderCompleted" />

    <!-- Notification -->
    <Notification v-if="showNotification" :message="notificationMessage" :type="notificationType"
      @close="showNotification = false" />
  </div>
</template>
