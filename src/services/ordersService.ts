import { ref, computed } from 'vue';
import type {
  Order,
  Invoice,
  TopSellingMedication,
  DeliveryOrder,
  ReservedMedication,
  RevenueSummary,
  OrderFilterPeriod,
  InvoiceFilterPeriod,
  DeliveryFilterPeriod,
  DeliveryStatusFilter,
  DeliveryPartnerFilter,
  ReservationStatusFilter,
  ReservationSourceFilter,
  DateRange,
  TopSellingSort,
  OrdersViewState
} from '../types/orders';
import type { ApiResponse } from '../types/dashboard';

// Cache for orders data
const ordersDataCache = ref<{
  orders: Order[];
  invoices: Invoice[];
  topSellingMedications: TopSellingMedication[];
  deliveryOrders: DeliveryOrder[];
  reservedMedications: ReservedMedication[];
  revenueSummary: RevenueSummary;
} | null>(null);

const isLoading = ref(false);
const error = ref<string | null>(null);
const lastFetchTime = ref<number | null>(null);

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

/**
 * Check if cache is valid
 */
const isCacheValid = computed(() => {
  if (!ordersDataCache.value || !lastFetchTime.value) return false;
  return Date.now() - lastFetchTime.value < CACHE_EXPIRATION;
});

/**
 * Fetch orders data
 */
export async function fetchOrdersData(): Promise<ApiResponse<OrdersViewState>> {
  try {
    // Return cached data if valid
    if (isCacheValid.value && ordersDataCache.value) {
      return {
        success: true,
        data: {
          ...ordersDataCache.value,
          orderFilter: 'this-month',
          invoiceFilter: 'this-month',
          revenueFilter: 'this-month',
          topSellingSort: 'most-sold',
          activeTab: 'orders',
          isLoading: false,
          error: null
        }
      };
    }

    isLoading.value = true;
    error.value = null;

    // In a real application, this would be an API call
    // For now, we'll simulate an API call with a timeout
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock data
    const data = {
      orders: getMockOrders(),
      invoices: getMockInvoices(),
      topSellingMedications: getMockTopSellingMedications(),
      deliveryOrders: getMockDeliveryOrders(),
      reservedMedications: getMockReservedMedications(),
      revenueSummary: getMockRevenueSummary(),
      orderFilter: 'this-month' as OrderFilterPeriod,
      invoiceFilter: 'this-month' as InvoiceFilterPeriod,
      revenueFilter: 'this-month' as InvoiceFilterPeriod,
      deliveryFilter: 'this-month' as DeliveryFilterPeriod,
      deliveryStatusFilter: 'all' as DeliveryStatusFilter,
      deliveryPartnerFilter: 'all' as DeliveryPartnerFilter,
      reservationStatusFilter: 'all' as ReservationStatusFilter,
      reservationSourceFilter: 'all' as ReservationSourceFilter,
      topSellingSort: 'most-sold' as TopSellingSort,
      activeTab: 'orders' as 'orders' | 'invoices' | 'revenue' | 'top-selling' | 'delivery' | 'reserved',
      isLoading: false,
      error: null
    };

    // Update cache
    ordersDataCache.value = {
      orders: data.orders,
      invoices: data.invoices,
      topSellingMedications: data.topSellingMedications,
      deliveryOrders: data.deliveryOrders,
      reservedMedications: data.reservedMedications,
      revenueSummary: data.revenueSummary
    };
    lastFetchTime.value = Date.now();

    return { success: true, data };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    error.value = errorMessage;
    return { success: false, error: errorMessage };
  } finally {
    isLoading.value = false;
  }
}

/**
 * Get loading state
 */
export function getOrdersLoadingState() {
  return { isLoading, error };
}

/**
 * Clear orders data cache
 */
export function clearOrdersCache() {
  ordersDataCache.value = null;
  lastFetchTime.value = null;
}

/**
 * Filter orders by period
 */
export function filterOrdersByPeriod(orders: Order[], period: OrderFilterPeriod): Order[] {
  // If 'all-time' is selected, return all orders
  if (period === 'all-time') {
    return orders;
  }
  
  const now = new Date();
  const startDate = new Date();

  switch (period) {
    case 'this-month':
      startDate.setDate(1); // First day of current month
      break;
    case 'last-6-months':
      startDate.setMonth(now.getMonth() - 6);
      break;
    case 'last-year':
      startDate.setFullYear(now.getFullYear() - 1);
      break;
    case 'last-5-years':
      startDate.setFullYear(now.getFullYear() - 5);
      break;
  }

  return orders.filter(order => new Date(order.date) >= startDate);
}

/**
 * Filter invoices by period
 */
export function filterInvoicesByPeriod(invoices: Invoice[], period: InvoiceFilterPeriod, customRange?: DateRange): Invoice[] {
  const now = new Date();
  const startDate = new Date();

  switch (period) {
    case 'today':
      startDate.setHours(0, 0, 0, 0); // Start of today
      break;
    case 'this-week':
      startDate.setDate(now.getDate() - now.getDay()); // First day of current week (Sunday)
      startDate.setHours(0, 0, 0, 0);
      break;
    case 'this-month':
      startDate.setDate(1); // First day of current month
      startDate.setHours(0, 0, 0, 0);
      break;
    case 'custom':
      if (customRange) {
        return invoices.filter(invoice => {
          const invoiceDate = new Date(invoice.date);
          return invoiceDate >= new Date(customRange.startDate) &&
            invoiceDate <= new Date(customRange.endDate);
        });
      }
      return invoices;
  }

  return invoices.filter(invoice => new Date(invoice.date) >= startDate);
}

/**
 * Sort top selling medications
 */
export function sortTopSellingMedications(medications: TopSellingMedication[], sortBy: TopSellingSort): TopSellingMedication[] {
  return [...medications].sort((a, b) => {
    if (sortBy === 'most-sold') {
      return b.unitsSold - a.unitsSold;
    } else {
      return b.totalSales - a.totalSales;
    }
  });
}

/**
 * Filter delivery orders by period, status, and partner
 */
export function filterDeliveryOrders(
  deliveryOrders: DeliveryOrder[],
  period: DeliveryFilterPeriod,
  statusFilter: DeliveryStatusFilter = 'all',
  partnerFilter: DeliveryPartnerFilter = 'all'
): DeliveryOrder[] {
  // First filter by period
  const now = new Date();
  const startDate = new Date();

  switch (period) {
    case 'today':
      startDate.setHours(0, 0, 0, 0); // Start of today
      break;
    case 'last-7-days':
      startDate.setDate(now.getDate() - 7);
      break;
    case 'this-month':
      startDate.setDate(1); // First day of current month
      startDate.setHours(0, 0, 0, 0);
      break;
    case 'all':
      // No date filtering
      startDate.setFullYear(2000); // Far in the past
      break;
  }

  // Apply period filter
  let filtered = deliveryOrders.filter(order => new Date(order.deliveryTime) >= startDate);

  // Apply status filter if not 'all'
  if (statusFilter !== 'all') {
    filtered = filtered.filter(order => order.status === statusFilter);
  }

  // Apply partner filter if not 'all'
  if (partnerFilter !== 'all') {
    filtered = filtered.filter(order => order.partner === partnerFilter);
  }

  return filtered;
}

/**
 * Filter reserved medications by status and source
 */
export function filterReservedMedications(
  reservedMedications: ReservedMedication[],
  statusFilter: ReservationStatusFilter = 'all',
  sourceFilter: ReservationSourceFilter = 'all'
): ReservedMedication[] {
  let filtered = [...reservedMedications];

  // Apply status filter if not 'all'
  if (statusFilter !== 'all') {
    filtered = filtered.filter(med => med.status === statusFilter);
  }

  // Apply source filter if not 'all'
  if (sourceFilter !== 'all') {
    filtered = filtered.filter(med => med.source === sourceFilter);
  }

  return filtered;
}

// Mock data functions
function getMockOrders(): Order[] {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  const lastWeek = new Date(today);
  lastWeek.setDate(lastWeek.getDate() - 7);

  const lastMonth = new Date(today);
  lastMonth.setMonth(lastMonth.getMonth() - 1);

  return [
    {
      id: 'ORD-10001',
      customerName: 'Cairo Central Pharmacy',
      totalAmount: 1250.75,
      date: today.toISOString(),
      status: 'delivered'
    },
    {
      id: 'ORD-10002',
      customerName: 'Alexandria Medical Center',
      totalAmount: 875.50,
      date: yesterday.toISOString(),
      status: 'delivered'
    },
    {
      id: 'ORD-10003',
      customerName: 'Giza Health Clinic',
      totalAmount: 450.25,
      date: yesterday.toISOString(),
      status: 'pending'
    },
    {
      id: 'ORD-10004',
      customerName: 'Luxor Pharmacy',
      totalAmount: 1500.00,
      date: lastWeek.toISOString(),
      status: 'delivered'
    },
    {
      id: 'ORD-10005',
      customerName: 'Aswan Medical Supplies',
      totalAmount: 950.75,
      date: lastWeek.toISOString(),
      status: 'canceled'
    },
    {
      id: 'ORD-10006',
      customerName: 'Hurghada Health Center',
      totalAmount: 2250.50,
      date: lastMonth.toISOString(),
      status: 'pending'
    },
    {
      id: 'ORD-10007',
      customerName: 'Sharm El Sheikh Clinic',
      totalAmount: 1750.25,
      date: lastMonth.toISOString(),
      status: 'delivered'
    },
    {
      id: 'ORD-10008',
      customerName: 'Mansoura Pharmacy',
      totalAmount: 825.00,
      date: lastMonth.toISOString(),
      status: 'delivered'
    },
    {
      id: 'ORD-10009',
      customerName: 'Tanta Medical Center',
      totalAmount: 1875.50,
      date: today.toISOString(),
      status: 'pending'
    },
    {
      id: 'ORD-10010',
      customerName: 'Damietta Pharmacy',
      totalAmount: 1125.25,
      date: yesterday.toISOString(),
      status: 'delivered'
    }
  ];
}

function getMockInvoices(): Invoice[] {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  const lastWeek = new Date(today);
  lastWeek.setDate(lastWeek.getDate() - 7);

  const lastMonth = new Date(today);
  lastMonth.setMonth(lastMonth.getMonth() - 1);

  return [
    {
      id: 'INV-20001',
      orderId: 'ORD-10001',
      date: today.toISOString(),
      totalAmount: 1250.75,
      paymentMethod: 'credit',
      isPaid: true,
      pdfUrl: '/invoices/INV-20001.pdf'
    },
    {
      id: 'INV-20002',
      orderId: 'ORD-10002',
      date: yesterday.toISOString(),
      totalAmount: 875.50,
      paymentMethod: 'bank-transfer',
      isPaid: true,
      pdfUrl: '/invoices/INV-20002.pdf'
    },
    {
      id: 'INV-20003',
      orderId: 'ORD-10003',
      date: yesterday.toISOString(),
      totalAmount: 450.25,
      paymentMethod: 'wallet',
      isPaid: false
    },
    {
      id: 'INV-20004',
      orderId: 'ORD-10004',
      date: lastWeek.toISOString(),
      totalAmount: 1500.00,
      paymentMethod: 'cash',
      isPaid: true,
      pdfUrl: '/invoices/INV-20004.pdf'
    },
    {
      id: 'INV-20005',
      orderId: 'ORD-10005',
      date: lastWeek.toISOString(),
      totalAmount: 950.75,
      paymentMethod: 'credit',
      isPaid: false
    },
    {
      id: 'INV-20006',
      orderId: 'ORD-10006',
      date: lastMonth.toISOString(),
      totalAmount: 2250.50,
      paymentMethod: 'bank-transfer',
      isPaid: false
    },
    {
      id: 'INV-20007',
      orderId: 'ORD-10007',
      date: lastMonth.toISOString(),
      totalAmount: 1750.25,
      paymentMethod: 'wallet',
      isPaid: true,
      pdfUrl: '/invoices/INV-20007.pdf'
    },
    {
      id: 'INV-20008',
      orderId: 'ORD-10008',
      date: lastMonth.toISOString(),
      totalAmount: 825.00,
      paymentMethod: 'cash',
      isPaid: true,
      pdfUrl: '/invoices/INV-20008.pdf'
    },
    {
      id: 'INV-20009',
      orderId: 'ORD-10009',
      date: today.toISOString(),
      totalAmount: 1875.50,
      paymentMethod: 'credit',
      isPaid: false
    },
    {
      id: 'INV-20010',
      orderId: 'ORD-10010',
      date: yesterday.toISOString(),
      totalAmount: 1125.25,
      paymentMethod: 'wallet',
      isPaid: true,
      pdfUrl: '/invoices/INV-20010.pdf'
    }
  ];
}

function getMockTopSellingMedications(): TopSellingMedication[] {
  return [
    {
      id: 1,
      name: 'Paracetamol 500mg',
      category: 'Pain Relief',
      unitsSold: 1250,
      totalSales: 6250.00
    },
    {
      id: 2,
      name: 'Amoxicillin 250mg',
      category: 'Antibiotics',
      unitsSold: 850,
      totalSales: 8500.00
    },
    {
      id: 3,
      name: 'Vitamin C 1000mg',
      category: 'Vitamins',
      unitsSold: 1100,
      totalSales: 5500.00
    },
    {
      id: 4,
      name: 'Ibuprofen 400mg',
      category: 'Pain Relief',
      unitsSold: 950,
      totalSales: 4750.00
    },
    {
      id: 5,
      name: 'Omeprazole 20mg',
      category: 'Digestive',
      unitsSold: 750,
      totalSales: 7500.00
    },
    {
      id: 6,
      name: 'Metformin 500mg',
      category: 'Diabetes',
      unitsSold: 650,
      totalSales: 9750.00
    },
    {
      id: 7,
      name: 'Atorvastatin 10mg',
      category: 'Cholesterol',
      unitsSold: 550,
      totalSales: 8250.00
    },
    {
      id: 8,
      name: 'Salbutamol Inhaler',
      category: 'Respiratory',
      unitsSold: 450,
      totalSales: 11250.00
    }
  ];
}

function getMockRevenueSummary(): RevenueSummary {
  // Calculate actual values from the orders
  const orders = getMockOrders();
  const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
  const numberOfOrders = orders.length;
  const averageOrderValue = totalRevenue / numberOfOrders;

  return {
    totalRevenue,
    numberOfOrders,
    averageOrderValue
  };
}

function getMockDeliveryOrders(): DeliveryOrder[] {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  const lastWeek = new Date(today);
  lastWeek.setDate(lastWeek.getDate() - 7);

  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return [
    {
      id: 'DEL-001',
      orderId: 'ORD-10001',
      customerName: 'Ahmed Mohamed',
      address: '123 Tahrir St, Cairo',
      itemsCount: 3,
      deliveryTime: tomorrow.toISOString(),
      status: 'pending',
      partner: 'talabat',
      contactNumber: '+20 123 456 7890',
      notes: 'Call before delivery'
    },
    {
      id: 'DEL-002',
      orderId: 'ORD-10002',
      customerName: 'Sara Ali',
      address: '45 Maadi St, Cairo',
      itemsCount: 2,
      deliveryTime: today.toISOString(),
      status: 'out_for_delivery',
      partner: 'pharmacy_driver',
      contactNumber: '+20 111 222 3333'
    },
    {
      id: 'DEL-003',
      orderId: 'ORD-10003',
      customerName: 'Mohamed Hassan',
      address: '78 Alexandria Corniche, Alexandria',
      itemsCount: 5,
      deliveryTime: yesterday.toISOString(),
      status: 'delivered',
      partner: 'careem',
      contactNumber: '+20 100 200 3000'
    },
    {
      id: 'DEL-004',
      orderId: 'ORD-10004',
      customerName: 'Fatima Kamal',
      address: '15 Giza St, Giza',
      itemsCount: 1,
      deliveryTime: today.toISOString(),
      status: 'pending',
      partner: 'other',
      notes: 'Customer will pick up from nearby branch'
    },
    {
      id: 'DEL-005',
      orderId: 'ORD-10005',
      customerName: 'Omar Khaled',
      address: '32 Zamalek St, Cairo',
      itemsCount: 4,
      deliveryTime: tomorrow.toISOString(),
      status: 'pending',
      partner: 'talabat',
      contactNumber: '+20 155 666 7777'
    },
    {
      id: 'DEL-006',
      orderId: 'ORD-10006',
      customerName: 'Laila Mahmoud',
      address: '90 Heliopolis St, Cairo',
      itemsCount: 2,
      deliveryTime: today.toISOString(),
      status: 'out_for_delivery',
      partner: 'careem',
      contactNumber: '+20 122 333 4444'
    },
    {
      id: 'DEL-007',
      orderId: 'ORD-10007',
      customerName: 'Youssef Ibrahim',
      address: '55 Dokki St, Giza',
      itemsCount: 3,
      deliveryTime: yesterday.toISOString(),
      status: 'delivered',
      partner: 'pharmacy_driver'
    },
    {
      id: 'DEL-008',
      orderId: 'ORD-10008',
      customerName: 'Nour Ahmed',
      address: '123 Mohandessin St, Giza',
      itemsCount: 6,
      deliveryTime: lastWeek.toISOString(),
      status: 'delivered',
      partner: 'talabat'
    }
  ];
}

function getMockReservedMedications(): ReservedMedication[] {
  const today = new Date();

  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  const lastWeek = new Date(today);
  lastWeek.setDate(lastWeek.getDate() - 7);

  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const nextWeek = new Date(today);
  nextWeek.setDate(nextWeek.getDate() + 7);

  const lastMonth = new Date(today);
  lastMonth.setMonth(lastMonth.getMonth() - 1);

  return [
    {
      id: 1,
      medicationId: 101,
      medicationName: 'Paracetamol 500mg',
      quantityReserved: 20,
      reservedBy: 'Ahmed Mohamed',
      source: 'app',
      reservationDate: yesterday.toISOString(),
      expiryDate: tomorrow.toISOString(),
      status: 'active'
    },
    {
      id: 2,
      medicationId: 102,
      medicationName: 'Amoxicillin 250mg',
      quantityReserved: 15,
      reservedBy: 'Sara Ali',
      source: 'phone',
      reservationDate: lastWeek.toISOString(),
      expiryDate: today.toISOString(),
      status: 'active'
    },
    {
      id: 3,
      medicationId: 103,
      medicationName: 'Vitamin C 1000mg',
      quantityReserved: 30,
      reservedBy: 'Mohamed Hassan',
      source: 'website',
      reservationDate: lastMonth.toISOString(),
      expiryDate: lastWeek.toISOString(),
      status: 'expired'
    },
    {
      id: 4,
      medicationId: 104,
      medicationName: 'Ibuprofen 400mg',
      quantityReserved: 10,
      reservedBy: 'Fatima Kamal',
      source: 'in_person',
      reservationDate: today.toISOString(),
      expiryDate: nextWeek.toISOString(),
      status: 'active'
    },
    {
      id: 5,
      medicationId: 105,
      medicationName: 'Omeprazole 20mg',
      quantityReserved: 25,
      reservedBy: 'Omar Khaled',
      source: 'app',
      reservationDate: yesterday.toISOString(),
      expiryDate: tomorrow.toISOString(),
      status: 'active'
    },
    {
      id: 6,
      medicationId: 106,
      medicationName: 'Metformin 500mg',
      quantityReserved: 60,
      reservedBy: 'Laila Mahmoud',
      source: 'phone',
      reservationDate: lastMonth.toISOString(),
      expiryDate: lastWeek.toISOString(),
      status: 'expired'
    },
    {
      id: 7,
      medicationId: 107,
      medicationName: 'Atorvastatin 10mg',
      quantityReserved: 45,
      reservedBy: 'Youssef Ibrahim',
      source: 'website',
      reservationDate: today.toISOString(),
      expiryDate: nextWeek.toISOString(),
      status: 'active'
    },
    {
      id: 8,
      medicationId: 108,
      medicationName: 'Salbutamol Inhaler',
      quantityReserved: 5,
      reservedBy: 'Nour Ahmed',
      source: 'in_person',
      reservationDate: yesterday.toISOString(),
      expiryDate: tomorrow.toISOString(),
      status: 'active'
    }
  ];
}
