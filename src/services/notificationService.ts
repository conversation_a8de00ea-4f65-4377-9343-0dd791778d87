import { ref, computed } from 'vue';
import type { Notification } from '../types/notifications';
import type { ApiResponse } from '../types/dashboard';

// Cache for notifications data
const notificationsCache = ref<Notification[] | null>(null);
const isLoading = ref(false);
const error = ref<string | null>(null);
const lastFetchTime = ref<number | null>(null);

// Cache expiration time (2 minutes)
const CACHE_EXPIRATION = 2 * 60 * 1000;

/**
 * Check if cache is valid
 */
const isCacheValid = computed(() => {
  if (!notificationsCache.value || !lastFetchTime.value) return false;
  return Date.now() - lastFetchTime.value < CACHE_EXPIRATION;
});

/**
 * Fetch all notifications
 */
export async function fetchNotifications(): Promise<ApiResponse<Notification[]>> {
  // Return cached data if valid
  if (isCacheValid.value && notificationsCache.value) {
    return { success: true, data: notificationsCache.value };
  }

  // Otherwise, fetch new data
  isLoading.value = true;
  error.value = null;

  try {
    // In a real app, this would be an API call
    // For now, we'll use mock data
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay

    const data = getMockNotifications();

    // Update cache
    notificationsCache.value = data;
    lastFetchTime.value = Date.now();

    return { success: true, data };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    error.value = errorMessage;
    return { success: false, error: errorMessage };
  } finally {
    isLoading.value = false;
  }
}

/**
 * Get loading state
 */
export function getNotificationsLoadingState() {
  return { isLoading, error };
}

/**
 * Clear notifications cache
 */
export function clearNotificationsCache() {
  notificationsCache.value = null;
  lastFetchTime.value = null;
}

/**
 * Mark a notification as read
 */
export async function markNotificationAsRead(id: number): Promise<ApiResponse<boolean>> {
  try {
    // In a real app, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 200)); // Simulate network delay

    // Update the cache
    if (notificationsCache.value) {
      const notification = notificationsCache.value.find(n => n.id === id);
      if (notification) {
        notification.read = true;
      }
    }

    return { success: true, data: true };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return { success: false, error: errorMessage };
  }
}

/**
 * Mark all notifications as read
 */
export async function markAllNotificationsAsRead(): Promise<ApiResponse<boolean>> {
  try {
    // In a real app, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate network delay

    // Update the cache
    if (notificationsCache.value) {
      notificationsCache.value.forEach(notification => {
        notification.read = true;
      });
    }

    return { success: true, data: true };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    return { success: false, error: errorMessage };
  }
}

/**
 * Generate mock notifications data
 */
function getMockNotifications(): Notification[] {
  return [
    {
      id: 1,
      title: 'New medication added',
      message: 'Paracetamol 500mg has been added to inventory',
      time: '10 minutes ago',
      type: 'medication',
      read: false,
      priority: 'medium',
      category: 'Inventory',
      relatedId: '123',
      relatedRoute: 'view-stock'
    },
    {
      id: 2,
      title: 'Low stock alert',
      message: 'Amoxicillin 250mg is running low',
      time: '1 hour ago',
      type: 'stock',
      read: false,
      priority: 'high',
      category: 'Stock Alert',
      relatedId: '456',
      relatedRoute: 'view-stock'
    },
    {
      id: 3,
      title: 'System update',
      message: 'DAWINII system will be updated tonight at 2 AM',
      time: '3 hours ago',
      type: 'system',
      read: true,
      priority: 'low',
      category: 'System'
    },
    {
      id: 4,
      title: 'New order received',
      message: 'Order #12345 has been placed',
      time: '5 hours ago',
      type: 'order',
      read: true,
      priority: 'medium',
      category: 'Orders',
      relatedId: '12345',
      relatedRoute: 'orders-invoices'
    },
    {
      id: 5,
      title: 'Payment received',
      message: 'Payment for order #12345 has been received',
      time: '6 hours ago',
      type: 'order',
      read: true,
      priority: 'low',
      category: 'Payments',
      relatedId: '12345',
      relatedRoute: 'orders-invoices'
    },
    {
      id: 6,
      title: 'Medication expiring soon',
      message: 'Ibuprofen 400mg will expire in 30 days',
      time: '1 day ago',
      type: 'medication',
      read: true,
      priority: 'medium',
      category: 'Expiry Alert',
      relatedId: '789',
      relatedRoute: 'view-stock'
    },
    {
      id: 7,
      title: 'New delivery order',
      message: 'New delivery order #54321 has been assigned',
      time: '2 days ago',
      type: 'order',
      read: true,
      priority: 'medium',
      category: 'Delivery',
      relatedId: '54321',
      relatedRoute: 'orders-invoices'
    },
    {
      id: 8,
      title: 'System maintenance',
      message: 'System maintenance scheduled for tomorrow at 1 AM',
      time: '3 days ago',
      type: 'system',
      read: true,
      priority: 'high',
      category: 'Maintenance'
    },
    {
      id: 9,
      title: 'Stock replenished',
      message: 'Amoxicillin 250mg has been restocked',
      time: '4 days ago',
      type: 'stock',
      read: true,
      priority: 'low',
      category: 'Inventory',
      relatedId: '456',
      relatedRoute: 'view-stock'
    },
    {
      id: 10,
      title: 'New feature available',
      message: 'Check out the new reporting features in the dashboard',
      time: '5 days ago',
      type: 'system',
      read: true,
      priority: 'low',
      category: 'Features',
      relatedRoute: 'dashboard'
    }
  ];
}
