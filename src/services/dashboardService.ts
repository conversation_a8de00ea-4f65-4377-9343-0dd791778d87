import { ref, computed } from 'vue';
import type {
    DashboardData,
    InventoryData,
    SalesData,
    DeliveryData,
    AppOrdersData,
    StoreOrdersData,
    RevenueDataPoint,
    ApiResponse
} from '../types/dashboard';

// Cache for dashboard data
const dashboardDataCache = ref<DashboardData | null>(null);
const isLoading = ref(false);
const error = ref<string | null>(null);
const lastFetchTime = ref<number | null>(null);

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

/**
 * Check if cache is valid
 */
const isCacheValid = computed(() => {
    if (!dashboardDataCache.value || !lastFetchTime.value) return false;
    return Date.now() - lastFetchTime.value < CACHE_EXPIRATION;
});

/**
 * Fetch dashboard data from API
 * Currently using mock data, but can be replaced with actual API calls
 */
export async function fetchDashboardData(): Promise<ApiResponse<DashboardData>> {
    try {
        // Return cached data if valid
        if (isCacheValid.value && dashboardDataCache.value) {
            return { success: true, data: dashboardDataCache.value };
        }

        isLoading.value = true;
        error.value = null;

        // In a real application, this would be an API call
        // For now, we'll simulate an API call with a timeout
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock data - in a real app, this would come from the API
        const data: DashboardData = {
            inventory: getMockInventoryData(),
            sales: getMockSalesData(),
            delivery: getMockDeliveryData(),
            appOrders: getMockAppOrdersData(),
            storeOrders: getMockStoreOrdersData(),
            monthlyRevenue: getMockMonthlyRevenueData()
        };

        // Update cache
        dashboardDataCache.value = data;
        lastFetchTime.value = Date.now();

        return { success: true, data };
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        error.value = errorMessage;
        return { success: false, error: errorMessage };
    } finally {
        isLoading.value = false;
    }
}

/**
 * Get loading state
 */
export function getDashboardLoadingState() {
    return { isLoading, error };
}

/**
 * Clear dashboard data cache
 */
export function clearDashboardCache() {
    dashboardDataCache.value = null;
    lastFetchTime.value = null;
}

// Mock data functions
function getMockInventoryData(): InventoryData {
    return {
        totalMedications: 1248,
        outOfStock: 23,
        expiringSoon: 45,
        recentlyAdded: 12,
        lowStock: 67
    };
}

function getMockSalesData(): SalesData {
    return {
        todayInvoices: 32,
        todayRevenue: 4850.75,
        topSelling: [
            { name: 'Paracetamol 500mg', count: 48, revenue: 720 },
            { name: 'Amoxicillin 250mg', count: 36, revenue: 540 },
            { name: 'Vitamin C 1000mg', count: 24, revenue: 360 }
        ]
    };
}



function getMockDeliveryData(): DeliveryData {
    return {
        new: 8,
        inTransit: 5,
        completed: 15
    };
}

function getMockAppOrdersData(): AppOrdersData {
    return {
        totalUsers: 1245,
        activeUsers: 876,
        totalOrders: 342,
        averageOrderValue: 78.50,
        pendingReviews: 18
    };
}

function getMockStoreOrdersData(): StoreOrdersData {
    return {
        pending: 8,
        inTransit: 15,
        completed: 142,
        rejected: 3
    };
}

function getMockMonthlyRevenueData(): RevenueDataPoint[] {
    return [
        { month: 'Jan', revenue: 12500 },
        { month: 'Feb', revenue: 14200 },
        { month: 'Mar', revenue: 16800 },
        { month: 'Apr', revenue: 15300 },
        { month: 'May', revenue: 17500 },
        { month: 'Jun', revenue: 19200 }
    ];
}