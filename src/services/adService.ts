export interface AdData {
  id: string;
  imageUrl: string;
  medicationName: string;
  companyName: string;
  tagline: string;
  ctaLink?: string;
}

// Mock ad data for pharmaceutical companies
export const mockAds: AdData[] = [
  {
    id: 'ad-1',
    imageUrl: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    medicationName: 'CardioMax Pro',
    companyName: 'Pharma Solutions Inc.',
    tagline: 'Advanced cardiovascular protection with proven results for better heart health.',
    ctaLink: '/medications/cardiomax-pro'
  },
  {
    id: 'ad-2',
    imageUrl: 'https://images.unsplash.com/photo-1576671081837-49000212a370?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    medicationName: 'DiabetCare Plus',
    companyName: 'MediCore Pharmaceuticals',
    tagline: 'Revolutionary diabetes management with 24-hour glucose control technology.',
    ctaLink: '/medications/diabetcare-plus'
  },
  {
    id: 'ad-3',
    imageUrl: 'https://images.unsplash.com/photo-1587854692152-cbe660dbde88?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    medicationName: 'NeuroShield',
    companyName: 'BrainHealth Labs',
    tagline: 'Cognitive enhancement and neuroprotection for optimal brain function.',
    ctaLink: '/medications/neuroshield'
  },
  {
    id: 'ad-4',
    imageUrl: 'https://images.unsplash.com/photo-1631549916768-4119b2e5f926?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    medicationName: 'ImmunoBoost',
    companyName: 'Wellness Pharma Group',
    tagline: 'Strengthen your immune system with our advanced formula for year-round protection.',
    ctaLink: '/medications/immunoboost'
  }
];

// Mock ad data in Arabic
export const mockAdsArabic: AdData[] = [
  {
    id: 'ad-1-ar',
    imageUrl: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    medicationName: 'كارديو ماكس برو',
    companyName: 'شركة الحلول الدوائية',
    tagline: 'حماية متقدمة للقلب والأوعية الدموية مع نتائج مثبتة لصحة أفضل للقلب.',
    ctaLink: '/medications/cardiomax-pro'
  },
  {
    id: 'ad-2-ar',
    imageUrl: 'https://images.unsplash.com/photo-1576671081837-49000212a370?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    medicationName: 'دايابت كير بلس',
    companyName: 'شركة ميديكور للأدوية',
    tagline: 'إدارة ثورية لمرض السكري مع تقنية التحكم في الجلوكوز على مدار 24 ساعة.',
    ctaLink: '/medications/diabetcare-plus'
  },
  {
    id: 'ad-3-ar',
    imageUrl: 'https://images.unsplash.com/photo-1587854692152-cbe660dbde88?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    medicationName: 'نيورو شيلد',
    companyName: 'مختبرات صحة الدماغ',
    tagline: 'تعزيز الإدراك وحماية الأعصاب لوظائف مثلى للدماغ.',
    ctaLink: '/medications/neuroshield'
  },
  {
    id: 'ad-4-ar',
    imageUrl: 'https://images.unsplash.com/photo-1631549916768-4119b2e5f926?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    medicationName: 'إيمونو بوست',
    companyName: 'مجموعة ويلنس فارما',
    tagline: 'قوي جهازك المناعي بتركيبتنا المتقدمة للحماية على مدار السنة.',
    ctaLink: '/medications/immunoboost'
  }
];

// Service functions
export const getAds = (lang: 'ar' | 'en' = 'en'): AdData[] => {
  return lang === 'ar' ? mockAdsArabic : mockAds;
};

export const getAdById = (id: string, lang: 'ar' | 'en' = 'en'): AdData | undefined => {
  const ads = getAds(lang);
  return ads.find(ad => ad.id === id);
};

// Simulate API call with delay
export const fetchAds = async (lang: 'ar' | 'en' = 'en'): Promise<AdData[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getAds(lang));
    }, 500); // Simulate network delay
  });
};
