import { ref, computed } from 'vue';
import type { MedicationItem, StockStatus, ExpiryStatus } from '../types/inventory';

// Cache for inventory data
const inventoryDataCache = ref<MedicationItem[] | null>(null);
const isLoading = ref(false);
const error = ref<string | null>(null);
const lastFetchTime = ref<number | null>(null);

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

/**
 * Check if cache is valid
 */
const isCacheValid = computed(() => {
  if (!inventoryDataCache.value || !lastFetchTime.value) return false;
  return Date.now() - lastFetchTime.value < CACHE_EXPIRATION;
});

/**
 * Get inventory data
 */
export async function getInventoryData() {
  // If we have valid cached data, return it
  if (isCacheValid.value && inventoryDataCache.value) {
    return { success: true, data: inventoryDataCache.value };
  }

  // Otherwise, fetch new data
  isLoading.value = true;
  error.value = null;

  try {
    // In a real app, this would be an API call
    // For now, we'll use mock data
    await new Promise(resolve => setTimeout(resolve, 800)); // Simulate network delay

    const data = getMockInventoryData();

    // Update cache
    inventoryDataCache.value = data;
    lastFetchTime.value = Date.now();

    return { success: true, data };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    error.value = errorMessage;
    return { success: false, error: errorMessage };
  } finally {
    isLoading.value = false;
  }
}

/**
 * Get loading state
 */
export function getInventoryLoadingState() {
  return { isLoading, error };
}

/**
 * Clear inventory data cache
 */
export function clearInventoryCache() {
  inventoryDataCache.value = null;
  lastFetchTime.value = null;
}

/**
 * Generate mock inventory data
 */
function getMockInventoryData(): MedicationItem[] {
  const today = new Date();

  // Helper function to add days to a date
  const addDays = (date: Date, days: number) => {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  };

  // Helper function to format date as YYYY-MM-DD
  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  // Helper function to determine stock status
  const getStockStatus = (quantity: number): StockStatus => {
    if (quantity <= 0) return 'out-of-stock';
    if (quantity < 20) return 'low-stock';
    return 'in-stock';
  };

  // Helper function to determine expiry status
  const getExpiryStatus = (expiryDate: string): ExpiryStatus => {
    const expiry = new Date(expiryDate);
    const now = new Date();

    if (expiry < now) return 'expired';

    // If expiry is within 30 days, it's expiring soon
    const thirtyDaysFromNow = addDays(now, 30);
    if (expiry <= thirtyDaysFromNow) return 'expiring-soon';

    return 'valid';
  };

  // Generate 50 mock medication items
  return [
    {
      id: 1,
      name: 'Paracetamol 500mg',
      category: 'Pain Relief',
      quantity: 150,
      expiryDate: formatDate(addDays(today, 365)),
      addedDate: formatDate(addDays(today, -30)), // Added 30 days ago
      pricePerUnit: 5.99,
      status: 'in-stock' as StockStatus,
      expiryStatus: 'valid' as ExpiryStatus
    },
    {
      id: 2,
      name: 'Amoxicillin 250mg',
      category: 'Antibiotics',
      quantity: 15,
      expiryDate: formatDate(addDays(today, 20)),
      addedDate: formatDate(addDays(today, -5)), // Added 5 days ago (recent)
      pricePerUnit: 12.50,
      status: 'low-stock' as StockStatus,
      expiryStatus: 'expiring-soon' as ExpiryStatus
    },
    {
      id: 3,
      name: 'Ibuprofen 400mg',
      category: 'Pain Relief',
      quantity: 200,
      expiryDate: formatDate(addDays(today, 180)),
      addedDate: formatDate(addDays(today, -60)), // Added 60 days ago
      pricePerUnit: 4.75,
      status: 'in-stock' as StockStatus,
      expiryStatus: 'valid' as ExpiryStatus
    },
    {
      id: 4,
      name: 'Loratadine 10mg',
      category: 'Allergy',
      quantity: 0,
      expiryDate: formatDate(addDays(today, 90)),
      addedDate: formatDate(addDays(today, -2)), // Added 2 days ago (recent)
      pricePerUnit: 8.25,
      status: 'out-of-stock' as StockStatus,
      expiryStatus: 'valid' as ExpiryStatus
    },
    {
      id: 5,
      name: 'Omeprazole 20mg',
      category: 'Digestive',
      quantity: 90,
      expiryDate: formatDate(addDays(today, -10)),
      addedDate: formatDate(addDays(today, -3)), // Added 3 days ago (recent)
      pricePerUnit: 15.30,
      status: 'in-stock' as StockStatus,
      expiryStatus: 'expired' as ExpiryStatus
    },
    // Add more mock data with different statuses
    ...Array.from({ length: 45 }, (_, i) => {
      const id = i + 6;
      const quantity = Math.floor(Math.random() * 200);
      const daysToExpiry = Math.floor(Math.random() * 400) - 20; // Some items will be expired
      const expiryDate = formatDate(addDays(today, daysToExpiry));

      // Random added date between 1 and 90 days ago
      const daysAgo = Math.floor(Math.random() * 90) + 1;
      const addedDate = formatDate(addDays(today, -daysAgo));

      return {
        id,
        name: `Medication ${id}`,
        category: ['Pain Relief', 'Antibiotics', 'Allergy', 'Digestive', 'Vitamins', 'Cardiovascular'][Math.floor(Math.random() * 6)],
        quantity,
        expiryDate,
        addedDate,
        pricePerUnit: parseFloat((Math.random() * 50 + 2).toFixed(2)),
        status: getStockStatus(quantity),
        expiryStatus: getExpiryStatus(expiryDate)
      };
    })
  ];
}
