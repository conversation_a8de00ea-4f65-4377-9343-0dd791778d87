import { ref, computed } from 'vue';
import type {
  OrderRequest,
  OrderRequestAction,
  OrderRequestActionResult,
  OrderRequestNotification,
  OrderRequestServiceResponse,
  OrderRequestMedication,
  OrderRequestServiceType,
  OrderRequestStatus
} from '../types/orderRequests';

// Reactive state for order requests
const activeOrderRequests = ref<OrderRequest[]>([]);
const orderRequestNotifications = ref<OrderRequestNotification[]>([]);
const isProcessingAction = ref(false);

// Mock WebSocket connection state
const isConnected = ref(false);
const connectionAttempts = ref(0);

/**
 * Initialize the order request service (mock WebSocket connection)
 */
export function initOrderRequestService(): void {
  // Simulate WebSocket connection
  isConnected.value = true;
  connectionAttempts.value = 0;
  
  // Start listening for mock order requests
  startMockOrderRequestListener();
  
  console.log('Order Request Service initialized');
}

/**
 * Cleanup the order request service
 */
export function cleanupOrderRequestService(): void {
  isConnected.value = false;
  activeOrderRequests.value = [];
  orderRequestNotifications.value = [];
  
  console.log('Order Request Service cleaned up');
}

/**
 * Get active order requests
 */
export function getActiveOrderRequests() {
  return computed(() => activeOrderRequests.value);
}

/**
 * Get order request notifications
 */
export function getOrderRequestNotifications() {
  return computed(() => orderRequestNotifications.value);
}

/**
 * Get processing state
 */
export function getProcessingState() {
  return computed(() => isProcessingAction.value);
}

/**
 * Accept an order request
 */
export async function acceptOrderRequest(orderRequestId: string): Promise<OrderRequestActionResult> {
  isProcessingAction.value = true;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update order request status
    const orderRequest = activeOrderRequests.value.find(req => req.id === orderRequestId);
    if (orderRequest) {
      orderRequest.status = 'accepted';
      
      // Remove from active requests after a delay
      setTimeout(() => {
        removeOrderRequest(orderRequestId);
      }, 2000);
      
      return {
        success: true,
        message: 'Order request accepted successfully'
      };
    }
    
    return {
      success: false,
      error: 'Order request not found'
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to accept order request'
    };
  } finally {
    isProcessingAction.value = false;
  }
}

/**
 * Reject an order request
 */
export async function rejectOrderRequest(orderRequestId: string): Promise<OrderRequestActionResult> {
  isProcessingAction.value = true;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Update order request status
    const orderRequest = activeOrderRequests.value.find(req => req.id === orderRequestId);
    if (orderRequest) {
      orderRequest.status = 'rejected';
      
      // Remove from active requests after a delay
      setTimeout(() => {
        removeOrderRequest(orderRequestId);
      }, 1500);
      
      return {
        success: true,
        message: 'Order request rejected successfully'
      };
    }
    
    return {
      success: false,
      error: 'Order request not found'
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to reject order request'
    };
  } finally {
    isProcessingAction.value = false;
  }
}

/**
 * Release a locked medication
 */
export async function releaseLocked(orderRequestId: string, medicationId: number): Promise<OrderRequestActionResult> {
  isProcessingAction.value = true;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 600));
    
    // Find and update the medication status
    const orderRequest = activeOrderRequests.value.find(req => req.id === orderRequestId);
    if (orderRequest) {
      const medication = orderRequest.medications.find(med => med.medicationId === medicationId);
      if (medication && medication.status === 'locked') {
        medication.status = 'available';
        medication.lockedSince = undefined;
        medication.lockDurationHours = undefined;
        
        return {
          success: true,
          message: 'Medication released successfully'
        };
      }
    }
    
    return {
      success: false,
      error: 'Medication not found or not locked'
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to release medication'
    };
  } finally {
    isProcessingAction.value = false;
  }
}

/**
 * Remove an order request from active list
 */
function removeOrderRequest(orderRequestId: string): void {
  const index = activeOrderRequests.value.findIndex(req => req.id === orderRequestId);
  if (index !== -1) {
    activeOrderRequests.value.splice(index, 1);
  }
}

/**
 * Add a new order request notification
 */
function addOrderRequestNotification(orderRequest: OrderRequest): void {
  const notification: OrderRequestNotification = {
    id: `notif-${orderRequest.id}`,
    orderRequestId: orderRequest.id,
    title: `New ${orderRequest.serviceType} Order Request`,
    message: `${orderRequest.userName} has placed a ${orderRequest.serviceType.toLowerCase()} order`,
    timestamp: new Date().toISOString(),
    read: false,
    priority: 'high'
  };
  
  orderRequestNotifications.value.unshift(notification);
  
  // Dispatch custom event for notification
  const event = new CustomEvent('newOrderRequest', {
    detail: { orderRequest, notification }
  });
  window.dispatchEvent(event);
}

/**
 * Start mock order request listener (simulates WebSocket)
 */
function startMockOrderRequestListener(): void {
  // Simulate receiving order requests periodically for demo
  const simulateOrderRequest = () => {
    if (Math.random() > 0.7) { // 30% chance every interval
      const mockOrderRequest = generateMockOrderRequest();
      activeOrderRequests.value.push(mockOrderRequest);
      addOrderRequestNotification(mockOrderRequest);
    }
  };
  
  // Simulate order requests every 30-60 seconds for demo
  setInterval(simulateOrderRequest, 45000);
  
  // Add one immediately for testing
  setTimeout(() => {
    const mockOrderRequest = generateMockOrderRequest();
    activeOrderRequests.value.push(mockOrderRequest);
    addOrderRequestNotification(mockOrderRequest);
  }, 3000);
}

/**
 * Generate mock order request for testing
 */
function generateMockOrderRequest(): OrderRequest {
  const serviceTypes: OrderRequestServiceType[] = ['Reserved', 'Delivery'];
  const userNames = ['Ahmed Mohamed', 'Sara Ali', 'Mohamed Hassan', 'Fatima Ahmed', 'Omar Khaled'];
  const addresses = [
    '123 Tahrir St, Cairo',
    '45 Maadi St, Cairo',
    '78 Alexandria Corniche, Alexandria',
    '12 Zamalek St, Cairo',
    '56 Heliopolis St, Cairo'
  ];
  
  const serviceType = serviceTypes[Math.floor(Math.random() * serviceTypes.length)];
  const userName = userNames[Math.floor(Math.random() * userNames.length)];
  const address = addresses[Math.floor(Math.random() * addresses.length)];
  
  const now = new Date();
  const orderTime = new Date(now.getTime() - Math.random() * 30 * 60 * 1000); // Random time in last 30 minutes
  
  return {
    id: `REQ-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    userId: `USER-${Math.floor(Math.random() * 10000)}`,
    userName,
    userPhone: `+20 1${Math.floor(Math.random() * 90000000) + 10000000}`,
    serviceType,
    location: {
      address,
      coordinates: {
        latitude: 30.0444 + (Math.random() - 0.5) * 0.1,
        longitude: 31.2357 + (Math.random() - 0.5) * 0.1
      }
    },
    pointsToEarn: Math.floor(Math.random() * 50) + 10,
    orderTime: orderTime.toISOString(),
    createdAt: orderTime.toISOString(),
    status: 'pending',
    medications: generateMockMedications(),
    totalAmount: Math.floor(Math.random() * 500) + 100,
    notes: Math.random() > 0.7 ? 'Please call before delivery' : undefined,
    estimatedDeliveryTime: serviceType === 'Delivery' ? 
      new Date(now.getTime() + 2 * 60 * 60 * 1000).toISOString() : undefined,
    reservationExpiryTime: serviceType === 'Reserved' ? 
      new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString() : undefined
  };
}

/**
 * Generate mock medications for order request
 */
function generateMockMedications(): OrderRequestMedication[] {
  const medicationNames = [
    'Paracetamol 500mg',
    'Amoxicillin 250mg',
    'Vitamin C 1000mg',
    'Ibuprofen 400mg',
    'Aspirin 100mg',
    'Omeprazole 20mg'
  ];
  
  const statuses: OrderRequestMedication['status'][] = ['available', 'out_of_stock', 'locked', 'low_stock'];
  const count = Math.floor(Math.random() * 4) + 1; // 1-4 medications
  
  return Array.from({ length: count }, (_, index) => {
    const name = medicationNames[Math.floor(Math.random() * medicationNames.length)];
    const quantity = Math.floor(Math.random() * 5) + 1;
    const unitPrice = Math.floor(Math.random() * 50) + 10;
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    // If locked, add lock information
    const lockedSince = status === 'locked' ? 
      new Date(Date.now() - Math.random() * 10 * 60 * 60 * 1000).toISOString() : undefined;
    const lockDurationHours = lockedSince ? 
      Math.floor((Date.now() - new Date(lockedSince).getTime()) / (1000 * 60 * 60)) : undefined;
    
    return {
      id: index + 1,
      medicationId: Math.floor(Math.random() * 1000) + 1,
      name,
      quantity,
      unitPrice,
      totalPrice: unitPrice * quantity,
      status,
      lockedSince,
      lockDurationHours
    };
  });
}
