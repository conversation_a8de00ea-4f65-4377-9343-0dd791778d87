import { ref } from 'vue';
import type { Review, RatingSummary, ReviewsResponse } from '../types/reviews';

// Generate recent dates for realistic filtering
const generateRecentDate = (daysAgo: number): string => {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  return date.toISOString();
};

// Mock reviews data with recent dates
const mockReviews: Review[] = [
  {
    id: '1',
    customerName: '<PERSON>',
    rating: 5,
    message: 'Excellent service! The medication was delivered quickly and the pharmacist was very helpful in explaining the dosage.',
    date: generateRecentDate(0), // Today
    medicationName: 'Paracetamol 500mg',
    medicationId: 1,
    orderId: 'ORD-2024-001',
    verified: true
  },
  {
    id: '2',
    customerName: 'Fatima Al-Zahra',
    rating: 4,
    message: 'Good pharmacy with reasonable prices. The staff is knowledgeable and friendly.',
    date: generateRecentDate(1), // Yesterday
    medicationName: 'Vitamin D3',
    medicationId: 2,
    orderId: 'ORD-2024-002',
    verified: true
  },
  {
    id: '3',
    customerName: null, // Anonymous review
    rating: 5,
    message: 'Very professional service. They had all the medications I needed and the prices were competitive.',
    date: generateRecentDate(2), // 2 days ago
    medicationName: 'Amoxicillin 250mg',
    medicationId: 3,
    verified: false
  },
  {
    id: '4',
    customerName: 'Omar Mohamed',
    rating: 3,
    message: 'The service was okay, but the waiting time was a bit long. The medication quality is good though.',
    date: generateRecentDate(5), // 5 days ago
    medicationName: 'Insulin',
    medicationId: 4,
    orderId: 'ORD-2024-003',
    verified: true
  },
  {
    id: '5',
    customerName: 'Layla Ibrahim',
    rating: 5,
    message: 'Outstanding pharmacy! They always have what I need and the delivery is super fast. Highly recommended!',
    date: generateRecentDate(8), // 8 days ago (last week)
    medicationName: 'Blood Pressure Medication',
    medicationId: 5,
    orderId: 'ORD-2024-004',
    verified: true
  },
  {
    id: '6',
    customerName: 'Khalid Al-Ahmad',
    rating: 4,
    message: 'Great pharmacy with a good selection of medications. The pharmacist is very knowledgeable.',
    date: generateRecentDate(12), // 12 days ago
    medicationName: 'Antibiotic Cream',
    medicationId: 6,
    verified: true
  },
  {
    id: '7',
    customerName: null, // Anonymous review
    rating: 2,
    message: 'The medication was fine but the customer service could be improved. Had to wait too long.',
    date: generateRecentDate(15), // 15 days ago
    verified: false
  },
  {
    id: '8',
    customerName: 'Nour Hassan',
    rating: 5,
    message: 'Perfect experience! Quick service, fair prices, and very helpful staff. Will definitely come back.',
    date: generateRecentDate(20), // 20 days ago
    medicationName: 'Cough Syrup',
    medicationId: 7,
    orderId: 'ORD-2024-005',
    verified: true
  },
  {
    id: '9',
    customerName: 'Yusuf Ali',
    rating: 4,
    message: 'Good pharmacy overall. They have most medications in stock and the prices are reasonable.',
    date: generateRecentDate(25), // 25 days ago
    medicationName: 'Pain Relief Gel',
    medicationId: 8,
    verified: true
  },
  {
    id: '10',
    customerName: 'Maryam Khalil',
    rating: 5,
    message: 'Excellent service and very professional staff. They explained everything clearly and were very patient.',
    date: generateRecentDate(35), // 35 days ago (last month)
    medicationName: 'Allergy Medication',
    medicationId: 9,
    orderId: 'ORD-2024-006',
    verified: true
  },
  {
    id: '11',
    customerName: 'Hassan Ali',
    rating: 4,
    message: 'Good service and helpful staff. The pharmacy is well-organized and clean.',
    date: generateRecentDate(45), // 45 days ago
    medicationName: 'Vitamins',
    medicationId: 10,
    verified: true
  },
  {
    id: '12',
    customerName: 'Sara Ahmed',
    rating: 5,
    message: 'Amazing pharmacy! They always have everything in stock and the prices are fair.',
    date: generateRecentDate(60), // 60 days ago
    medicationName: 'Calcium Supplements',
    medicationId: 11,
    orderId: 'ORD-2024-007',
    verified: true
  }
];

// Calculate rating summary from mock data
const calculateRatingSummary = (reviews: Review[]): RatingSummary => {
  const starBreakdown = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
  let totalRating = 0;

  reviews.forEach(review => {
    starBreakdown[review.rating]++;
    totalRating += review.rating;
  });

  const averageRating = reviews.length > 0 ? totalRating / reviews.length : 0;

  return {
    averageRating,
    totalReviews: reviews.length,
    starBreakdown
  };
};

// Cache for reviews data
const reviewsDataCache = ref<{
  reviews: Review[];
  ratingSummary: RatingSummary;
} | null>(null);

// Simulate API delay
const simulateDelay = (ms: number = 1000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Reviews Service
 * Provides methods to fetch and manage customer reviews data
 */
export const reviewsService = {
  /**
   * Fetch all reviews with rating summary
   */
  async fetchReviews(): Promise<ReviewsResponse> {
    await simulateDelay(800);

    // Use cached data if available
    if (reviewsDataCache.value) {
      return {
        reviews: reviewsDataCache.value.reviews,
        ratingSummary: reviewsDataCache.value.ratingSummary,
        totalCount: reviewsDataCache.value.reviews.length,
        hasMore: false
      };
    }

    // Calculate rating summary
    const ratingSummary = calculateRatingSummary(mockReviews);

    // Cache the data
    reviewsDataCache.value = {
      reviews: mockReviews,
      ratingSummary
    };

    return {
      reviews: mockReviews,
      ratingSummary,
      totalCount: mockReviews.length,
      hasMore: false
    };
  },

  /**
   * Get rating summary only
   */
  async fetchRatingSummary(): Promise<RatingSummary> {
    await simulateDelay(500);
    return calculateRatingSummary(mockReviews);
  },

  /**
   * Clear cache (useful for refreshing data)
   */
  clearCache(): void {
    reviewsDataCache.value = null;
  }
};

export default reviewsService;
