/**
 * Store Service - Handles store-related API operations
 */

import { ref } from 'vue';
import type {
  Store,
  StoreInventoryItem,
  StoreSearch,
  StorePagination,
  StoreSort,
  StoreOrderRequest,
  StoreServiceResponse,
  StoresListResponse,
  StoreInventoryResponse
} from '../types/stores';

// Reactive state
const isLoading = ref(false);
const error = ref<string | null>(null);

// Cache for stores data
const storesCache = ref<Store[]>([]);
const lastFetchTime = ref<number>(0);
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get all partner stores with search and filtering
 */
export async function getStores(
  search?: StoreSearch,
  pagination?: Partial<StorePagination>,
  sort?: StoreSort
): Promise<StoreServiceResponse<StoresListResponse>> {
  // Check cache first
  const now = Date.now();
  if (storesCache.value.length > 0 && (now - lastFetchTime.value) < CACHE_DURATION) {
    const filteredStores = filterStores(storesCache.value, search);
    const paginatedResult = paginateStores(filteredStores, pagination);

    return {
      success: true,
      data: {
        stores: paginatedResult.stores,
        pagination: paginatedResult.pagination,
        filters: search?.filters || {}
      }
    };
  }

  isLoading.value = true;
  error.value = null;

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    const mockStores = getMockStores();

    // Update cache
    storesCache.value = mockStores;
    lastFetchTime.value = now;

    const filteredStores = filterStores(mockStores, search);
    const paginatedResult = paginateStores(filteredStores, pagination);

    return {
      success: true,
      data: {
        stores: paginatedResult.stores,
        pagination: paginatedResult.pagination,
        filters: search?.filters || {}
      }
    };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Failed to fetch stores';
    error.value = errorMessage;
    return { success: false, error: errorMessage };
  } finally {
    isLoading.value = false;
  }
}

/**
 * Get store inventory by store ID
 */
export async function getStoreInventory(
  storeId: string,
  search?: string,
  pagination?: Partial<StorePagination>
): Promise<StoreServiceResponse<StoreInventoryResponse>> {
  isLoading.value = true;
  error.value = null;

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800));

    const store = storesCache.value.find(s => s.id === storeId);
    if (!store) {
      throw new Error('Store not found');
    }

    const mockInventory = getMockStoreInventory(storeId);
    let filteredInventory = mockInventory;

    // Apply search filter
    if (search && search.trim()) {
      const searchLower = search.toLowerCase();
      filteredInventory = mockInventory.filter(item =>
        item.name.toLowerCase().includes(searchLower) ||
        item.category.toLowerCase().includes(searchLower) ||
        item.manufacturer?.toLowerCase().includes(searchLower)
      );
    }

    // Apply pagination
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedInventory = filteredInventory.slice(startIndex, endIndex);

    const paginationResult: StorePagination = {
      page,
      limit,
      total: filteredInventory.length,
      totalPages: Math.ceil(filteredInventory.length / limit)
    };

    return {
      success: true,
      data: {
        storeId,
        storeName: store.name,
        inventory: paginatedInventory,
        pagination: paginationResult
      }
    };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Failed to fetch store inventory';
    error.value = errorMessage;
    return { success: false, error: errorMessage };
  } finally {
    isLoading.value = false;
  }
}

/**
 * Send order request to a store
 */
export async function sendOrderRequest(
  orderRequest: StoreOrderRequest
): Promise<StoreServiceResponse<{ requestId: string }>> {
  isLoading.value = true;
  error.value = null;

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1200));

    const requestId = `REQ-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    return {
      success: true,
      data: { requestId },
      message: 'Order request sent successfully'
    };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Failed to send order request';
    error.value = errorMessage;
    return { success: false, error: errorMessage };
  } finally {
    isLoading.value = false;
  }
}

/**
 * Get loading state
 */
export function getStoreLoadingState() {
  return { isLoading, error };
}

/**
 * Clear stores cache
 */
export function clearStoresCache() {
  storesCache.value = [];
  lastFetchTime.value = 0;
}

// Helper functions
function filterStores(stores: Store[], search?: StoreSearch): Store[] {
  if (!search) return stores;

  let filtered = stores;

  // Apply text search
  if (search.query && search.query.trim()) {
    const query = search.query.toLowerCase();
    filtered = filtered.filter(store =>
      store.name.toLowerCase().includes(query) ||
      store.location.city.toLowerCase().includes(query) ||
      store.location.address.toLowerCase().includes(query)
    );
  }

  // Apply filters
  if (search.filters) {
    const { city, storeType, availability, openNow, deliverySupport } = search.filters;

    if (city && city !== 'all') {
      filtered = filtered.filter(store => store.location.city === city);
    }

    if (storeType && storeType !== 'all') {
      filtered = filtered.filter(store => store.storeType === storeType);
    }

    if (openNow !== undefined && openNow !== 'all') {
      filtered = filtered.filter(store => store.isOpen === openNow);
    }

    if (deliverySupport !== undefined && deliverySupport !== 'all') {
      filtered = filtered.filter(store => store.deliverySupport === deliverySupport);
    }
  }

  return filtered;
}

function paginateStores(stores: Store[], pagination?: Partial<StorePagination>) {
  const page = pagination?.page || 1;
  const limit = pagination?.limit || 12;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedStores = stores.slice(startIndex, endIndex);

  const paginationResult: StorePagination = {
    page,
    limit,
    total: stores.length,
    totalPages: Math.ceil(stores.length / limit)
  };

  return {
    stores: paginatedStores,
    pagination: paginationResult
  };
}

// Mock data generators
function getMockStores(): Store[] {
  const cities = ['Cairo', 'Alexandria', 'Giza', 'Shubra El Kheima', 'Port Said', 'Suez'];
  const storeTypes: ('main' | 'branch' | 'partner')[] = ['main', 'branch', 'partner'];

  return Array.from({ length: 48 }, (_, i) => {
    const id = `store-${i + 1}`;
    const city = cities[Math.floor(Math.random() * cities.length)];
    const storeType = storeTypes[Math.floor(Math.random() * storeTypes.length)];
    const isOpen = Math.random() > 0.3; // 70% chance of being open

    return {
      id,
      name: `${storeType === 'main' ? 'Dawinii Pharmacy' : storeType === 'branch' ? 'Dawinii Branch' : 'Partner Pharmacy'} ${i + 1}`,
      location: {
        address: `${Math.floor(Math.random() * 100) + 1} Main Street, ${city}`,
        city,
        region: `${city} Governorate`,
        coordinates: {
          latitude: 30.0444 + (Math.random() - 0.5) * 0.5,
          longitude: 31.2357 + (Math.random() - 0.5) * 0.5
        }
      },
      storeType,
      contact: {
        phone: `+20 1${Math.floor(Math.random() * 90000000) + 10000000}`,
        email: `store${i + 1}@dawinii.com`
      },
      workingHours: {
        open: '08:00',
        close: '22:00'
      },
      deliverySupport: Math.random() > 0.4, // 60% chance of delivery support
      isOpen,
      description: `Professional pharmacy services in ${city}`,
      verified: Math.random() > 0.1, // 90% verified
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date().toISOString()
    };
  });
}

function getMockStoreInventory(storeId: string): StoreInventoryItem[] {
  const medications = [
    'Paracetamol 500mg', 'Amoxicillin 250mg', 'Ibuprofen 400mg', 'Omeprazole 20mg',
    'Metformin 500mg', 'Aspirin 100mg', 'Vitamin D3 1000IU', 'Vitamin C 1000mg',
    'Cetirizine 10mg', 'Loratadine 10mg', 'Simvastatin 20mg', 'Amlodipine 5mg',
    'Atorvastatin 40mg', 'Lisinopril 10mg', 'Metoprolol 50mg', 'Furosemide 40mg',
    'Warfarin 5mg', 'Digoxin 0.25mg', 'Insulin Glargine', 'Insulin Aspart',
    'Levothyroxine 100mcg', 'Prednisone 20mg', 'Azithromycin 500mg', 'Ciprofloxacin 500mg',
    'Doxycycline 100mg', 'Fluconazole 150mg', 'Ranitidine 150mg', 'Lansoprazole 30mg',
    'Montelukast 10mg', 'Salbutamol Inhaler', 'Budesonide Inhaler', 'Fluticasone Nasal Spray'
  ];

  const categories = ['Pain Relief', 'Antibiotics', 'Vitamins', 'Digestive', 'Cardiovascular', 'Allergy', 'Diabetes', 'Respiratory', 'Dermatology', 'Neurology', 'Hormones', 'Anti-inflammatory'];
  const manufacturers = ['Pharma Co.', 'MediCorp', 'HealthLab', 'BioPharm', 'MedTech', 'GlobalMed', 'PharmaPlus', 'MedSolutions', 'LifeScience', 'CurePharma'];

  return Array.from({ length: Math.floor(Math.random() * 40) + 30 }, (_, i) => {
    const quantity = Math.floor(Math.random() * 200);
    let status: 'in-stock' | 'low-stock' | 'out-of-stock';

    if (quantity === 0) status = 'out-of-stock';
    else if (quantity < 20) status = 'low-stock';
    else status = 'in-stock';

    return {
      id: `${storeId}-med-${i + 1}`,
      medicationId: `med-${i + 1}`,
      name: medications[Math.floor(Math.random() * medications.length)],
      category: categories[Math.floor(Math.random() * categories.length)],
      quantity,
      pricePerUnit: Math.round((Math.random() * 50 + 5) * 100) / 100,
      expiryDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status,
      manufacturer: manufacturers[Math.floor(Math.random() * manufacturers.length)],
      batchNumber: `B${Math.floor(Math.random() * 10000) + 1000}`
    };
  });
}
