FROM node:18-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# Install missing type definitions
RUN yarn add --dev @types/leaflet @types/qrcode @types/node

# Copy source code and build
COPY . .
RUN yarn vite build

# Production stage
FROM nginx:alpine

# Copy built app
COPY --from=builder /app/dist /usr/share/nginx/html

# Simple nginx config
RUN echo 'server { \
    listen 80; \
    location / { \
        root /usr/share/nginx/html; \
        index index.html; \
        try_files $uri $uri/ /index.html; \
    } \
}' > /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
