/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Primary brand colors - Healthcare Teal/Emerald palette
        primary: {
          50: '#f0fdfa',
          100: '#ccfbf1',
          200: '#99f6e4',
          300: '#5eead4',
          400: '#2dd4bf',
          500: '#14b8a6',
          600: '#0d9488',
          700: '#0f766e',
          800: '#115e59',
          900: '#134e4a',
        },
        // Extended gray palette
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
        // Semantic colors - Healthcare optimized
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        info: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        // Modern dark mode colors - OpenAI inspired
        slate: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#333333',
          800: '#1f1f1f',
          900: '#121212',
        },
        // Additional neutral grays for modern dark mode
        neutral: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#737373',
          600: '#525252',
          700: '#404040',
          800: '#262626',
          850: '#1f1f1f',
          900: '#171717',
          950: '#0a0a0a',
        },
      },
      fontFamily: {
        sans: ['Inter', 'Tajawal', 'system-ui', 'sans-serif'],
        arabic: ['Tajawal', 'Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      animation: {
        'spin-slow': 'spin 2s linear infinite',
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(1rem)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideDown: {
          '0%': { opacity: '0', transform: 'translateY(-1rem)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
      },
      boxShadow: {
        'light': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'medium': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'large': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'dark-light': '0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)',
        'dark-medium': '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
        'dark-large': '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)',
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
    },
  },
  plugins: [
    // Add RTL support plugin
    function ({ addUtilities }) {
      const newUtilities = {
        '.rtl': {
          direction: 'rtl',
        },
        '.ltr': {
          direction: 'ltr',
        },
      }
      addUtilities(newUtilities)
    }
  ],
  safelist: [
    // Dynamic classes that might be used programmatically
    'inset-0',
    'bg-gray-100',
    'bg-gray-900',
    'bg-teal-50',
    'bg-white',
    'bg-primary-700',
    'text-white',
    'text-primary-700',
    'text-primary-600',
    'text-gray-600',
    'text-gray-700',
    'text-gray-800',
    'text-info-800',
    'text-error-600',
    'border-info-200',
    'border-primary-500',
    'border-b-primary-600',
    'border-b-primary-400',
    // Status colors
    'bg-success-50',
    'bg-warning-50',
    'bg-error-50',
    'text-success-600',
    'text-warning-600',
    'text-error-600',
    'border-success-200',
    'border-warning-200',
    'border-error-200',
    // Dark mode variants - modern neutral colors
    'dark:bg-slate-800',
    'dark:bg-slate-900',
    'dark:bg-neutral-850',
    'dark:bg-neutral-900',
    'dark:bg-neutral-800',
    'dark:bg-neutral-700',
    'dark:text-gray-100',
    'dark:text-gray-200',
    'dark:text-neutral-100',
    'dark:text-neutral-200',
    'dark:border-slate-700',
    'dark:border-slate-600',
    'dark:border-neutral-700',
    'dark:border-neutral-800',
    'dark:border-b-primary-400',
    // Skeleton loading colors
    'bg-gray-200',
    'bg-gray-50',
    // Healthcare teal colors
    'text-teal-600',
    'text-teal-400',
    'text-teal-700',
    'text-teal-800',
    'dark:text-teal-400',
    'dark:text-teal-300',
    'dark:text-teal-200',
    'bg-teal-50',
    'bg-teal-100',
    'bg-teal-900/20',
    'dark:bg-teal-900/20',
    'dark:bg-teal-900/30',
    'border-teal-200',
    'border-teal-700',
    'border-teal-800',
    'dark:border-teal-800',
    'hover:bg-teal-50',
    'hover:border-teal-500',
    'hover:text-teal-600',
    'dark:hover:text-teal-400',
    // Export button colors
    'bg-teal-600',
    'bg-teal-700',
    'hover:bg-teal-700',
    'hover:bg-teal-800',
    'focus:ring-teal-500',
    'from-teal-600',
    'to-teal-700',
    'from-teal-700',
    'to-teal-800',
  ],
}
